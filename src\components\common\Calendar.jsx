import React, { useState, useEffect } from "react";
import { useRouter } from "next/router";
import { usePathname } from "next/navigation";

const Calendar = ({ setOpenCalendar }) => {
  const router = useRouter();
  const pathname = usePathname();
  const [selectedDate, setSelectedDate] = useState(null);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [days, setDays] = useState([]);

  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  useEffect(() => {
    const initialSelectedDate = pathname.split("/")[3];
    const datePart = initialSelectedDate.split("-").slice(-3).join(" ");
    const formattedDate = new Date(datePart);
    renderCalendar(formattedDate);
  }, [currentDate]);

  const renderCalendar = (date) => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const tempDays = Array.from({ length: firstDay }, () => null).concat(
      Array.from({ length: daysInMonth }, (_, i) => i + 1)
    );
    const selected = new Date(year, month, day);
    setSelectedDate(selected);
    setDays(tempDays);
  };

  const onDateSelect = (day) => {
    if (day) {
      const year = currentDate.getFullYear();
      const month = currentDate.getMonth();
      const selected = new Date(year, month, day);

      setSelectedDate(selected);

      const options = {
        weekday: "long",
        month: "long",
        day: "numeric",
        year: "numeric",
      };
      const formattedDate = selected.toLocaleDateString("en-US", options);
      const slug = formattedDate
        .toLowerCase()
        .replace(/,/g, "") // Remove all commas
        .split(" ") // Split into parts
        .join("-"); // Join with dashes
      const getHoroscope = pathname.split("/")[3].split("-")[0];
      const url = `/astrology/daily-horoscope/${getHoroscope}-daily-horoscope-${slug}`;
      router.push(url);
      setOpenCalendar(false);
    }
  };

  const handleMonthChange = (e) => {
    setCurrentDate((prev) => new Date(prev.setMonth(+e.target.value)));
  };

  const handleYearChange = (e) => {
    setCurrentDate((prev) => new Date(prev.setFullYear(+e.target.value)));
  };

  const handlePrev = () => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev);
      newDate.setMonth(newDate.getMonth() - 1);
      if (newDate.getMonth() === 11 && prev.getMonth() === 0) {
        return prev;
      }
      return newDate;
    });
  };

const handleNext = () => {
  setCurrentDate((prev) => {
    const newDate = new Date(prev);
    newDate.setMonth(newDate.getMonth() + 1);
    const today = new Date();
    return newDate > today ? prev : newDate;
  });
};

const currentYear = currentDate.getFullYear();
const currentMonth = currentDate.getMonth();

return (
  <div className="calendar">
    <div className="calendar-header">
      <button
        onClick={handlePrev}
        style={{
          visibility:
            currentYear === 2025 && currentMonth === 0 ? "hidden" : "visible",
          color: "#000",
        }}
      >
        &#8249;
      </button>
      <span>
        <select value={currentMonth} onChange={handleMonthChange}>
          {months.map((month, index) => (
            <option key={index} value={index}>
              {month}
            </option>
          ))}
        </select>
        <select value={currentYear} onChange={handleYearChange}>
          {Array.from({ length: 6 }, (_, i) => 2025 + i).map((year) => (
            <option key={year} value={year}>
              {year}
            </option>
          ))}
        </select>
      </span>
      <button onClick={handleNext} style={{ color: "#000" }}>
        &#8250;
      </button>
    </div>
    <div className="calendar-days">
      {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
        <div key={day} className="day-label">
          {day}
        </div>
      ))}
    </div>
    <div className="calendar-grid">
      {days.map((day, index) => {
        const dateObj = new Date(currentYear, currentMonth, day);
        const today = new Date();
        const isPastOrToday = day && dateObj <= today;

        return (
          <div
            key={index}
            className={`day ${
              selectedDate?.getDate() === day &&
              selectedDate?.getMonth() === currentMonth &&
              selectedDate?.getFullYear() === currentYear
                ? "day-selected"
                : ""
            } ${isPastOrToday ? "dayHover" : "dayHoverDisabled"}`}
            onClick={() => isPastOrToday && onDateSelect(day)}
          >
            {day}
          </div>
        );
      })}
    </div>
  </div>
);
};

export default Calendar;
