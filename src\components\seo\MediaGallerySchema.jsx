import Head from "next/head";

const MediaGallerySchema = ({ title, description, data }) => {
  const associatedMedia = data?.map((item) => ({
    "@type": "ImageObject",
    name: item?.title || "",
    thumbnailUrl: item?.image || "",
  }));
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "MediaGallery",
    headline: title,
    description: description,
    mainEntityOfPage: {
      "@type": "ImageGallery",
      associatedMedia: associatedMedia,
    },
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
      ></script>
    </Head>
  );
};

export default MediaGallerySchema;
