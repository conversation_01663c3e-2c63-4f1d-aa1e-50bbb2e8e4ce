import React from "react";
import { menus } from "@/helper/MenuData";
import Head from "next/head";
import { CiFacebook } from "react-icons/ci";
import { ampNavbarCSS } from "./ampCss";

const AMPNavbar = () => {
	return (
		<>
			<style jsx>{ampNavbarCSS}</style>
			<Head>
				<script
					async
					custom-element="amp-sidebar"
					src="https://cdn.ampproject.org/v0/amp-sidebar-0.1.js"
				></script>
				<script
					async
					custom-element="amp-bind"
					src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"
				></script>
			</Head>

			<amp-state id="sidebarState">
				<script
					type="application/json"
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({ open: false }),
					}}
				/>
			</amp-state>

			<amp-state id="navState">
				<script
					type="application/json"
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({ mobmenu: null }),
					}}
				/>
			</amp-state>

			<div className="nav-rel">
				<amp-sidebar
					id="mobile-nav"
					layout="nodisplay"
					side="left"
					className="mob-menu"
					on="sidebarClose:AMP.setState({ sidebarState: { open: false } })"
				>
					<div style={{ overflowX: "auto", paddingBottom: "80px" }}>
						{menus.map((item, i) => (
							<div key={`mobile-menu-item-${i}`}>
								{item.submenus.length === 0 ? (
									<a
										href={item.link}
										className="mob-item"
										style={{ textDecoration: "none", color: "#212529" }}
									>
										{item.name}
									</a>
								) : (
									<div
										dangerouslySetInnerHTML={{
											__html: `<div
                        class="mob-item"
                        role="button"
                        tabindex="0"
                        [class]="navState.mobmenu == ${i} ? 'mob-item mob-open-0' : 'mob-item'"
                        on="tap:AMP.setState({ navState: { mobmenu: ${i} } })">
                        ${item.name}
                        <span class="chevron">&rsaquo;</span>
                      </div>`,
										}}
									/>
								)}
							</div>
						))}

						<div className="mbf">
							<div className="mob-follows">Follow us</div>
							<div className="mf-icons">
								<a href="https://facebook.com" target="_blank">
									<CiFacebook />
								</a>
								<a href="https://instagram.com" target="_blank">
									📷
								</a>
								<a href="https://twitter.com" target="_blank">
									🐦
								</a>
								<a href="https://youtube.com" target="_blank">
									📺
								</a>
							</div>
						</div>
					</div>

					<div
						dangerouslySetInnerHTML={{
							__html: `
                <div class="mob-submenu" [hidden]="navState.mobmenu == null">
                  <div className="mob-mbody">
                    <div
                      class="mbody-head"
                      role="button"
                      tabindex="1"
                      on="tap:AMP.setState({ navState: { mobmenu: null } })"
                    >
                      <span class="chevron">&lsaquo;</span>
                      <span
                        class="title"
                        [text]="'${menus
													.map((m) => m.name)
													.join("|")}'.split('|')[navState.mobmenu]"
                      ></span>
                    </div>
                    <div class="mob-body-dynamic">
                      ${menus
												.map((menu, index) => {
													if (!menu.submenus.length) return "";
													return `
                            <div [hidden]="navState.mobmenu != ${index}">
                              ${menu.submenus
																.map(
																	(sub) => `
                                    <a class="mob-item" href="${sub.link}" style="text-decoration:none; color:#212529;">
                                      ${sub.name}
                                    </a>`
																)
																.join("")}
                            </div>
                          `;
												})
												.join("")}
                    </div>
                  </div>
                  <div class="mbf">
                    <div class="mob-follows">Follow us</div>
                    <div class="mf-icons">
                      <a href="https://facebook.com" target="_blank">📘</a>
                      <a href="https://instagram.com" target="_blank">📷</a>
                      <a href="https://twitter.com" target="_blank">🐦</a>
                      <a href="https://youtube.com" target="_blank">📺</a>
                    </div>
                  </div>
                </div>
              `,
						}}
					/>
				</amp-sidebar>

				<div className="nav-wrapper">
					<a href="/" className="logo">
						Manifest
					</a>
					<div
						dangerouslySetInnerHTML={{
							__html: `
                <div
                  class="hamburger"
                  [class]="sidebarState.open ? 'hamburger is-active' : 'hamburger'"
                  role="button"
                  tabindex="2"
                  on="tap:mobile-nav.toggle,AMP.setState({ sidebarState: { open: !sidebarState.open } })"
                >
                  <span class="line"></span>
                  <span class="line"></span>
                  <span class="line"></span>
                </div>
              `,
						}}
					/>
				</div>
			</div>
		</>
	);
};

export default AMPNavbar;
