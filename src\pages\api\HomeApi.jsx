import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Home Data
export const getHome = async (body) => {
  const res = await fetch(Const.Link + "api/tenant/home", new Headers("GET"));
  return ProcessAPI(res);
};

// Get Flagged Pages
export const getFlaggedPage = async (slug) => {
  const res = await fetch(
    Const.Link + `api/tenant/get-flagged?slug=${slug}`,
    new Headers("GET")
  );
  return ProcessAPI(res);
};

export const addNewsLetterSubscription = async (body) => {
  const res = await fetch(
    Const.Link + `api/tenant/news-letter-subscription`,
    new Headers("POST", body)
  );
  return ProcessAPI(res);
};
