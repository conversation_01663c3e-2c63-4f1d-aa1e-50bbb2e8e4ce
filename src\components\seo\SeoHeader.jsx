import React from "react";
import Head from "next/head";
import { useRouter } from "next/router";
import { Const } from "@/utils/Constants";
import WebPageSchema from "@/components/seo/WebPageSchema";
import NewsMediaOrganizationSchema from "@/components/seo/NewsMediaOrganizationSchema";
import SiteNavigationSchema from "@/components/seo/SiteNavigationSchema";

const SeoHeader = ({ meta = {}, type = "website" }) => {
  const router = useRouter();
  const canonical = `${Const.ClientLink}/${router.asPath?.slice(1)}`;
  const Logo = `${Const.ClientLink}/logo svg.svg`;
  return (
    <Head>
      <title>{meta?.title || ""}</title>
      <meta name="description" content={meta?.description || ""} />
      <meta name="keywords" content={meta?.keywords || ""} />
      {meta?.author && <meta name="author" content={meta?.author || ""} />}
      <meta name="publisher" content={Const.Brand} />
      <meta
        name="robots"
        content={
          `${meta?.robots}, max-image-preview:large` ||
          "noindex,nofollow, max-image-preview:large"
        }
      />
      <meta name="google-adsense-account" content="ca-pub-****************" />
      <link rel="canonical" href={meta?.canonical || canonical} />
      {/* OG Tags */}
      <meta property="og:locale" content="en_US" />
      <meta property="og:type" content={type} />
      <meta property="og:title" content={meta?.og?.title || ""} />
      <meta property="og:description" content={meta?.og?.description || ""} />
      <meta property="og:url" content={meta?.canonical || canonical} />
      <meta property="og:site_name" content={Const.Brand} />
      <meta property="og:image" content={meta?.og?.image || Logo} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      {/* Twitter Tag */}
      <meta
        name="twitter:card"
        content={meta?.twitter?.card || "summary_large_image"}
      />
      <meta
        name="twitter:title"
        content={meta?.twitter?.title || meta?.title}
      />
      <meta
        name="twitter:description"
        content={meta?.twitter?.description || meta?.description}
      />
      <meta name="twitter:site" content={"@_manifestind"} />
      <meta name="twitter:image" content={meta?.twitter?.image || Logo} />
      <meta name="twitter:creator" content={"@_manifestind"} />
      <meta charset="UTF-8" />
      <meta http-equiv="Content-Type" content="text/html;charset=UTF-8" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <link
        rel="icon"
        type="image/png"
        sizes="16x16"
        href="/manifest_favicon.png"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="32x32"
        href="/manifest_favicon.png"
      />
      <link
        rel="icon"
        type="image/png"
        sizes="192x192"
        href="/manifest_favicon.png"
      />
      <link rel="apple-touch-icon" href="/manifest_favicon.png" />
      <link
        rel="alternate"
        hrefLang="en-in"
        href={meta?.canonical || canonical}
      />
      <WebPageSchema
        name={meta?.title || ""}
        description={meta?.description || ""}
        url={meta?.canonical || canonical}
      />
      <NewsMediaOrganizationSchema
        clientLink={Const.ClientLink}
        logoUrl={Logo}
        address={{
          streetAddress:
            "Business Media Pvt Ltd, Thapar House, Central Wing, 3rd floor, Janpath Lane",
          addressLocality: "New Delhi",
          addressRegion: "India",
          postalCode: "110 001",
        }}
        contact={{
          telephone: "+91–11–23486700",
          contactType: "Customer Service",
          areaServed: "IN",
          availableLanguage: "English",
          hoursAvailable: {
            opens: "09:00",
            closes: "19:00",
          },
        }}
        sameAs={[
          "https://www.facebook.com/",
          "https://www.instagram.com/manifest.ind/",
          "https://x.com/_manifestind",
        ]}
      />
      <SiteNavigationSchema />
    </Head>
  );
};

export default SeoHeader;
