
.header_wrapper2 {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  position: fixed;
  z-index: 10;
  background-color: #faf7f3;
  font-family: var(--primary-font-scotch-display);
  width: 100%;

  z-index: 999;
}
.header_wrapper2 .container{
  padding-bottom: 1.4rem;
}

.Navbar_wrapper2 {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  padding: 1rem 0;
  height: 3rem;
}
.Navbar_logo2 {
  position: relative;
  z-index: 8;
  /* height: 0; */
}
.Navbar_logo2 img {
  filter: invert(1);
  height: 42px;
}
.header_right_part2 {
  position: relative;
  /* background-color: green; */
  /* height: auto; */
  display: flex;
  justify-content: flex-end;
}

.nav_hamburger2 {
  display: block;
  font-size: 2rem;
}
.header_right_part2 svg {
  color: #000;
  cursor: pointer;
}
.header_right_part2 nav {
  position: relative;
  /* text-align: right; */
  /* display: none; */
  /* align-items: center; */
  /* flex-direction: column; */
  height: 90%;
  width: 100%;
  margin-top: 0.5rem;

  /* background-color: rgb(13, 0, 128); */
}
.header_right_part2 nav ul {
  list-style: none;
  display: flex;
  width: 100%;
  justify-content: flex-end;
  gap: 1.3rem;
  /* display: none; */
}
.header_right_part2 nav ul li {
  /* margin: 0 1rem; */
  /* background-color: pink; */
  pointer-events: all;
  font-size: 1.1rem;
  white-space: nowrap;
  font-weight: 500;
  /* transition: all 0.5s ease; */
}
.header_right_part2 nav ul li a {
  color: gray;
  /* transition: all 0.3s ease; */
}
.header_right_part2 nav ul li a:hover {
  color: black;
}
/* .header_right_part2 nav ul li:hover {
  transform: translateY(-10px);
} */
.nav_active {
  color: rgba(0, 0, 0, 1) !important;
}
.nav_hamburger2 nav {
  /* display: none; */
}
.Plus_btn2 {
  text-align: right;
  cursor: pointer;
  /* font-weight: 100; */
}

.Navbar_logo2 h3 {
  position: absolute;
  font-family: var(--primary-font-Neue);
  font-weight: 400;
  /* display: none; */
  opacity: 0;
  margin-top: 1rem;
  color: #000;
}
.shopImgnav2 {
  /* display: none; */
  height: 200px;
  position: absolute;
  right: 0;
  z-index: 9;
  right: 0;
    top: 50%;
    transform: translate(0%, -50%);
    transition: opacity 0.3s ease;
    opacity: 0;
    pointer-events: none;
}
.shopImgnav2 img {
  height: 190px;
  width: 130px;
}
/* .tp_button.tp_button_Imgnav {
  margin: 5px;
} */
 .Navbar_wrapper2{
  display: none;
 }
 .Navbar_logo2mbl img{
  height: 42px;
  filter: invert(1);
 }
@media only screen and (min-width: 1024px) {
  .Navbar_wrapper2{
    display: grid;
   }
  /* .header_right_part2 {
    align-items: center;
  } */
  .header_right_part2 nav {
    display: flex;
    /* height: 0; */
  }
  .nav_hamburger2 {
    display: none;
  }
  .Navbar_wrapper2 {
    height: 4rem;
  }
  .Navbar_logo2 img {
    height: 63px;
    /* transition: all 0.5s ease; */
  }
  /* .header_right_part2 {
    display: block;
  }
  .Navbar_wrapper2 {
    padding: 1.3rem 0rem;
  }
  .Navbar_logo2 img {
    height: 80%;
  }
  .nav_hamburger2 nav {
    display: block;
  }
  .nav_hamburger2 nav ul {
    display: initial;
  }
  .nav_hamburger2 nav ul li {
    padding: 0.2rem 0;
    font-size: 1.2rem;
  }
  .nav_hamburger2 {
    display: none;
  } */
}

@media only screen and (min-width: 1300px) {
  /* .nav_hamburger2 nav ul li {
    font-size: 1.4rem;
  }
  .Navbar_logo2 img {
    height: 100%;
  } */
  .header_right_part2 nav ul li {
    font-size: 1.4rem;
  }
  .Navbar_logo2 h3 {
    font-size: 1.5rem;
  }
}
