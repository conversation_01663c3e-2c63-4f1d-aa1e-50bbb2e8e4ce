import Link from "next/link";
import React, { useEffect, useRef, useState, useCallback } from "react";

const WebStories = ({ data, seeAll }) => {
  const containerRef = useRef(null);
  const scrollLeftButtonRef = useRef(null);
  const scrollRightButtonRef = useRef(null);
  const [isAtStart, setIsAtStart] = useState(true);
  const [isAtEnd, setIsAtEnd] = useState(false);

  // Update arrow visibility based on scroll position
  const updateArrowVisibility = useCallback(() => {
    if (containerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = containerRef.current;
      setIsAtStart(scrollLeft === 0);
      setIsAtEnd(scrollLeft + clientWidth >= scrollWidth);
    }
  }, []);

  // Scroll handlers
  const scrollBy = useCallback((offset) => {
    if (containerRef.current) {
      containerRef.current.scrollBy({
        left: offset,
        behavior: "smooth",
      });
    }
  }, []);

  useEffect(() => {
    if (window.innerWidth >= 768) {
      const handleScroll = () => updateArrowVisibility();

      const container = containerRef.current;
      const scrollLeftButton = scrollLeftButtonRef.current;
      const scrollRightButton = scrollRightButtonRef.current;

      if (container) {
        container.addEventListener("scroll", handleScroll, { passive: true });
      }
      if (scrollLeftButton) {
        scrollLeftButton.addEventListener("click", () => scrollBy(-200));
      }
      if (scrollRightButton) {
        scrollRightButton.addEventListener("click", () => scrollBy(200));
      }

      // Initial check for arrow visibility
      updateArrowVisibility();

      return () => {
        if (container) {
          container.removeEventListener("scroll", handleScroll);
        }
        if (scrollLeftButton) {
          scrollLeftButton.removeEventListener("click", () => scrollBy(-200));
        }
        if (scrollRightButton) {
          scrollRightButton.removeEventListener("click", () => scrollBy(200));
        }
      };
    }
  }, [updateArrowVisibility, scrollBy]);

  return (
    <div className="stories">
      {/* <div className="container"> */}
      <div className="row">
        <div className="columns small-12 stories__column">
          <div className="glide glide--ltr glide--slider glide--swipeable">
            <div className="glide__track">
              <ul
                className="glide__slides"
                ref={containerRef}
                // style={{ overflowX: "auto", whiteSpace: "nowrap" }}
              >
                {" "}
                {data.map((item, index) => {
                  return (
                    <li
                      key={`home-webstories-${index}`}
                      className="glide__slide glide__slide--active"
                    >
                      <Link href={item?.slug ?? "#"}>
                        <div className="stories__story">
                          <div className="stories__img">
                            <img
                              src={item?.coverImg ?? ""}
                              alt={item?.altName ?? ""}
                            />
                          </div>
                          <h5 className="marque long-text">
                            <span>{item?.title ?? ""}</span>
                          </h5>
                        </div>
                      </Link>
                    </li>
                  );
                })}
                <li className="glide__slide glide__slide--active">
                  <Link href={seeAll ?? "#"}>
                    <div className="stories__story">
                      <div className="stories__img">
                        <h5 className="marque long-text">
                          <span>See More</span>
                        </h5>
                      </div>
                    </div>
                  </Link>
                </li>
              </ul>
            </div>
            <div className="controls">
              <div
                className="left-button_btn_story"
                style={{ opacity: isAtStart ? 0 : 1 }}
                disabled={isAtStart}
              >
                <img
                  ref={scrollLeftButtonRef}
                  src="/Assets/manifest-logo-wedding-09.png"
                  alt=""
                />
              </div>
              <div
                className="right-button_btn_story"
                ref={scrollRightButtonRef}
                style={{ opacity: isAtEnd ? 0 : 1 }}
                disabled={isAtEnd}
              >
                <img src="/Assets/manifest-logo-wedding-09.png" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* </div> */}
    </div>
  );
};

export default WebStories;
