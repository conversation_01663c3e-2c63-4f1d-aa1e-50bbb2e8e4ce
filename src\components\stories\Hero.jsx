import React from "react";
import Breadcrumb from "@/components/stories/Breadcrumb";
import Link from "next/link";
import { IoMdShare } from "react-icons/io";
import { FiBookmark } from "react-icons/fi";
import { dateFormateWithTimeShort } from "@/utils/Util";

const Hero = ({
  title,
  excerpt,
  author,
  timestamp,
  breadcrumbs,
  setOpenShare,
  contributor = [],
}) => {
  return (
    <>
      <div className="story-top">
        <Breadcrumb data={breadcrumbs} />
        <div className="contr-fluid">
          <div className="contr-fluid_inner">
            <h1>{title ?? ""}</h1>
            <div className="para-ButFlex">
              <p>{excerpt ?? ""}</p>
              {/* <div className="sound-share-back">
                  <Button>Next</Button>
                  
                </div> */}
            </div>
          </div>
          <div className="detailed-media_info">
            <div className="card-meta_meta">
              <div className="card-meta_meta_inner">
                {breadcrumbs &&
                  breadcrumbs[1].slug !== "/astrology/daily-horoscope" && (
                    <div className="card-meta_item">
                      <span className="card-meta_label text-7">
                        {dateFormateWithTimeShort(timestamp ?? "")}
                      </span>
                    </div>
                  )}
                {author?.length > 0 && (
                  <div className="card-meta_item">
                    <span className="card-meta_label text-7">
                      By{" "}
                      {author.map((auth, index) => (
                        <React.Fragment key={auth.slug}>
                          <Link href={auth.slug} className="author-link">
                            {auth.name}
                          </Link>
                          {index < author.length - 1 && ", "}
                        </React.Fragment>
                      ))}
                      {contributor?.length > 0 && ", "}
                    </span>
                  </div>
                )}
                {contributor?.length > 0 && (
                  <div className="card-meta_item">
                    <span className="card-meta_label text-7">
                      {!author?.length && "By "}
                      {contributor.join(", ")}
                    </span>
                  </div>
                )}
              </div>
              <div className="card-actions_actions">
                <li>
                  <button
                    className="rounded_btns"
                    aria-label=""
                    onClick={() => setOpenShare(true)}
                  >
                    <IoMdShare />
                    <span>Share</span>
                  </button>
                </li>
                {/* <li>
                  <button className="rounded_btns" aria-label="">
                    <FiBookmark />
                  </button>
                </li> */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default Hero;
