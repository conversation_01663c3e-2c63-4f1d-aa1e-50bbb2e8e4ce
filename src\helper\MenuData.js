export const menus = [
  {
    name: "Sanskaar 101",
    link: "/sanskaar",
    submenus: [
      {
        name: "Rituals & Traditions",
        link: "/sanskaar/rituals-traditional",
      },
      {
        name: "Old Wives’ Tales",
        link: "/sanskaar/old-wives-tales",
      },
    ],
  },
  {
    name: "Spotlight",
    link: "/spotlight",
    submenus: [
      {
        name: "Little Black Book",
        link: "/spotlight/little-black-book",
      },
      {
        name: "On The Radar",
        link: "/spotlight/on-the-radar",
      },
    ],
  },
  {
    name: "Wedding Diaries",
    link: "/wedding-diaries",
    submenus: [
      {
        name: "Celebrities",
        link: "/wedding-diaries/celebrities",
      },
      {
        name: "Real Weddings",
        link: "/wedding-diaries/real-weddings",
      },
    ],
  },
  {
    name: "Fashion & Beauty",
    link: "/fashion-beauty",
    submenus: [
      {
        name: "Celebrity Style",
        link: "/fashion-beauty/celebrity-style",
      },
      {
        name: "Features",
        link: "/fashion-beauty/features",
      },
      {
        name: "Jewellery",
        link: "/fashion-beauty/jewellery",
      },
      {
        name: "Skincare",
        link: "/fashion-beauty/skincare",
      },
      {
        name: "Make-Up",
        link: "/fashion-beauty/make-up",
      },
      {
        name: "Hair",
        link: "/fashion-beauty/hair",
      },
      {
        name: "Shopping",
        link: "/fashion-beauty/shopping",
      },
    ],
  },
  {
    name: "Trends & Culture",
    link: "/trends-culture",
    submenus: [
      {
        name: "Zeitgeist",
        link: "/trends-culture/zeitgeist",
      },
      {
        name: "Moodboard",
        link: "/trends-culture/moodboard",
      },
      {
        name: "Gifting",
        link: "/trends-culture/gifting",
      },
      {
        name: "Relationships",
        link: "/trends-culture/relationships",
      },
      {
        name: "Health & Wellness",
        link: "/trends-culture/health-wellness",
      },
      {
        name: "Travel",
        link: "/trends-culture/travel",
      },
      {
        name: "Food",
        link: "/trends-culture/food",
      },
    ],
  },
  {
    name: "Astrology",
    link: "/astrology",
    submenus: [
      {
        name: "Daily Horoscope",
        link: "/astrology",
      },
      {
        name: "In The Stars",
        link: "/astrology",
      },
    ],
  },
];
