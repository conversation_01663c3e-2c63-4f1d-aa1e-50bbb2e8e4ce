import React from "react";
// import Navbar from "../common/Navbar";
import Story from "./WebStories";
import Link from "next/link";
import YellowBgSection from "../sections/SwiperCardSection";
import VideoCards from "./VideoSection";
import Celebrities from "./GlimpseSection";
import Celebrities2 from "./Celebrities2";
import Banner from "./Banner";
import { banner1Data } from "@/helper/Data";
import { banner2Data } from "@/helper/Data";
import FollowUp from "../sections/HighlightSection";
import NewsContact from "../common/NewsContact";
import TransparentSection from "../common/TransparentSection";

const Hero = () => {
  return (
    <div className="hero_sec">
      <Story />
      <TransparentSection />
      <Banner
        title={banner1Data.title}
        image={banner1Data.coverImg}
        altName={banner1Data.altName}
        excerpt={banner1Data.excerpt}
        author={banner1Data.author}
        slug={banner1Data.slug}
        category={banner1Data.category}
        timestamp={banner1Data.timestamp}
      />
      <YellowBgSection />
      <VideoCards />
      <Celebrities />
      <Banner
        title={banner2Data.title}
        image={banner2Data.coverImg}
        altName={banner2Data.altName}
        excerpt={banner2Data.excerpt}
        author={banner2Data.author}
        slug={banner2Data.slug}
        category={banner2Data.category}
        timestamp={banner2Data.timestamp}
      />
      <FollowUp />
      <Celebrities2 />
      <NewsContact />
    </div>
  );
};

export default Hero;
