import React, { useState } from "react";
import { usePathname } from "next/navigation";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import NewsArticleSchema from "@/components/seo/NewsArticleSchema";
import <PERSON> from "@/components/stories/Hero";
import Banner from "@/components/stories/Banner";
import ContentWrapper from "@/components/stories/ContentWrapper";
import SwiperCardSection from "@/components/sections/SwiperCardSection";
import ShareModal from "@/components/common/ShareModal";
import TransparentSection from "@/components/common/TransparentSection";
import { Const } from "@/utils/Constants";
import {
  getLatestStories,
  getStories,
  getStoriesAuthor,
} from "@/pages/api/ArticleApi";
import { Ads } from "@/components/ads/Ads";
import { useContinuousScroll } from "@/hooks/useContinuousScroll";
import NextStorySeparator from "@/components/stories/NextStorySeparator";

const Stories = ({
  _id,
  data,
  breadcrumbs,
  author,
  tag,
  latest,
  meta,
  slug,
}) => {
  const [openShare, setOpenShare] = useState(false);
  const pathname = usePathname();
  const router = pathname.split("/");
  const { stories, visibleSlug, registerStoryRef } = useContinuousScroll({
    _id,
    data,
    author,
    breadcrumbs,
    latest,
    tag,
    meta,
    slug,
  });

  return (
    <>
      {stories.map(
        ({ _id, data, author, breadcrumbs, latest, tag, meta, slug }) => {
          // Detect if this is a promotional article
          const promotional = data?.isPromotional || false;
          const content = data && data.content ? JSON.parse(data.content) : "";

          return (
            <div
              key={_id}
              className={`story-container `}
              ref={(el) => registerStoryRef(el, _id, slug)}
              data-current={slug === visibleSlug ? "true" : "false"}
              data-promotional={promotional ? "true" : "false"}
              islast={
                _id.toString() === stories[stories.length - 1]?._id.toString()
                  ? "true"
                  : "false"
              }
            >
              {slug === visibleSlug && (
                <>
                  <SeoHeader meta={meta} type="article" />
                  <BreadcrumbSchema itemList={breadcrumbs ?? []} />
                  <NewsArticleSchema
                    headline={meta?.title || ""}
                    datePublished={data?.timestamp || ""}
                    dateModified={data?.updatedAt || ""}
                    articleSection={breadcrumbs?.[0]?.name || ""}
                    keywords={meta?.keywords || []}
                    description={meta?.description || ""}
                    url={pathname}
                    content={content || {}}
                    author={author?.[0] || {}}
                    image={data?.coverImg || ""}
                  />
                </>
              )}

              <div className="Stories_wrapper">
                <div className="container">
                  <Ads
                    id={`div-gpt-ad-stories-mobile-top-${_id}}`}
                    style={{ margin: "30px auto 0px" }}
                    adUnits={[
                      {
                        adUnit: "/23290324739/Manifest-Mobile-Top-320",
                        sizes: [
                          [320, 50],
                          [320, 100],
                        ],
                        sizeMapping: [
                          {
                            viewport: [1023, 0],
                            sizes: [
                              [320, 50],
                              [320, 100],
                            ],
                          },
                        ],
                        minWidth: 768,
                        maxWidth: 1023,
                      },
                      {
                        adUnit: "/23290324739/Manifest-Mobile-Top-320",
                        sizes: [
                          [320, 50],
                          [320, 100],
                        ],
                        sizeMapping: [
                          {
                            viewport: [767, 0],
                            sizes: [
                              [320, 50],
                              [320, 100],
                            ],
                          },
                        ],
                        minWidth: 0,
                        maxWidth: 767,
                      },
                    ]}
                    targeting={{
                      section: [router?.[1] || null],
                      "sub-section": [router?.[2] || null],
                    }}
                  />
                  <Hero
                    title={data?.title ?? ""}
                    excerpt={data?.excerpt ?? ""}
                    author={author ?? []}
                    timestamp={data?.updatedAt ?? ""}
                    breadcrumbs={breadcrumbs ?? []}
                    setOpenShare={setOpenShare}
                    contributor={data?.contributor ?? []}
                  />
                  <div className="Stories_wrapper_inner">
                    <Ads
                      id={`div-gpt-ad-stories-top-${_id}`}
                      style={{ margin: "30px auto" }}
                      adUnits={[
                        {
                          adUnit: "/23290324739/Manifest-Desktop-TOP-970",
                          sizes: [[970, 90]],
                          sizeMapping: [
                            {
                              viewport: [0, 0],
                              sizes: [[970, 90]],
                            },
                          ],
                          minWidth: 1024,
                          maxWidth: Infinity,
                        },
                        {
                          adUnit: "/23290324739/Manifest-Mobile-Top-300",
                          sizes: [[300, 100]],
                          sizeMapping: [
                            {
                              viewport: [1023, 0],
                              sizes: [[300, 100]],
                            },
                          ],
                          minWidth: 768,
                          maxWidth: 1023,
                        },
                        {
                          adUnit: "/23290324739/Manifest-Mobile-Top-300",
                          sizes: [[300, 100]],
                          sizeMapping: [
                            {
                              viewport: [767, 0],
                              sizes: [[300, 100]],
                            },
                          ],
                          minWidth: 0,
                          maxWidth: 767,
                        },
                      ]}
                      targeting={{
                        section: [router?.[1] || null],
                        "sub-section": [router?.[2] || null],
                      }}
                    />
                    <Banner
                      image={data?.coverImg ?? ""}
                      altName={data?.altName ?? ""}
                      caption={data?.caption ? data?.caption : ""}
                      courtesy={data?.courtesy ? data?.courtesy : ""}
                      removeClassName={"gradient_remove_bg"}
                    />
                    <ContentWrapper
                      content={content ?? ""}
                      tag={tag ?? []}
                      isAdsActive={true}
                    />
                    <Ads
                      id={`div-gpt-ad-stories-bottom-${_id}`}
                      style={{ margin: "50px auto 0px" }}
                      adUnits={[
                        {
                          adUnit: "/23290324739/Manifest-Desktop-Bottom-300",
                          sizes: [[300, 250]],
                          sizeMapping: [
                            {
                              viewport: [0, 0],
                              sizes: [[300, 250]],
                            },
                          ],
                          minWidth: 1024,
                          maxWidth: Infinity,
                        },
                        {
                          adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                          sizes: [
                            [300, 100],
                            [300, 250],
                          ],
                          sizeMapping: [
                            {
                              viewport: [1023, 0],
                              sizes: [
                                [300, 100],
                                [300, 250],
                              ],
                            },
                          ],
                          minWidth: 768,
                          maxWidth: 1023,
                        },
                        {
                          adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                          sizes: [
                            [300, 100],
                            [300, 250],
                          ],
                          sizeMapping: [
                            {
                              viewport: [767, 0],
                              sizes: [
                                [300, 100],
                                [300, 250],
                              ],
                            },
                          ],
                          minWidth: 0,
                          maxWidth: 767,
                        },
                      ]}
                      targeting={{
                        section: [router?.[1] || null],
                        "sub-section": [router?.[2] || null],
                      }}
                    />
                    {_id.toString() !==
                      stories[stories.length - 1]?._id.toString() && (
                      <NextStorySeparator
                        containerStyle={{
                          marginBottom: "80px 0px",
                          backgroundColor: " #faf7f3",
                        }}
                      />
                    )}
                    {/* <SwiperCardSection title="Keep Exploring" data={latest} /> */}
                  </div>
                </div>
              </div>
            </div>
          );
        }
      )}
      <ShareModal show={openShare} setShow={setOpenShare} />
      <TransparentSection />
    </>
  );
};

export default Stories;

export async function getServerSideProps(context) {
  const { res } = context;
  const { category, subcategory, stories } = context.params;
  const url = `/${category}/${subcategory}/${stories}`;
  const LIMIT = 12;
  const storiesUrl = `/${stories}`;
  const latestStoriesPayload = {
    slug: url,
    limit: LIMIT,
  };
  try {
    const storiesRes = await getStories(url);
    const authorRes = await getStoriesAuthor(storiesUrl);
    const latestStoriesRes = await getLatestStories(latestStoriesPayload);
    if (storiesRes?.data?.isURLCorrect === false) {
      res.writeHead(301, { Location: storiesRes?.data?.correctUrl });
      res.end();
    }
    if (
      !storiesRes ||
      storiesRes.statusCode === Const.NotFound404 ||
      Object.keys(storiesRes.data.articleData).length === 0
    ) {
      return {
        notFound: true,
      };
    }

    return {
      props: {
        _id: storiesRes?.data?.articleData?._id ?? "",
        data: storiesRes?.data?.articleData?.data ?? {},
        template: storiesRes?.data?.articleData?.data?.template ?? 0,
        breadcrumbs: storiesRes?.data?.articleData?.breadcrumbs ?? [],
        author:
          authorRes?.data && authorRes?.data?.length > 0 ? authorRes?.data : [],
        tag: storiesRes?.data?.articleData?.tag ?? [],
        latest: latestStoriesRes?.data ?? [],
        meta: storiesRes?.data?.articleData?.meta ?? {},
        slug: `/${category}/${subcategory}/${stories}`,
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      notFound: true,
    };
  }
}
