import React, { useEffect, useState } from "react";
import { useRouter } from "next/router";
import SeoHeader from "@/components/seo/SeoHeader";
import Heading from "@/components/tag/Heading";
import GridSection from "@/components/tag/GridSection";
import TransparentSection from "@/components/common/TransparentSection";
import { getTag } from "@/pages/api/TagApi";
import { Const } from "@/utils/Constants";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";

const Tag = ({ title, initialData, initialCount, meta }) => {
  const Limit = 8;
  const router = useRouter();
  const [data, setData] = useState(initialData);
  const [count, setCount] = useState(initialCount);
  const [offset, setOffset] = useState(Const.Offset);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    setData(initialData);
    setCount(initialCount);
    setOffset(Const.Offset);
    setHasMore(initialData.length < initialCount);
  }, [router.query.slug, initialData, initialCount]);

  const handleShowMore = async () => {
    const newOffset = offset + Limit;
    setOffset(newOffset);

    const payload = {
      slug: `/${router.query.slug}`,
      limit: Limit,
      offset: newOffset,
    };

    try {
      const response = await getTag(payload);
      const newItems = response?.data?.data ?? [];
      const totalItems = response?.data?.count ?? 0;

      setData((prev) => [...prev, ...newItems]);
      setOffset(newOffset);
      setCount(totalItems);

      if (newItems.length === 0 || newOffset + Limit >= totalItems) {
        setHasMore(false);
      }
    } catch (e) {
      console.error("Error fetching more tag:", e);
    }
  };
  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div className="tagsWrapper">
        <div className="container">
          <Heading title={title} />
          <GridSection
            id="tags-grid-section"
            data={data}
            hasMore={hasMore}
            handleShowMore={handleShowMore}
          />
        </div>
      </div>
      <TransparentSection />
    </>
  );
};

export default Tag;

export async function getServerSideProps(context) {
  const url = `/${context.params.slug}`;
  const Limit = 8;
  const payload = {
    slug: url,
    limit: Limit,
    offset: Const.Offset,
  };

  try {
    const tagRes = await getTag(payload);
    if (tagRes.status !== "success" || tagRes.data.data === 0) {
      return {
        notFound: true,
      };
    }
    return {
      props: {
        title: tagRes?.data?.title ?? "",
        initialData: tagRes?.data?.data ?? [],
        initialCount: tagRes?.data?.count ?? 0,
        meta: tagRes?.data?.meta ?? {},
      },
    };
  } catch (error) {
    console.error("Error fetching category data:", error);
    return { notFound: true };
  }
}
