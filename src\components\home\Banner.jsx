import React from "react";
import Link from "next/link";
import Image from "next/image";
import { getAuthorText } from "@/utils/Util";

const Banner = ({ data }) => {
  if (!data || data.length === 0) return null;
  const bannerData = data && data.length > 0 ? data[0] : {};

  return (
    // <div className="container">
    <div className="big-container-blog_wrapper">
      <div className="big-container-blog">
        <Link href={bannerData?.slug ?? "#"} className="">
          <div className="blog-img-container">
            <Image
              src={bannerData?.coverImg ? bannerData?.coverImg : ""}
              alt={bannerData?.altName ?? ""}
              fill
            />
          </div>
          <div className="blog-banner-text">
            <strong>{bannerData?.category}</strong>
            <h2 className="slug">{bannerData?.title}</h2>
            <p className="blog-banner-text-desc">{bannerData?.excerpt}</p>
            <span className="date-author">
              {getAuthorText("By", bannerData.author, bannerData.contributor)}
            </span>
          </div>
        </Link>
      </div>
    </div>
    // </div>
  );
};

export default Banner;
