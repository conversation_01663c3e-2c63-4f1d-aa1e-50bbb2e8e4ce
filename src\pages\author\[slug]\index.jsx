import React, { useEffect, useState } from "react";
import gsap from "gsap";
import { useRouter } from "next/router";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import InfoSection from "@/components/author/InfoSection";
import GridSection from "@/components/author/GridSection";
import TransparentSection from "@/components/common/TransparentSection";
import ScrollToPlugin from "gsap/dist/ScrollToPlugin";
import { getAuthor, getAuthorStories } from "@/pages/api/AuthorApi";
import { Const } from "@/utils/Constants";

gsap.registerPlugin(ScrollToPlugin);
const Author = ({ meta, info, initialData, initialCount }) => {
  const Limit = 8;
  const router = useRouter();
  const [data, setData] = useState(initialData);
  const [count, setCount] = useState(initialCount);
  const [offset, setOffset] = useState(Const.Offset);
  const [hasMore, setHasMore] = useState(true);

  useEffect(() => {
    setData(initialData);
    setCount(initialCount);
    setOffset(Const.Offset);
    setHasMore(initialData.length < initialCount);
  }, [router.query.slug, initialData, initialCount]);

  const handleShowMore = async () => {
    const newOffset = offset + Limit;
    setOffset(newOffset);

    const payload = {
      slug: `/${router.query.slug}`,
      limit: Limit,
      offset: newOffset,
    };

    try {
      const response = await getAuthorStories(payload);
      const newItems = response?.data ?? [];
      const totalItems = response?.totalCounts ?? 0;

      setData((prev) => [...prev, ...newItems]);
      setOffset(newOffset);
      setCount(totalItems);

      // Determine if there is more data to load
      if (newItems.length === 0 || newOffset + Limit >= totalItems) {
        setHasMore(false);
      }
    } catch (e) {
      console.error("Error fetching more author stories:", e);
    }
  };

  // useEffect(() => {
  //   const handleClick = () => {
  //     const targetElement = document.querySelector(".tagsWrapper"); //here we will take class of second container of the page for scrolling

  //     if (targetElement) {
  //       gsap.to(window, {
  //         scrollTo: { y: targetElement, autoKill: false },
  //         duration: 1,
  //         ease: "power2.inOut",
  //       });
  //     }
  //   };
  //   const button = document.querySelector(".author_downArrowCntr");
  //   button?.addEventListener("click", handleClick);

  //   return () => {
  //     button?.removeEventListener("click", handleClick);
  //   };
  // }, []);
  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div className="authorWapper_mainCntr">
        <InfoSection data={info} />
        <GridSection
          title={info?.name ?? ""}
          data={data}
          handleShowMore={handleShowMore}
          hasMore={hasMore}
        />
      </div>
      <TransparentSection />
    </>
  );
};

export default Author;

export async function getServerSideProps(context) {
  const Limit = 8;
  const url = `/${context.params.slug}`;
  const payload = {
    slug: url,
    limit: Limit,
    offset: Const.Offset,
  };

  try {
    const authorRes = await getAuthor(url);
    if (authorRes.status !== "success") {
      return {
        notFound: true,
      };
    }

    const info = {
      name: authorRes?.data?.name || "",
      subheading: authorRes?.data?.subheading || "",
      image: authorRes?.data?.image || "",
      aboutus: authorRes?.data?.aboutus || "",
      social: authorRes?.data?.social || {},
      timestamp: authorRes?.data?.timestamp || "",
    };
    const authorStoriesRes = await getAuthorStories(payload);
    return {
      props: {
        info: info ?? {},
        initialCount: authorStoriesRes?.totalCounts ?? 0,
        initialData: authorStoriesRes?.data ?? [],
        meta: authorRes?.data?.meta ?? {},
      },
    };
  } catch (error) {
    console.error("Error fetching author data:", error);
    return { notFound: true };
  }
}
