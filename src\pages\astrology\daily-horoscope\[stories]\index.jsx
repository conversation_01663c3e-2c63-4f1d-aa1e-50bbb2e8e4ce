import React, { useState } from "react";
import { usePathname } from "next/navigation";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import NewsArticleSchema from "@/components/seo/NewsArticleSchema";
import <PERSON> from "@/components/stories/Hero";
import AstroContentWrapper from "@/components/astrology/AstroContentWrapper";
import Advertisement from "@/components/stories/Advertisement";
import ShareModal from "@/components/common/ShareModal";
import TransparentSection from "@/components/common/TransparentSection";
import { getStories, getStoriesAuthor } from "@/pages/api/ArticleApi";
import { Ads } from "@/components/ads/Ads";
import { Const } from "@/utils/Constants";

const DailyHoroscopes = ({ data, breadcrumbs, author, tag, meta }) => {
  const pathname = usePathname();
  const router = pathname.split("/");
  const [openShare, setOpenShare] = useState(false);
  const content = data && data.content ? JSON.parse(data.content) : "";
  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema itemList={breadcrumbs} />
      <NewsArticleSchema
        headline={meta?.title || ""}
        datePublished={data?.timestamp || ""}
        dateModified={data?.updatedAt || ""}
        articleSection={breadcrumbs?.[0]?.name || ""}
        keywords={meta?.keywords || []}
        description={meta?.description || ""}
        url={pathname}
        content={content || {}}
        author={author?.[0] || {}}
        image={data?.coverImg || ""}
      />
      <div className="Stories_wrapper">
        <div className="container">
          <Hero
            title={data?.title ?? ""}
            excerpt={data?.excerpt ?? ""}
            author={author ?? []}
            timestamp={data?.timestamp ?? ""}
            breadcrumbs={breadcrumbs ?? []}
            setOpenShare={setOpenShare}
          />
          <div className="Stories_wrapper_inner">
            <Ads
              id="div-gpt-ad-stories-top"
              style={{ margin: "30px auto" }}
              adUnits={[
                {
                  adUnit: "/23290324739/Manifest-Desktop-TOP-970",
                  sizes: [[970, 90]],
                  sizeMapping: [
                    {
                      viewport: [0, 0],
                      sizes: [[970, 90]],
                    },
                  ],
                  minWidth: 1024,
                  maxWidth: Infinity,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Top-320",
                  sizes: [
                    [320, 50],
                    [320, 100],
                  ],
                  sizeMapping: [
                    {
                      viewport: [1023, 0],
                      sizes: [
                        [320, 50],
                        [320, 100],
                      ],
                    },
                  ],
                  minWidth: 768,
                  maxWidth: 1023,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Top-320",
                  sizes: [
                    [320, 50],
                    [320, 100],
                  ],
                  sizeMapping: [
                    {
                      viewport: [767, 0],
                      sizes: [
                        [320, 50],
                        [320, 100],
                      ],
                    },
                  ],
                  minWidth: 0,
                  maxWidth: 767,
                },
              ]}
              targeting={{
                section: [router?.[1] || null],
                "sub-section": [router?.[2] || null],
              }}
            />
            <article className="Article_container">
              <AstroContentWrapper
                content={content || {}}
                tag={tag ?? []}
              />
              {/* <aside className="Sidebar_container">
                <Advertisement isAstrology={true} />
              </aside> */}
            </article>
            <Ads
              id="div-gpt-ad-stories-bottom"
              style={{ margin: "50px auto 0px" }}
              adUnits={[
                {
                  adUnit: "/23290324739/Manifest-Desktop-Bottom-300",
                  sizes: [[300, 250]],
                  sizeMapping: [
                    {
                      viewport: [0, 0],
                      sizes: [[300, 250]],
                    },
                  ],
                  minWidth: 1024,
                  maxWidth: Infinity,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                  sizes: [
                    [300, 100],
                    [300, 250],
                  ],
                  sizeMapping: [
                    {
                      viewport: [1023, 0],
                      sizes: [
                        [300, 100],
                        [300, 250],
                      ],
                    },
                  ],
                  minWidth: 768,
                  maxWidth: 1023,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                  sizes: [
                    [300, 100],
                    [300, 250],
                  ],
                  sizeMapping: [
                    {
                      viewport: [767, 0],
                      sizes: [
                        [300, 100],
                        [300, 250],
                      ],
                    },
                  ],
                  minWidth: 0,
                  maxWidth: 767,
                },
              ]}
              targeting={{
                section: [router?.[1] || null],
                "sub-section": [router?.[2] || null],
              }}
            />
          </div>
        </div>
      </div>
      <ShareModal show={openShare} setShow={setOpenShare} />
      <TransparentSection />
    </>
  );
};

export default DailyHoroscopes;

export async function getServerSideProps(context) {
  const { res } = context;
  const { stories } = context.params;
  const url = `/astrology/daily-horoscope/${stories}`;
  const storiesUrl = `/${stories}`;
  try {
    const storiesRes = await getStories(url);
    const authorRes = await getStoriesAuthor(storiesUrl);
    if (storiesRes?.data?.isURLCorrect === false) {
      res.writeHead(301, { Location: storiesRes?.data?.correctUrl });
      res.end();
    }
    if (
      !storiesRes ||
      storiesRes.statusCode === Const.NotFound404 ||
      Object.keys(storiesRes.data.articleData).length === 0
    ) {
      return {
        notFound: true,
      };
    }

    return {
      props: {
        data: storiesRes?.data?.articleData?.data ?? {},
        template: storiesRes?.data?.articleData?.data?.template ?? 0,
        breadcrumbs: storiesRes?.data?.articleData?.breadcrumbs ?? [],
        author:
          authorRes?.data && authorRes?.data?.length > 0 ? authorRes?.data : [],
        tag: storiesRes?.data?.articleData?.tag ?? [],
        meta: storiesRes?.data?.articleData?.meta ?? {},
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      notFound: true,
    };
  }
}
