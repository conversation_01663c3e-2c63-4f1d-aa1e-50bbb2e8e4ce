import React from "react";
import Link from "next/link";
import Image from "next/image";
import { getAuthorText } from "@/utils/Util";

const BannerArticle = ({ data }) => {
  const bannerData = data && data.length > 0 ? data[0] : {};
  return (
    <>
      {bannerData && (
        <>
          <div className="big-container-blog k">
            <Link href={bannerData?.slug ?? ""}>
              <div className="blog-img-container">
                <Image
                  src={bannerData?.coverImg ? bannerData?.coverImg : ""}
                  alt={bannerData?.altName ?? ""}
                  fill
                />
              </div>
              <div className="blog-banner-text">
                <h2 className="slug">{bannerData?.title ?? ""}</h2>
                <p className="blog-banner-text-desc">
                  {bannerData?.excerpt ?? ""}
                </p>
                <span className="date-author">
                  {getAuthorText(
                    "// By",
                    bannerData?.author,
                    bannerData?.contributor
                  )}
                </span>
              </div>
            </Link>
          </div>
        </>
      )}
    </>
  );
};

export default BannerArticle;
