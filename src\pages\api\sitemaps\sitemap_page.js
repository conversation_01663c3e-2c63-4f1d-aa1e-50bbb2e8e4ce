import { Const } from "@/utils/Constants";

export default async function handler(req, res) {
  try {
    res.setHeader("Content-Type", "application/xml");
    const response = {
      data: [
        {
          loc: "/",
          changefreq: "daily",
          priority: 1,
        },
        {
          loc: "/about-us",
          changefreq: "daily",
          priority: 1,
        },
        {
          loc: "/work-with-us",
          changefreq: "daily",
          priority: 1,
        },
        {
          loc: "/contact-us",
          changefreq: "daily",
          priority: 1,
        },
        {
          loc: "/privacy-policy",
          changefreq: "daily",
          priority: 1,
        },
        {
          loc: "/terms-of-use",
          changefreq: "daily",
          priority: 1,
        },
        {
          loc: "/disclaimer",
          changefreq: "daily",
          priority: 1,
        },
      ],
    };
    const data = response && response.data.length > 0 ? response.data : [];
    if (data.length > 0) {
      const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${data
          .map(
            (item) => `
              <url>
                <loc>${Const.ClientLink}${item?.loc || ""}</loc>
                <changefreq>${item?.changefreq || "daily"}</changefreq>
                <priority>${item?.priority ?? 1}</priority>
              </url>`
          )
          .join("")}
      </urlset>`;
      return res.status(200).send(sitemap);
    } else {
      return res.status(404).send();
    }
  } catch (error) {
    console.error("Error generating sitemap:", error.message);
    return res.status(500).send("Internal Server Error");
  }
}
