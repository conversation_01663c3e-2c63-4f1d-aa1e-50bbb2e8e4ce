import React from "react";
import Link from "next/link";
import Seo<PERSON>eader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import TransparentSection from "@/components/common/TransparentSection";

const ContactUs = ({ meta }) => {
  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div className="ContactUsCntr">
        <div className="container">
          <div className="pageInner">
            <h1 className="ContactUs_heading">ContactUs</h1>
            <div className="entry-content">
              <p>
                For&nbsp;editorial&nbsp;inquiries,&nbsp;please&nbsp;reach&nbsp;out
                to&nbsp;
                <Link
                  href="mailto:<EMAIL>"
                  target="_blank"
                  rel="noreferrer noopener"
                >
                  <strong><EMAIL></strong>
                </Link>
              </p>

              <p>
                For&nbsp;business&nbsp;inquiries,&nbsp;please&nbsp;reach&nbsp;out
                to&nbsp;
                <Link
                  href="mailto:<EMAIL>"
                  target="_blank"
                  rel="noreferrer noopener"
                >
                  <strong><EMAIL></strong>
                </Link>
              </p>
            </div>
          </div>
        </div>
      </div>
      <TransparentSection />
    </>
  );
};

export default ContactUs;

export async function getStaticProps() {
  const meta = {
    title: "Contact Manifest Magazine India",
    description:
      "Get in touch with Manifest Magazine! Whether you have questions, suggestions, or media inquiries, reach out to our team for assistance and we’ll respond promptly.",
    keywords: [],
    author: "Manifest India",
    robots: "index,follow",
  };
  return { props: { meta: meta } };
}
