import React, { useState } from "react";
import useAuthentication from "@/hooks/useAuthentication";
import Se<PERSON><PERSON>eader from "@/components/seo/SeoHeader";
import <PERSON> from "@/components/stories/Hero";
import Banner from "@/components/stories/Banner";
import ContentWrapper from "@/components/stories/ContentWrapper";
import SwiperCardSection from "@/components/sections/SwiperCardSection";
import ShareModal from "@/components/common/ShareModal";
import TransparentSection from "@/components/common/TransparentSection";
import { getStoriesAuthor, viewArticle } from "@/pages/api/ArticleApi";

const Stories = ({ data, breadcrumbs, author, tag, latest, meta }) => {
  const [openShare, setOpenShare] = useState(false);
  const isAuthenticated = useAuthentication();
  const content = data && data.content ? JSON.parse(data.content) : "";

  if (!isAuthenticated)
    return (
      <>
        <div
          className="Stories_wrapper"
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            textAlign: "center",
            height: "100vh",
          }}
        >
          <div
            className="container"
            style={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
            }}
          >
            <h1>Please log in to view the content of this page</h1>
          </div>
        </div>
      </>
    );

  return (
    <>
      <SeoHeader meta={meta} />
      <div className="Stories_wrapper">
        <div className="container">
          <Hero
            title={data?.title ?? ""}
            excerpt={data?.excerpt ?? ""}
            author={author ?? []}
            timestamp={data?.timestamp ?? ""}
            breadcrumbs={breadcrumbs ?? []}
            setOpenShare={setOpenShare}
            contributor={data?.contributor ?? []}
          />
          <div className="Stories_wrapper_inner">
            <Banner
              image={data?.coverImg ?? ""}
              altName={data?.altName ?? ""}
              caption={data?.caption ? data?.caption : ""}
              courtesy={data?.courtesy ? data?.courtesy : ""}
              removeClassName={"gradient_remove_bg"}
            />
            <ContentWrapper content={content || {}} tag={tag ?? []} />
            <SwiperCardSection title="Keep Exploring" data={latest} />
          </div>
        </div>
      </div>
      <ShareModal show={openShare} setShow={setOpenShare} />
      <TransparentSection />
    </>
  );
};

export default Stories;

export async function getServerSideProps(context) {
  const { slug } = context.params;
  const url = `/${slug}`;

  try {
    const storiesRes = await viewArticle(url);
    const authorRes = await getStoriesAuthor(url);
    if (!storiesRes || Object.keys(storiesRes.data).length === 0) {
      return {
        notFound: true,
      };
    }
    return {
      props: {
        data: storiesRes?.data?.data ?? {},
        template: storiesRes?.data?.data?.template ?? 0,
        breadcrumbs: storiesRes?.data?.breadcrumbs ?? [],
        author:
          authorRes?.data && authorRes?.data?.length > 0 ? authorRes?.data : [],
        tag: storiesRes?.data?.tag ?? [],
        meta: storiesRes?.data?.meta ?? {},
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      notFound: true,
    };
  }
}
