.header_wrapper {
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  position: fixed;
  z-index: 10;
  background-color: #faf7f3;
  font-family: var(--primary-font-scotch-display);
  width: 100%;
  z-index: 99;
}
.header_wrapper .container{
  padding-bottom: 1.4rem;
}
.Navbar_wrapper {
  position: relative;
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  padding: 1rem 0;
  height: 3rem;
}
.Navbar_logo {
  position: relative;
  z-index: 8;
  /* height: 0; */
}
.Navbar_logo h3 {
  position: absolute;
  font-family: var(--primary-font-Neue);
  font-weight: 400;
  display: none;
  margin-top: 1rem;
  color: #000;
}
.Navbar_logo img {
  filter: invert(1);
  height: 42px;
}
.header_right_part {
  position: relative;
  /* background-color: green; */
  height: auto;
  display: flex;
  justify-content: flex-end;
}

.nav_hamburger {
  display: block;
  font-size: 2rem;
  cursor: pointer;
}
.subscribe_text {
  display: block;
  font-size: 1.1rem;
  font-family: var(--primary-font-scotch-display-compress);
}
.subscribe_text a {
  color: #000;
}
.header_wrapper nav {
  position: relative;
  text-align: right;
  display: flex;
  flex-direction: column;
  height: 90%;
  margin-top: 0.5rem;
  /* background-color: rgb(13, 0, 128); */
}
.header_wrapper nav ul {
  list-style: none;
  display: none;
}
.header_wrapper nav ul li {
  position: absolute;
  right: 0;
  /* margin: 0 1rem; */
  /* background-color: pink; */
  pointer-events: all;
  font-weight: 500;
  /* transition: all 0.3s ease; */
}
.header_right_part nav ul li a {
  color: gray;
  transition: all 0.3s ease;
}
/* .header_right_part nav ul li:hover {
  transform: translateY(-10px);
} */
.header_right_part nav ul li a:hover {
  color: black;
}
/* .header_wrapper nav ul li:nth-child(1) {
  top: 0%;
  left: 0;
  z-index: 7;
}
.header_wrapper nav ul li:nth-child(2) {
  top: 20%;
  left: 0;
  z-index: 6;
}
.header_wrapper nav ul li:nth-child(3) {
  top: 40%;
  left: 0;
  z-index: 5;
}
.header_wrapper nav ul li:nth-child(4) {
  top: 60%;
  left: 0;
  z-index: 4;
}
.header_wrapper nav ul li:nth-child(5) {
  top: 80%;
  left: 0;
  z-index: 3;
}
.header_wrapper nav ul li:nth-child(6) {
  top: 100%;
  left: 0;
  z-index: 2;
} */
/* nav ul li:nth-child(7) {
  top: 90%;
  left: 0;
  z-index: 1;
} */
.header_wrapper nav {
  display: none;
}
.Plus_btn {
  text-align: right;
  cursor: pointer;
  /* font-weight: 100; */
}
.shopImgnav {
  /* display: none; */
  height: 200px;
  position: absolute;
  right: 0;
  z-index: 9;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}
.shopImgnav img {
  height: 190px;
  width: 130px;
}
/* .tp_button.tp_button_Imgnav {
  margin: 5px;
} */
.Navbar_wrapperMbl {
  display: none;
}
@media only screen and (min-width: 1024px) {
  .header_right_part {
    display: block;
  }
  .Navbar_wrapper {
    padding: 1.3rem 0rem;
    height: 300px;
    grid-template-columns: 1fr 2.5fr !important;
    /* background-color: red; */
  }
  .Navbar_logo img {
    /* width: 180px; */
    /* height: 80%; */
    height: 18vw;
  }
  .Navbar_logo h3 {
    display: initial;
  }
  .header_wrapper nav {
    display: block;
  }
  .header_wrapper nav ul {
    display: initial;
  }
  .header_wrapper nav ul li {
    padding: 0.2rem 0;
    font-size: 1.1rem;
    /* transition: all 0.3s ease; */
  }
  .nav_hamburger {
    display: none;
  }
  .subscribe_text {
    display: none;
  }
}
@media only screen and (min-width: 1300px) {
  .header_wrapper nav ul li {
    font-size: 1.4rem;
  }
  .Navbar_logo img {
    /* width: 180px; */
    /* height: 100%; */
    height: 18vw;
  }
  .Navbar_logo h3 {
    font-size: 1.5rem;
  }
  .Navbar_wrapper {
    grid-template-columns: 1fr 2fr !important;
    /* background-color: red; */
  }
}
@media only screen and (min-width: 1600px) {
  .Navbar_wrapper {
    grid-template-columns: 1fr 1.5fr !important;
    /* background-color: red; */
  }
}

@media only screen and (max-width: 1023px) {
  .Navbar_wrapper {
    display: none;
  }
  .Navbar_wrapperMbl {
    display: block;
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1.5fr;
    padding: 1rem 0;
    height: 3rem;
  }
}
