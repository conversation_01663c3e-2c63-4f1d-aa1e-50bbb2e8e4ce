import Link from "next/link";
import React from "react";
import { RxCross2 } from "react-icons/rx";
import { FaInstagram, FaYoutube } from "react-icons/fa";
import { FaFacebookF } from "react-icons/fa";
import { FaWhatsapp } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { RiInstagramFill } from "react-icons/ri";
import { FaThreads } from "react-icons/fa6";
import Image from "next/image";
import Button from "./Button";

const MobileMenu = ({ menu, closeMenu, showMenu, setShowMenu }) => {
	const handlePageRefresh = () => {
		setShowMenu(false);
	};
	return (
		<div className={`dropdown-menu-main_M ${showMenu ? `open` : ""}`}>
			<div className="dropdown-wrapper_M">
				<div className="close-btn_M arrow-btn_M toggle-dropdown_M" onClick={closeMenu}>
					<RxCross2 />
				</div>
				<div>
					<div className="dropdown-top-links_M">
						{menu.map((item, index) => {
							return (
								<div
									key={`nav-menu-${index}`}
									className={`single-dropdown-col_M ${showMenu ? `visible` : ""}`}
								>
									<p>
										<Link href={item.link} onClick={handlePageRefresh}>
											{item.name}
										</Link>
									</p>
									<div className="dropdown-links_M">
										{item.submenus && item.submenus.length > 0 && (
											<div>
												{item.submenus.map((submenu, submenuIndex) => (
													<div key={`submenu-${submenuIndex}`}>
														<Link
															href={submenu.link}
															onClick={handlePageRefresh}
															className="animated-link-underline_M"
														>
															{submenu.name}
														</Link>
													</div>
												))}
											</div>
										)}
									</div>
								</div>
							);
						})}
					</div>
					<div className="testimonial_product_main">
						<div className="testimonial_product menunavImg">
							<div className="tp_wrapper">
								<div className="tp_media">
									<div className="tp_image">
										<Image
											id="block-testimonial_XHMbWy-34619739308283"
											className=""
											sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px"
											loading="lazy"
											fill
											alt="Khushi Kapoor on Manifest Cover"
											src="/Assets/Manifest-Artboard-2.jpg"
											//   srcset="//shop.lonelyplanet.com/cdn/shop/files/Ireland_16_600x.jpg?v=1708466142 600w, //shop.lonelyplanet.com/cdn/shop/files/Ireland_16_700x.jpg?v=1708466142 700w, //shop.lonelyplanet.com/cdn/shop/files/Ireland_16_800x.jpg?v=1708466142 800w, //shop.lonelyplanet.com/cdn/shop/files/Ireland_16_1000x.jpg?v=1708466142 1000w, //shop.lonelyplanet.com/cdn/shop/files/Ireland_16_1200x.jpg?v=1708466142 1200w, //shop.lonelyplanet.com/cdn/shop/files/Ireland_16_1400x.jpg?v=1708466142 1400w"
										/>
									</div>
									<div className="tp_button">
										<Button
											href={"https://shop.yudirect.biz/ManifestLife/Subscribe.php"}
											target={"_blank"}
										>
											Subscribe
										</Button>
										{/* <Link herf=""></Link> */}
									</div>
								</div>
							</div>
						</div>
					</div>
					<div className="Mobile_social_links">
						{/* Social links : */}
						<div className="Mobile_social_links_inner">
							<Link href="https://www.instagram.com/manifest.ind/" rel="nofollow">
								<span>
									<RiInstagramFill />
								</span>
							</Link>
							<Link href="https://www.facebook.com/profile.php?id=61559407741751" rel="nofollow">
								<span>
									<FaFacebookF />
								</span>
							</Link>
							{/* <Link href="#">
                <span>
                  <FaWhatsapp />
                </span>
              </Link> */}
							<Link href="https://x.com/_manifestind" rel="nofollow">
								<span>
									<FaXTwitter />
								</span>
							</Link>
							<Link href="https://www.threads.net/@manifest.ind" rel="nofollow">
								<span>
									<FaThreads />
								</span>
							</Link>
							<Link href="https://www.youtube.com/@ManifestInd" rel="nofollow">
								<span>
									<FaYoutube />
								</span>
							</Link>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default MobileMenu;
