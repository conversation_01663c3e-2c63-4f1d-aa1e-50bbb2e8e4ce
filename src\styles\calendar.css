.calendar {
  width: 100%;
  height: auto;
  border-radius: 8px;
  overflow: hidden;
  z-index: 99;
}

.calendar-header {
    margin: 10px 0px 0px 0px;
    font-size: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    /* background: #000; */
    color: #fff;
    gap: 20px;
}
.calendar-header span{
  display: flex;
  gap: 20px;
}


.calendar-header button {
  background: none;
  border: none;
  color: white;
  font-size: 1.2rem;
  cursor: pointer;
}

.calendar-header select {
    background: transparent;
    color: #000;
    border: none;
    border-radius: 4px;
    padding: 2px 5px;
    font-size: 1.3rem;
    font-family: var(--primary-font-Neue);
    cursor: pointer;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  padding: 10px;
  background: rgba(0, 0, 0, 0.1);
  font-family: var(--primary-font-Neue);
}

.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  text-align: center;
  padding: 10px;
}

.day {
  padding:  20px 10px;
  border-radius: 4px;
  margin: 2px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.dayHover:hover {
  background: #000;
  color: white;
}

.dayHoverDisabled{
  background-color: transparent;cursor: not-allowed;pointer-events: none;
  color:gray
}

.day-selected {
  background: #000;
  color: white;
}
