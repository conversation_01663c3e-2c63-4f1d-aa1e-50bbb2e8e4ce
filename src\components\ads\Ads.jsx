import React, { useEffect, useMemo, useState } from "react";
import { useAdsContext } from "@/context/AdsProvider";

export const Ads = ({ id = "", adUnits = [], targeting = {}, style }) => {
  const { transitioning, loadAds, googletag } = useAdsContext();
  const [isStyle, setIsStyle] = useState(false);

  const selectedAd = useMemo(() => {
    if (typeof window === "undefined") return null;
    const width = window.innerWidth;
    return adUnits.find((ad) => width >= ad.minWidth && width <= ad.maxWidth);
  }, [adUnits]);

  useEffect(() => {
    if (transitioning || !selectedAd) return;

    const { adUnit, sizes, sizeMapping } = selectedAd;
    if (!adUnit || !sizes) return;
    setIsStyle(true);
    setTimeout(() => {
      loadAds(id, adUnit, sizes, sizeMapping, targeting);
    }, 100);
  }, [transitioning, selectedAd, targeting, loadAds, googletag]);

  return (
    <div className="ad-flex-all">
      <div
        id={id}
        data-cy="Ad"
        // className="ad-text"
        {...(isStyle ? { style } : {})}
      />
    </div>
  );
};
