/* #page-content {
  position: relative;
  z-index: 1;
  overflow: hidden;
  background-color: #fff;
}
.page-content_inner {
  background-color: #fff;
  color: #000;
}
.page-content_inner_item {
  height: calc(100vh - 200px);
  width: 100%;
  align-items: flex-end;
  pointer-events: none;
  display: flex;
  background-color: rgb(255, 255, 255);
  padding-bottom: 2rem;
}
.content_inner-item_wrapper {
  width: 100%;
  overflow: hidden;
}
.content_inner-item_wrapper h1 {
  font-size: 19vw;
  text-transform: uppercase;
  line-height: 1;
  font-family: var(--secondary-font-Bitter);
}
.all_pages_article {
  width: auto;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}
.article-1 {
  position: relative;
  position: absolute;
  margin-bottom: 2rem;
  width: 100%;
  opacity: 1;
  transform: translate(0px, 0px);
  z-index: 1;
}
.article-1_inner {
  position: relative;
  overflow: hidden;
  color: #fff;
  background-color: #049cdb;
  margin: 0 -2rem;
}
.article-1_inner_link {
  position: relative;
  display: block;
}
.article_grid {
  display: grid;
  min-height: 100vh;
  width: 100%;
  width: auto;
  grid-template-columns: repeat(12, minmax(0, 1fr));
  gap: 2rem;
  overflow: hidden;
  padding: 2rem 2rem;
}
.article_grid_cntr_inner {
  overflow: hidden;
  position: absolute;
  inset: 0;
  margin: 0 -2rem;
}
.picture_wrapper {
  background-color: #049cdb;
  display: block;
  inset: 0;
  overflow: hidden;
  position: absolute;
}
.article_grid_image {
  position: absolute;
  inset: 0;
  height: 100%;
  width: 100%;
  object-fit: cover;
  object-position: 50% 50%;
}
.page-content_article_next {
  background-color: rgb(255, 255, 255);
  z-index: 0;
  position: relative;
  transform: translate3d(0px, 0px, 0px);
}

.sec-2 {
  width: 100%;
  height: calc(100vh - 200px);
  position: relative;
  background-color: red;
}
.article-2 {
  position: absolute;
  margin-bottom: 2rem;
  width: 100%;
  opacity: 1;
  transform: translate(0px, 0px);
  z-index: 1;
}
.article-2_inner {
  position: relative;
  overflow: hidden;
  color: #fff;
  background-color: #049cdb;
  margin: 0 -2rem;
}
.article-2_inner_link {
  position: relative;
  display: block;
}
.article_grid {
  display: grid;
  min-height: 100vh;
  width: 100%;
  width: auto;
  grid-template-columns: repeat(12, minmax(0, 1fr));
  gap: 2rem;
  overflow: hidden;
  padding: 2rem 2rem;
} */

/* @media (max-width: 768px) {
  .page-content_inner_item {
    min-height: calc(90vh-45px);
    align-items:center
  }
} */

/* new code */
#page-wrapper {
  overflow-y: initial !important;
  overflow-y: scroll;
  position: static;
  font-family: var(--font-family-sans-serif);
  background-color: #faf7f3;
  position: relative;
  z-index: 9;
  /* padding: 0 0 5rem 0; */
  border-bottom: 1px solid #000;
}
#page-content {
  background-color: #faf7f3;
  overflow: hidden;
  z-index: 1;
  position: relative;
}
.bg-background {
  background-color: #faf7f3;
}
.text-foreground {
  color: #000;
}
.Title-header-wrapper {
  position: relative;
  /* padding-bottom: 2rem; */
  align-items: flex-end;
  width: 100%;
  height: calc(100vh - 200px);
  display: flex;
  pointer-events: none;
  /* background-color: rgb(255, 255, 255); */
}
.category-sub-cat {
  position: absolute;
  top: 50%;
  left: 50%;
  /* transform: translate(-50%, -50%); */
  transform: translate(-50%, -50%);
  font-family: var(--primary-font-Neue);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.category-sub-cat ul {
  display: flex;
  align-items: center;
  gap: 1rem;
  font-size: 5vw;
  flex-direction: column;
  opacity: 0;
}
@media only screen and (min-width: 768px) {
  .category-sub-cat ul {
    gap: 1.2rem;
    font-size: 2.2vw;
    display: flex;
    flex-direction: row;
  }
}
@media only screen and (min-width: 1024px) {
  .category-sub-cat ul {
    gap: 1rem;
    font-size: 1.5vw;
    display: flex;
    flex-direction: row;
  }
}
@media only screen and (min-width: 1600px) {
  .category-sub-cat ul {
    gap: 1rem;
    font-size: 1.3vw;
  }
}
.category-sub-cat ul li {
  cursor: pointer;
  pointer-events: all;
  transition: all 0.3s linear;
}
.category-sub-cat ul li a {
  color: gray;
  transition: all 0.3s ease;
}

.category-sub-cat ul li a.active {
  color: #000;
}
.category-sub-cat ul li a:hover {
  color: black;
}
.h1-header {
  width: 100%;
  opacity: 1;
  transform: translate3d(0, 0, 0);
  display: flex;
  align-items: flex-end;
  justify-content: center;
  /* font-family: system-ui; */
  font-family: var(--primary-font-scotch-display);
  font-display: auto;
  height: 100%;
  /* overflow: hidden; */
  /* font-style: italic; */
}
.h1-header h1 {
  /* padding: 0 2rem; */
  overflow: visible;
  font-size: 12vw;
  line-height: 1;
  font-weight: 300;
  padding: 0 0 2rem 0;
}

.all-page-section {
  /* padding: 0 2rem; */
  width: auto;
  margin: 0 auto;
  /* overflow: hidden; */
  /* display: flex;
  flex-direction: column;
  gap: 20px; */
}
.article-cntr {
  width: 100%;
  /* padding: 0 1rem; */
  /* height: 100vh; */
  position: relative;
  /* margin-bottom: 8rem; */
  z-index: 9;
  /* transform: scaleX(0); */
  /* transform-origin: right; */
  /* transform: translateX(100%); */
  /* transition: all 1s cubic-bezier(0.19, 1, 0.22, 1); */
  overflow: hidden;
  background-color: #faf7f3;
  clip-path: inset(0 0 0 100%);
}
.article-cntr img {
  /* width: 100%;
  height: 100%; */
  /* max-width: 100%;
  height: auto; */
  object-fit: cover;
  object-position: center;
  /* transform-origin: right; */
  /* transform: translateX(-100%); */
  /* transform: scaleX(0); */
  /* transform-origin: right; */
  /* transform: translateX(100%); */
}
/* @media only screen and (min-width: 768px) {
  
} */
.article-cntr .blog-banner-text-desc {
  font-family: var(--primary-font-Neue);
  width: 100%;
  /* font-style: italic; */
  /* color: #fff; */
  font-weight: 400;
  /* margin: 1rem 0; */
  letter-spacing: 0.02em;
  line-height: 1.2;
  /* font-size: 1.3rem; */
}

.article-cntr h1 {
  font-size: 10vw;
  line-height: 1;
  width: 100%;
  /* color: #fff; */
  font-family: var(--primary-font-scotch-display);
  /* margin: 15px 0; */
  line-height: 1;
  display: block;
  font-style: normal;
  font-weight: 400;
}

.page-1-cntr {
  width: 100%;
  min-height: 100vh;
  background-color: #faf7f3;
  position: relative;
  z-index: 8;
  padding: 0 1rem;
}
.page-1-cntr h1 {
  font-size: 6.26rem;
  line-height: 1;
  font-family: system-ui;
}
/* .card_grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  grid-gap: 20px;
  align-items: stretch;
}
.card_grid > article {
  box-shadow: 2px 2px 6px 0px rgba(0, 0, 0, 0.3);
}
.card_grid > article img {
  max-width: 100%;
}

.card_grid .text {
  padding: 20px;
} */

.page-2-cntr {
  width: 100%;
  /* height: auto; */
  background-color: #faf7f3;
  position: relative;
  z-index: 8;
  margin-bottom: 0;
  /* padding: 0 1rem; */
  /* background-repeat: no-repeat;
  background-position: center;
  background-size: cover; */
  /* padding: 50px; */
}

.article-cntr:before,
.page-2-cntr:before {
  position: absolute;
  top: 0px;
  left: 0px;
  opacity: 1;
  z-index: 2;
  width: 100%;
  height: 100%;
  content: "";
  pointer-events: none;
  transition: opacity 0.3s;
  /* background: linear-gradient(to top, rgb(0, 0, 0), transparent 65%) 0% 0% /
    calc(100% + 1px) calc(100% + 1px); */
}
.page-2-cntr img {
  /* width: 100%;
  height: 100%; */
  /* max-width: 100%;
  height: auto; */
  object-fit: cover;
  object-position: center;
}
.page-2-cntr h1 {
  font-size: 10vw;
  line-height: 1;
  width: 100%;
  /* color: #fff; */
  font-family: var(--primary-font-scotch-display);
  font-weight: 400;
}
.page-2-cntr .blog-banner-text-desc {
  font-family: var(--primary-font-Neue);
  width: 100%;
  /* font-style: italic; */
  /* color: #fff; */
  font-weight: 400;
  /* margin: 1rem 0; */
  letter-spacing: 0.02em;
  line-height: 1.2;
  /* font-size: 1.3rem; */
}
.page-3-cntr {
  width: 100%;
  min-height: 100vh;
  position: relative;
  z-index: 7;
  padding: 0 1rem;
}
.page-3-cntr h1 {
  font-size: 6.26rem;
  line-height: 1;
  font-family: system-ui;
}
.page-4-cntr {
  width: 100%;
  /* height: 100vh; */
  position: relative;
  z-index: 7;
  /* margin-bottom: 8rem; */
  /* background-repeat: no-repeat;
  background-position: center;
  background-size: cover; */
}
.page-4-cntr h1 {
  font-size: 6.26rem;
  line-height: 1;
  font-family: system-ui;
}
@media only screen and (min-width: 768px) {
  .article-cntr h1,
  .page-2-cntr h1 {
    font-size: 2rem;
    width: 80%;
  }
}
@media only screen and (min-width: 991px) {
  /* .article-cntr,
  .page-2-cntr {
    height: 100vh;
  } */
  .article-cntr {
    padding: 0;
  }
  .page-2-cntr {
    padding: 0;
    /* margin-bottom: 8rem; */
  }
  .article-cntr img,
  .page-2-cntr img {
    /* width: 100%;
    height: 100%; */
    object-fit: cover;
    object-position: center;
  }
  .article-cntr:before,
  .page-2-cntr:before {
    background: linear-gradient(to top, rgb(0, 0, 0), transparent 65%) 0% 0% /
      calc(100% + 1px) calc(100% + 1px);
  }
  .page-3-cntr {
    padding: 0 2rem;
  }
}
@media only screen and (min-width: 1200px) {
  .article-cntr h1,
  .page-2-cntr h1 {
    font-size: 2.5rem;
    width: 50%;
  }
}

@media only screen and (min-width: 1651px) {
  .article-cntr h1,
  .page-2-cntr h1 {
    font-size: 6vw;
  }
}
.page-5-cntr {
  width: 100%;
  min-height: 100vh;
  /* background-color: rgba(95, 163, 194, 0.728); */

  position: relative;
  z-index: 6;
  padding: 0 1rem;
}
.page-5-cntr h1 {
  font-size: 6.26rem;
  line-height: 1;
  font-family: system-ui;
}
.page-6-cntr {
  width: 100%;
  height: 100vh;

  position: relative;
  z-index: 6;
  margin-bottom: 8rem;
  /* background-repeat: no-repeat;
  background-position: center;
  background-size: cover; */
}
.page-6-cntr h1 {
  font-size: 6.26rem;
  line-height: 1;
  font-family: system-ui;
}
/* new code */
@media only screen and (min-width: 1401px) and (max-width: 1650px) {
  .article-cntr .blog-banner-text-desc {
    width: 50%;
    line-height: 1.5;
  }
}
@media only screen and (min-width: 768px) {
  .Title-header-wrapper {
    height: calc(100vh - 100px);
  }
  .h1-header h1 {
    font-size: 12vw;
    /* line-height: 0.6; */
  }
  .article-cntr .blog-banner-text-desc,
  .page-2-cntr .blog-banner-text-desc {
    width: 70%;
  }
}
@media only screen and (min-width: 1401px) and (max-width: 1650px) {
  .article-cntr .blog-banner-text-desc,
  .page-2-cntr .blog-banner-text-desc {
    width: 50%;
  }
  .page-1-cntr {
    padding: 0 2rem;
  }
  .page-5-cntr {
    padding: 0 2rem;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1300px) {
  /* .article-cntr .blog-banner-text-desc {
    width: 50%;
  } */
  .page-1-cntr {
    padding: 0 2rem;
  }
  .page-5-cntr {
    padding: 0 2rem;
  }
}
@media only screen and (min-width: 1651px) {
  .article-cntr .blog-banner-text-desc,
  .page-2-cntr .blog-banner-text-desc {
    width: 45%;
    line-height: 1.2;
  }
  .page-1-cntr {
    padding: 0 2rem;
  }
  .page-5-cntr {
    padding: 0 2rem;
  }
}
