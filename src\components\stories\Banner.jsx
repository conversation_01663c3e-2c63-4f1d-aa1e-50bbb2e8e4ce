import React from "react";
import Image from "next/image";
import { hasHtmlTags } from "@/utils/Util";

const Banner = ({ image, altName, caption, courtesy,removeClassName=null }) => {
  return (
    // <div className="big-container-blog_wrapper">
    <div className="big-container-blog">
      <div className={`blog-img-container ${removeClassName}`}>
        <Image fill src={image ?? ""} alt={altName ?? ""} />
      </div>
      <div className="Stories_caption_wrapper">
        {caption && hasHtmlTags(caption) ? (
          <span
            className="Stories_caption"
            dangerouslySetInnerHTML={{
              __html: caption,
            }}
          />
        ) : (
          <span className="Stories_caption">{caption}</span>
        )}
        {courtesy && hasHtmlTags(courtesy) ? (
          <span
            className="Stories_courtesy"
            dangerouslySetInnerHTML={{
              __html: courtesy,
            }}
          />
        ) : (
          <span className="Stories_courtesy">{courtesy}</span>
        )}
      </div>
    </div>
    // </div>
  );
};

export default Banner;
