import React, { useEffect, useState } from "react";
import { getResults, getFilters } from "../../pages/api/ResultApi";
import { FaAngleDown } from "react-icons/fa6";
import Button from "../common/Button";
import { RxCross2 } from "react-icons/rx";
import { FaChevronUp } from "react-icons/fa";
const MobileDrawerFilter = ({
  setShowFilter,
  showFilter,
  setNewFilterData,
  newFilterData,
  filterSelected,
  setFilterSelected,
  query,
  setNewData,
  setNewTotalCounts,
  openFilter,
  setOpenFilter,
}) => {
  // const [show, setShow] = useState(false);
  const handleFilter = async (filter, name) => {
    let index = filterSelected[name].indexOf(filter._id);
    if (index > -1) {
      let tempArray = filterSelected[name];
      console.log(tempArray, "before filter");
      const filteredArray = tempArray.filter((item) => item._id === filter._id);
      console.log(filteredArray, "after filter");
      let newFilters = { ...filterSelected, [name]: filteredArray };
      console.log(newFilters, "filters to send");
      const data = await getResults(query, 0, 10, newFilters);
      const filterData = await getFilters(query, newFilters);
      setNewFilterData(filterData);
      console.log(data.count, "=> data count");
      setNewData(data.data);
      setNewTotalCounts(data.totalCounts);
      setFilterSelected(newFilters);
    } else {
      let newFilters = {
        ...filterSelected,
        [name]: [...(filterSelected[name] || []), filter._id], // Ensure it's an array or initialize as empty
      };
      console.log(newFilters, "filters to send");
      const data = await getResults(query, 0, 10, newFilters);
      const filterData = await getFilters(query, newFilters);
      console.log(filterData, "new filter data ui");
      console.log(filterData, "server data");
      setNewFilterData(filterData);
      // console.log(data.count, "sort data")
      // console.log(data,"data from backend after filteration")
      setNewData(data.data);
      setNewTotalCounts(data.totalCounts);
      setFilterSelected(newFilters);
    }
  };
  const [expanded, setExpanded] = useState(null);

  const toggleAccordion = (section) => {
    setExpanded(expanded === section ? null : section);
  };
  return (
    <div
      className="modalfilterwrapper"
      style={{
        transform: openFilter ? "translateX(0%)" : "translateX(100%)",
      }}
    >
      <div role="dialog" aria-modal="true" className="modalfiltercntr">
        <div className="modalfiltercntrtop">
          <h1>Filters</h1>
          <div className="filterclosebtn" onClick={() => setOpenFilter(false)}>
            <button>
              <RxCross2 />
            </button>
          </div>
        </div>
        <div className="modalfiltercntrcenter">
          {/* <div className="accordion">
            <h3 className="heading-Namecntr">
              <span>Category</span>
              <span className="arrow-dropdown">
                <FaAngleDown />
              </span>
            </h3>
          </div> */}
          <div className="author-sort">
            <div
              className="sort-top"
              onClick={() => toggleAccordion("categories")}
            >
              <h3 className="filter-head-p sd">
                Filter by Categories
                {/* <span style={{ fontSize: "25px", marginLeft: "5px" }}>{` (${
                  newFilterData && (newFilterData?.subcategories?.length ?? 0)
                })`}</span> */}
              </h3>
              {/* <p className="sort-clear">CLEAR</p> */}
              <span className="arrow-dropdown">
                <FaAngleDown
                  style={{
                    transform:
                      expanded === "categories"
                        ? "rotate(180deg)"
                        : "rotate(0deg)",
                    transition: "all 0.3s ease",
                  }}
                />
              </span>
            </div>
            {expanded === "categories" && (
              <div className="pannelfilter">
                {newFilterData &&
                  newFilterData.subcategories.map((filter, k) => (
                    <div key={k}>
                      <div className="row-sort">
                        <div className="checkbox-filtername">
                          <input
                            type="checkbox"
                            checked={
                              filterSelected.subcategoryIds.includes(filter._id)
                                ? true
                                : false
                            }
                            onChange={() =>
                              handleFilter(filter, "subcategoryIds")
                            }
                          />
                          <p className="filter-name-tag">{filter.name}</p>
                        </div>
                        <p className="filter-qty-tag">{filter.articleCount}</p>
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </div>
          <div className="author-sort">
            <div className="sort-top" onClick={() => toggleAccordion("tags")}>
              <h3 className="filter-head-p sd">
                Filter by Tags
                {/* <span style={{ fontSize: "25px", marginLeft: "5px" }}>{` (${
                  newFilterData && (newFilterData?.tags?.length ?? 0)
                })`}</span> */}
                {/* <span
                onClick={() => {
                  setShow(!show);
                }}
                >
                  <FaAngleDown
                  style={{
                    transition: "all 0.3s ease",
                    rotate: show ? "0deg" : "180deg",
                    marginLeft: "5px",
                    cursor: "pointer",
                  }}
                  />
                </span> */}
              </h3>

              {/* <p className="sort-clear">CLEAR</p> */}
              <span className="arrow-dropdown">
                <FaAngleDown
                  style={{
                    transform:
                      expanded === "tags" ? "rotate(180deg)" : "rotate(0deg)",
                    transition: "all 0.3s ease",
                  }}
                />
              </span>
            </div>
            {expanded === "tags" && (
              <div className="pannelfilter">
                <div className="sort_cntr">
                  {newFilterData &&
                    newFilterData.tags.map((filter, k) => (
                      <div key={k}>
                        <div className="row-sort">
                          <div className="checkbox-filtername">
                            <input
                              type="checkbox"
                              checked={
                                filterSelected.tagIds.includes(filter._id)
                                  ? true
                                  : false
                              }
                              onChange={() => handleFilter(filter, "tagIds")}
                            />
                            <p className="filter-name-tag">
                              {filter.name || ""}
                            </p>
                          </div>
                          <p className="filter-qty-tag">
                            {filter.articleCount}
                          </p>
                        </div>
                      </div>
                    ))}
                </div>
              </div>
            )}
          </div>

          <div className="author-sort">
            <div
              className="sort-top"
              onClick={() => toggleAccordion("authors")}
            >
              <h3 className="filter-head-p sd">
                Filter by Authors
                {/* <span style={{ fontSize: "25px", marginLeft: "5px" }}>{` (${
                  newFilterData && (newFilterData?.writers?.length ?? 0)
                })`}</span> */}
              </h3>
              {/* <p className="sort-clear">CLEAR</p> */}
              <span className="arrow-dropdown">
                <FaAngleDown
                  style={{
                    transform:
                      expanded === "authors"
                        ? "rotate(180deg)"
                        : "rotate(0deg)",
                    transition: "all 0.3s ease",
                  }}
                />
              </span>
            </div>
            {/* <div className={`sort_cntr ${show ? null : "sort_hide"}`}> */}
            {expanded === "authors" && (
              <div className="pannelfilter">
                {newFilterData &&
                  newFilterData.writers.map((filter, k) => (
                    <div key={k}>
                      <div className="row-sort">
                        <div className="checkbox-filtername">
                          <input
                            type="checkbox"
                            checked={
                              filterSelected.writerIds.includes(filter._id)
                                ? true
                                : false
                            }
                            onChange={() => handleFilter(filter, "writerIds")}
                          />
                          <p className="filter-name-tag">
                            {filter.firstname} {filter.lastname || ""}
                          </p>
                        </div>
                        <p className="filter-qty-tag">{filter.articleCount}</p>
                      </div>
                    </div>
                  ))}
              </div>
            )}
            {/* </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MobileDrawerFilter;
