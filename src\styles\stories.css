.Stories_wrapper {
  position: relative;
  width: 100%;
  /* padding-top: 6vw; */
  /* overflow: hidden; */
  /* height: 100vh; */
  /* background-color: red; */
  background-color: #faf7f3;
  z-index: 9;
  padding: 4rem 0 0 0;
  /* border-bottom: 1px solid #000; */
}
.StoriesInfo_wrapper {
  position: relative;
  /* width: 100%; */
  /* height: 100vh; */
  background-color: #faf7f3;
  display: grid;
  grid-template-columns: 4fr 1fr;
  gap: 1.25rem;
  padding: 0rem 0;
  /* padding: 2rem 0; */
  /* margin: 2rem 0; */
}
.stories_header_wrapper {
  width: 100%;
  /* height: 50vh; */
  position: relative;
  /* background-color: red; */
  position: sticky;
  top: 120px;
  padding-top: 48px;
  padding-bottom: 24px;
}
.stories_header_item {
  -section-item-gap: 32px;
  display: grid;
  gap: 48px;
}
.stories_header_content {
  -section-content-gap: 20px;
  display: grid;
  gap: 42px;
  /* grid-template-columns: 1fr; */
}
.stories_header_content h1 {
  font-family: var(--primary-font-Neue-Medium);
  /* text-align: center; */
  font-size: 1.8rem;
  line-height: 1.2;
}
/* story-top */
.story-top {
  padding: 2rem 0 0px 0;
  position: sticky;
  top: 50px;
  display: flex;
  /* align-items: center; */
  justify-content: center;
  flex-direction: column;
  z-index: 9;
}
.breadcrumb {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}
.breadcrumb p {
  font-size: 12px !important;
  margin-bottom: 0 !important;
  font-weight: 400;
  font-style: normal !important;
  font-family: var(--primary-font-Neue);
  color: #000;
  display: flex;
}
.breadcrumb p a {
  color: #000;
}

.contr-fluid {
  width: 100%;
  max-width: 1520px;
  padding-left: 0;
  padding-right: 0;
  display: grid;
  grid-template-columns: 2fr;
  /* gap: 1rem; */
}
.contr-fluid_inner {
  display: flex;
  flex-wrap: wrap;
  flex-direction: column;
}
.story-top h1 {
  /* font-family: var(--primary-font-Neue-Bold); */
  font-family: var(--primary-font-scotch-display-compress);
  /* font-style: italic; */
  font-weight: 400;
  font-size: 32px;
  line-height: 1;
  margin-bottom: 10px;
  display: block;
  color: #000;
  font-style: normal;
}
.para-ButFlex {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  justify-content: space-between;
  flex-direction: column;
  margin: 1rem 0;
}
/* .auther-ButFlex .date-author {
  color: #000;
} */
.para-ButFlex {
  color: #000;
}
.story-top p {
  font-size: 16px;
  line-height: 1.2;
  font-style: normal;
  /* margin-bottom: 25px; */
  /* font-family: var(--primary-font-Neue-Medium); */
  font-family: var(--primary-font-Neue);
  font-weight: 300;
}
/* .auther-ButFlex .cardauthorlink {
  color: #000;
} */

@media only screen and (max-width: 767px) {
  .story-top h1 {
    font-size: 30px;
    line-height: 1 !important;
    margin-bottom: 0;
  }
  .para-ButFlex {
    margin: 8px 0;
  }
}

@media only screen and (min-width: 768px) {
  .contr-fluid {
    width: 100%;
    max-width: 1520px;
    padding-left: 0;
    padding-right: 0;
    display: grid;
    grid-template-columns: 5fr 1fr;
  }
  .card-meta_meta {
    flex-direction: column;
  }
  .story-top {
    padding: 4rem 0;
    padding-bottom: 0px;
    top: 40px;
  }
  .story-top h1 {
    font-size: 2.5rem;
    line-height: 1.2;
  }
  .auther-ButFlex {
    flex-direction: row;
  }
  .breadcrumb p {
    font-size: 18px !important;
  }
  .story-top p {
    /* font-size: 18px !important; */
  }
}
@media (min-width: 1200px) {
  .container-fluid {
    /* padding-right: calc(1.5rem * 0.5); */
    /* padding-left: calc(1.5rem * 0.5); */
    max-width: 950px;
  }

  .story-top {
    align-items: flex-start;
    top: 40px;
  }
  .story-top p {
    font-size: 20px;
    line-height: 1.333;
  }
  .para-ButFlex {
    align-items: center;
    flex-direction: row;
  }

  .story-top h1 {
    font-size: 3.5rem;
  }
}
@media only screen and (min-width: 1401px) and (max-width: 1650px) {
  .container-fluid {
    max-width: 1100px;
  }
  .story-top p {
    width: 80%;
  }
  .StoriesInfo_left_innercntr-full-width.embed-twitter {
    position: relative;
    aspect-ratio: 16/9;
  }
}
@media only screen and (min-width: 1651px) {
  .container-fluid {
    /* padding-left: 1.5rem;
    padding-right: 1.5rem; */
    max-width: 1100px;
  }
  .story-top h1 {
    font-size: 3.8rem;
    line-height: 1.2;
  }
  .StoriesInfo_left_innercntr-full-width.embed-twitter {
    position: relative;
    aspect-ratio: 16/9;
  }
}
/* story-top */
.Stories_wrapper_inner {
  background-color: #faf7f3;
  position: relative;
  z-index: 99;
}
.StoriesInfo_leftcntr {
  position: relative;
  display: grid;
  /* gap: 48px; */
  font-family: var(--primary-font-Neue);
}
.StoriesInfo_left_innercntr {
  --section-content-gap: 20px;
  display: grid;
  /* gap: 42px; */
  /* background-color: blue; */
}
.StoriesInfo_left_innercntr-full-width {
  /* font-family: var(--primary-font-scotch-display-normal); */
  font-family: var(--primary-font-Neue);
  letter-spacing: 0.02em;
  display: grid;
  justify-content: flex-start;
  grid-template-columns: 100%;
  width: 100%;
}
.Stories_list_style {
  /* font-family: var(--primary-font-scotch-display-normal); */
  font-family: var(--primary-font-Neue);
  letter-spacing: 0.02em;
  display: grid;
  justify-content: flex-start;
  grid-template-columns: 100%;
  width: 100%;
  margin: 10px 0px;
}
.Stories_list_style li {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  padding: 5px 0px;
}
ol.Stories_list_style li {
  counter-increment: step-counter;
}
ul.Stories_list_style li {
  position: relative;
}
ol.Stories_list_style li::before {
  content: counter(step-counter) ".";
  font-size: 1.15rem;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: fit-content;
  line-height: 1;
}
ul.Stories_list_style li::before {
  content: "•";
  font-size: 1.5rem;
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: fit-content;
  line-height: 1;
}
ol.Stories_list_style li p,
ul.Stories_list_style li p {
  font-size: 18px;
  font-weight: 300;
}

.detailed-media_info {
  display: flex;
  flex-direction: column;
  gap: 32px;
  font-family: var(--primary-font-scotch-display-compress);
  /* align-items: flex-end; */
  /* justify-content: flex-end; */
  /* padding-left: 2rem; */
  /* width: 100%; */
}
.related-post-wrapper {
  width: 100%;
  margin: 20px 0;
  /* padding: 20px 20px; */
  padding: 15px 20px 10px 20px;
  background-color: #efe8db;
}
.related-post-heading {
}
.grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}
.related-post-card {
  display: flex !important;
  gap: 10px !important;
  border-bottom: 1px dotted rgba(0, 0, 0, 1);
  border-left: none !important;
  border-radius: 0px !important;
  overflow: hidden;
  color: inherit !important;
  /* padding-block: 10px; */
  padding: 12px 0px 10px 0px;
}
.related-post-card strong {
  line-height: 1;
}
.row .related-post-card:last-child {
  border: none !important;
}
.related-post-image {
  position: relative;
  width: 60%;
  max-width: 130px;
  height: auto;
  aspect-ratio: 1;
  overflow: hidden;
}
.category {
  /* color: red; */
  font-weight: bold;
  margin-top: 10px;
  font-size: 12px;
  text-transform: uppercase;
}

.card-title {
  display: -webkit-box;
  /* max-width: 300px; */
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  font-size: 1.5rem !important;
  overflow: hidden;
  font-weight: 600 !important;
  font-family: var(--primary-font-scotch-display-compress);
  line-height: 1;
  text-align: left;
  font-weight: 300;
  font-style: normal;
  color: inherit !important;
}

@media only screen and (min-width: 992px) {
  .detailed-media_info {
    align-items: center;
    padding-left: 0rem;
    /* justify-content: flex-end; */
  }
}
@media only screen and (min-width: 1600px) {
  .detailed-media_info {
    padding-left: 3rem;
  }
}
.card-meta_meta {
  display: flex;
  -moz-column-gap: 2em;
  column-gap: 2em;
  row-gap: 1.4em;
  flex-direction: column;
}
.card-meta_meta_inner {
  display: flex;
  -moz-column-gap: 1em;
  column-gap: 1em;
  gap: 8px;
  /* row-gap: 1em; */
  /* flex-direction: column; */
}
.card-meta_item {
  display: flex;
  /* flex-direction: column; */
  align-items: center;
  /* justify-content: space-between; */
  gap: 5px;
}
.card-meta_label {
  text-transform: uppercase;
  font-size: 15px;
  letter-spacing: 0;
  line-height: 1.5;
  letter-spacing: 0.05em;
  font-weight: 500;
  white-space: nowrap;
}
/* .card-meta_label:nth-child(1) {
  font-family: var(--primary-font-Neue);
} */
.text-7 {
  font-family: var(--primary-font-scotch-display-normal);
  font-style: normal;
  font-size: 16px;
  line-height: 1.5;
  text-transform: capitalize;
}
.card-actions_actions {
  display: flex;
  flex-basis: auto;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}
.rounded_btns {
  position: relative;
  z-index: 0;
  display: inline-flex;
  height: 35px;
  /* width: 44px; */
  padding: 0 15px;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: 44px;
  gap: 12px;
  text-overflow: ellipsis;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1.5px solid #000000; /* Default border */
  transition: border-color 0.4s ease; /* Smooth transition for border color */
  cursor: pointer;
  overflow: hidden;
}
.rounded_btns svg {
  font-size: 20px;
  z-index: 9;
  transition: all 0.3s ease;
}
.rounded_btns:before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 0;
  background-color: #000000;
  top: 100%;
  left: 0;
  transition: all 0.3s ease;
}

/* Apply hover effect to the button */
.rounded_btns:hover {
  color: #fff;
}

/* Apply hover effect to the pseudo-element */
.rounded_btns:hover:before {
  opacity: 1;
  border-color: rgb(189, 184, 184);
  color: #fff;
  top: 0;
}
.button_label {
  display: flex;
  align-items: center;
}
.button_label svg {
  font-size: 20px;
}
.StoriesInfo_leftcntr .rich-text_wrapper p {
  font-weight: 300;
  /* letter-spacing: 0.02em; */
  line-height: 1.222;
}
.StoriesInfo_leftcntr .rich-text_wrapper.leftSide {
  font-family: var(--primary-font-scotch-display);
  font-size: 1rem;
  letter-spacing: 0.03em;
  font-style: normal;
  line-height: 1.3;
}
.StoriesInfo_leftcntr .rich-text_wrapper.leftSide p {
  font-weight: 500;
}
.text_caption,
.text_content {
  display: grid;
  gap: 48px;
}
.rich-text_wrapper {
  font-size: 1.2rem;
  /* letter-spacing: 0.02em; */
}
.rich-text_wrapper a {
  color: rgba(59, 130, 246, 1) !important;
}
.rich-text_wrapper a span {
  color: unset !important;
}
.media-grid_wrapper {
  /* display: grid; */
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 20px 0;
}
.media-grid_wrapper_inner {
  display: grid;
  gap: 42px;
  width: 100%;
}
.media-grid_wrapper_inner-full-width {
  display: flex;
  flex-direction: column;
  width: 100%;
  /* height: 100vh; */
  /* background-color: red; */
  position: relative;
  overflow: hidden;
}
.media-grid_wrapper_inner-full-width img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.media-grid_item {
  display: grid;
  gap: 16px;
  grid-template-rows: max-content;
}
.media-wrapper_wrapper {
  position: relative;
  /* aspect-ratio: 0.79; */
}
.cursor-trigger_pointer {
  cursor: pointer;
}
.media-wrapper_absolute {
  position: absolute;
  inset: 0;
}
.media-item_media {
  opacity: 1;
  position: absolute;
  height: 100%;
  width: 100%;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  color: transparent;
}
.media-wrapper_absolute img {
  object-fit: cover;
}
.divider-line {
  border-left: 1px solid rgba(2, 3, 8, 0.1);
}
.StoriesInfo_rightcntr {
  position: relative;
  /* top: 200px;
  left: 0; */
  /* background-color: #efe8db; */
  /* font-family: var(--primary-font-Neue-Light); */
  /* display: flex;
  flex-direction: column;
  align-items: center; */
  padding-top: 1.5rem !important;
  /* align-self: start; */
}
.Stories_caption_wrapper {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 5px;
  font-size: 16px;
  padding: 0.5rem 0;
  flex-direction: column;
  font-family: var(--primary-font-Neue);
}
.Stories_caption {
  color: rgba(0, 0, 0, 1);
  font-weight: 500;
  font-size: 14px;
  line-height: 1.1;
}
.Stories_courtesy {
  font-weight: 500;
  /* color: rgba(0, 0, 0, 1); */
  color: gray;
  font-size: 14px;
}
.sticky-post {
  position: sticky;
  width: 100%;
  /* height: 10%; */
  top: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: var(--primary-font-Neue-Light);
  color: gray;
  /* background-color: rgb(223, 220, 220); */
  border-radius: 10px;
  margin-top: 1rem;
  flex-direction: column;
  gap: 1rem;
}
.sticky-post h1 {
  font-weight: 600;
  font-style: normal;
  font-size: 0.7rem;
  letter-spacing: 0.2em;
  line-height: 1;
}
.sticky-post img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.sticky-post span {
  font-size: 2rem;
}
.Readmore_actions {
  justify-content: flex-start;
  grid-template-columns: 31% 57%;
  /* width: 85%; */
  margin: 0 auto;
  display: grid;
  gap: 10px;
  grid-template-columns: 100%;
  align-items: center;
  padding: 1rem 0;
}
.Readmore_actions_first-not-button {
  /* margin-left: 2em; */
  /* font-family: var(--font-family-sans-serif); */
  font-size: 1rem;
  letter-spacing: 0;
  /* line-height: var(--line-height);
  --line-height: 1.5; */
}
.Readmore_actions_first-not-button span {
  font-size: 0.875rem;
}
ul li {
  list-style: none;
}
.Readmore_actions_inner {
  display: flex;
  grid-template-columns: 31% 57%;
  gap: 10px;
  flex-wrap: wrap;
  /* font-weight: 600; */
  /* flex-direction: column; */
}

.share__modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background-color: rgba(0, 0, 0, 0.5);
  transform: scale(1.1);
  opacity: 0;
  pointer-events: none;
  visibility: hidden;
  transition: visibility 0s linear 0.3s, opacity 0.3s 0s, transform 0.3s;
  z-index: 9999;
}
.share__modal.show {
  opacity: 1;
  pointer-events: all;
  visibility: visible;
  transform: scale(1);
  transition: visibility 0s linear 0s, opacity 0.25s 0s, transform 0.25s;
}
#share_modal_close_btn {
  position: absolute;
  font-size: 1.5rem;
  right: 30px;
  cursor: pointer;
}
.share__content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: white;
  width: 30rem;
  /* height: 20rem; */
  overflow-y: scroll;
  padding: 30px;
  border-radius: 10px;
  text-align: left;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}
.share_body {
  display: flex;
  flex-direction: column;
  gap: 30px;
}
.share_body h2 {
  font-size: 2rem;
  color: #000000;
  text-align: center;
  line-height: 1;
  font-family: var(--primary-font-scotch-display);
  font-weight: 400;
}

.share__icons {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
}
.share_body h3 {
  color: gray;
  font-weight: 300;
  font-size: 1rem;
  font-family: var(--primary-font-Neue);
  margin-bottom: 0.5rem;
}
#check-group {
  animation: 0.32s ease-in-out 1.03s check-group;
  transform-origin: center;
}

#check-group #check {
  animation: 0.34s cubic-bezier(0.65, 0, 1, 1) 0.8s forwards check;
  stroke-dasharray: 0, 75px;
  stroke-linecap: round;
  stroke-linejoin: round;
}

#check-group #outline {
  animation: 0.38s ease-in outline;
  transform: rotate(0deg);
  transform-origin: center;
}

#check-group #white-circle {
  animation: 0.35s ease-in 0.35s forwards circle;
  transform: none;
  transform-origin: center;
}
.copy_text_anim {
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-family: var(--primary-font-Neue);
}
.copy_text_anim svg {
}
.StoriesInfo_left_innercntr-full-width.embed-twitter {
  position: relative;
  aspect-ratio: 16/9;
}
.flex-all-embed {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
@keyframes outline {
  from {
    stroke-dasharray: 0, 345.576px;
  }
  to {
    stroke-dasharray: 345.576px, 345.576px;
  }
}
@keyframes circle {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(0);
  }
}
@keyframes check {
  from {
    stroke-dasharray: 0, 75px;
  }
  to {
    stroke-dasharray: 75px, 75px;
  }
}
@keyframes check-group {
  from {
    transform: scale(1);
  }
  50% {
    transform: scale(1.09);
  }
  to {
    transform: scale(1);
  }
}
.share__icon {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #f3f3f3;
  border-radius: 50%;
  width: 60px;
  height: 60px;
  color: #333;

  transition: background 0.3s, color 0.3s;
}
.share__icon svg {
  font-size: 24px;
}
.share__icon:hover {
  background: rgba(0, 0, 0, 1);
  color: #fff;
}

.share__icon button {
  all: unset;
  cursor: pointer;
}
.link_copycntr {
  width: 100%;
  padding: 10px;
  background-color: #f3f3f3;
  border-radius: 5px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
}
.link_copycntr input {
  width: 100%;
  background: none;
  outline: none;
  border: none;
  font-family: var(--primary-font-Neue);
}
.link_copycntr svg {
  font-size: 24px;
  cursor: pointer;
}

.nextStoryTitle {
  font-family: var(--primary-font-Neue);
  font-size: 1.8rem;
  font-weight: bold;
  white-space: nowrap;
  margin: 0;
  color: #1a1a1a;
}
.nextStoryLine {
  flex-grow: 1;
  height: 1px;
  background-color: #1a1a1a;
}
.nextStoryContainer {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 2rem 0 0 0;
  gap: 1rem;
}

@media (max-width: 600px) {
  #share_modal_close_btn {
    right: 20px;
  }
}
@media only screen and (max-width: 992px) {
  .card-meta_meta {
    row-gap: 16px;
  }
  .contr-fluid {
    gap: 1em;
  }
  .Stories_caption {
    font-size: 14px;
  }
  .Stories_courtesy {
    font-size: 14px;
  }
}
@media only screen and (max-width: 767px) {
  .grid {
    display: grid;
    grid-template-columns: 1fr !important;
    gap: 0px !important;
    padding: 2px 0px !important;
    border-top: 1px solid rgba(0, 0, 0, 1);
    border-bottom: 1px solid rgba(0, 0, 0, 1);
  }
  .related-stories-card {
    grid-column: span 1;
  }

  .related-post-wrapper strong {
    font-size: 12px;
  }
  .grid .related-post-card,
  .row .related-post-card {
    border-bottom: 1px dotted rgba(0, 0, 0, 1);
  }
  .grid .related-post-card:last-child {
    border-bottom: none !important;
  }
  .related-post-card {
    padding-block: 10px;
  }
  .grid h3.card-title,
  .row h3.card-title {
    -webkit-line-clamp: 3 !important;
    font-size: 1.2rem !important;
  }
  .row .related-post-card:first-child {
    padding-top: 10px !important;
  }
  .row .related-post-card:last-child {
    padding-bottom: 10px !important;
  }
  .related-post-heading {
    font-size: 27px !important;
    font-weight: 700 !important;
  }
  .share__content {
    width: 85vw;
  }
  .share_body h2 {
    font-size: 2rem;
  }
  .share__icon {
    width: 40px;
    height: 40px;
  }
  .share__icon svg {
    font-size: 18px;
  }
  .share__icons {
    justify-content: space-around;
  }
  .nextStoryTitle {
    margin-left: 12px;
  }
}
@media only screen and (max-width: 425px) {
  .share_body h2 {
    font-size: 1.5rem;
  }
}
@media only screen and (min-width: 640px) {
  .stories_header_content {
    grid-template-columns: 3fr 0fr;
  }
  .stories_header_content h1 {
    font-size: 2rem;
  }
  .StoriesInfo_left_innercntr,
  .media-grid_wrapper_inner {
    justify-content: center;
    grid-template-columns: 66.66%;
  }
  .media-grid_wrapper_inner-full-width {
    width: 66.66%;
  }
  .StoriesInfo_left_innercntr-full-width {
    justify-content: center;
    grid-template-columns: 100%;
  }
  .Stories_list_style {
    justify-content: center;
    grid-template-columns: 66.66%;
  }
  .text-7 {
    font-size: 20px;
  }
  .Readmore_actions {
    justify-content: center;
    grid-template-columns: 100%;

    /* flex-direction: row; */
  }
  .Readmore_actions_inner {
    gap: 10px;
    /* flex-wrap: nowrap; */
  }
}
@media only screen and (min-width: 768px) {
  .stories_header_wrapper {
    padding-top: 48px;
    padding-bottom: 32px;
  }
  .stories_header_content {
    grid-template-columns: 3fr 0fr;
  }
  .stories_header_content h1 {
    font-size: 3rem;
  }
  .StoriesInfo_left_innercntr {
    gap: 42px;
  }
  .Readmore_actions {
    --section-content-gap: 42px;
    justify-content: flex-start;
    grid-template-columns: 100%;
    width: 100%;
    margin: 0 auto;
    justify-content: center;
  }
  .media-grid_wrapper_inner-full-width {
    width: 66.66%;
  }
  /* .Readmore_actions_inner {
    flex-direction: row;
  } */
  .card-meta_item {
    flex-direction: column;
    align-items: flex-start;
  }
  .card-title {
    font-size: 1rem;
  }
}
@media only screen and (min-width: 1024px) {
  .stories_header_wrapper {
    padding-top: 28px;
    padding-bottom: 72px;
  }
  .stories_header_content {
    grid-template-columns: 1.2fr 1fr;
  }
  .stories_header_content h1 {
    font-size: 3rem;
  }
  .StoriesInfo_wrapper {
    grid-template-columns: 4fr 1fr;
    /* padding: 2rem 0; */
  }
  .StoriesInfo_leftcntr {
    /* --section-item-gap: 48px; */
  }
  .StoriesInfo_left_innercntr {
    justify-content: flex-start;
    grid-template-columns: 31% 69%;
    width: 100%;
    margin: 0 auto;
  }
  .StoriesInfo_left_innercntr-full-width {
    justify-content: flex-end;
    grid-template-columns: 66.66%;
  }
  .Stories_list_style {
    justify-content: flex-end;
    grid-template-columns: 66.66%;
  }
  .media-grid_wrapper_inner-full-width {
    width: 100%;
  }
  .StoriesInfo_left_innercntr > :first-child:last-child {
    grid-column-start: 1;
  }
  .media-grid_wrapper {
    padding: 20px 0;
  }
  .media-grid_wrapper_inner {
    width: 100%;
    /* margin: 0 auto; */
    grid-template-columns: repeat(2, 1fr);
  }
  .StoriesInfo_leftcntr .rich-text_wrapper.leftSide {
    max-width: 225px;
  }
  .sticky-post {
    position: sticky;
    top: 90px;
  }
  .sticky-post span {
    font-size: 5rem;
  }
  .Readmore_actions {
    --section-content-gap: 42px;
    justify-content: flex-start;
    grid-template-columns: 32% 66.66%;
    width: 100%;
    margin: 0 auto;
    /* justify-content: center; */
  }
  .StoriesInfo_left_innercntr-full-width.embed-twitter {
    position: relative;
    aspect-ratio: 16/10;
  }
}
@media only screen and (max-width: 991px) {
  .StoriesInfo_wrapper {
    grid-template-columns: 1fr;
  }
  /* .Readmore_actions {
    --section-content-gap: 42px;
    justify-content: flex-start;
    grid-template-columns: 66.66%;
    width: 100%;
    margin: 0 auto;
    justify-content: center;
  } */
}

/* embed */
.StoriesInfo_left_innercntr-full-width.embed-instagram {
  /* position: relative;
  width: 100%;
  max-width: 100%;
  height: 0;
  overflow: hidden; */
  position: relative;
  aspect-ratio: 4/5;
}
.StoriesInfo_left_innercntr-full-width.embed-instagram iframe {
  height: 100%;
}
.StoriesInfo_left_innercntr-full-width.embed-twitter iframe {
  height: 100%;
}
.StoriesInfo_left_innercntr-full-width.embed-youtube {
  /* position: relative;
  width: 100%;
  max-width: 100%;
  height: 0;
  overflow: hidden; */
  position: relative;
  aspect-ratio: 8/3;
}
.StoriesInfo_left_innercntr-full-width.embed-youtube iframe {
  height: 100%;
}
@media only screen and (max-width: 639px) {
  .StoriesInfo_left_innercntr-full-width.embed-youtube {
    aspect-ratio: 3.5/2;
  }
  .StoriesInfo_left_innercntr-full-width.embed-instagram {
    aspect-ratio: 2/4;
  }
}
/* embed */
