import parse from "html-react-parser";
import { Const } from "./Constants";

export const htmlParser = (data) => {
  return parse(data);
};

export const dateFormateWithTimeShort = (dateString) => {
  const date = new Date(dateString);

  const monthNames = [
    "Jan",
    "Feb",
    "Mar",
    "Apr",
    "May",
    "Jun",
    "Jul",
    "Aug",
    "Sep",
    "Oct",
    "Nov",
    "Dec",
  ];

  const month = monthNames[date.getMonth()];
  const day = date.getDate();
  const year = date.getFullYear();

  let hours = date.getHours();
  const minutes = date.getMinutes();
  const ampm = hours >= 12 ? "PM" : "AM";

  hours = hours % 12 || 12;

  const minutesStr = minutes.toString().padStart(2, "0");

  return `${month} ${day}, ${year}`;
};

export const statusLabel = (value) => {
  let label = "";
  if (value === Const.Inactive) {
    label = "Unpubilled";
  } else if (value === Const.Active) {
    label = "Published";
  } else if (value === Const.Trash) {
    label = "Trash";
  } else if (value === Const.Draft) {
    label = "Draft";
  } else if (value === Const.Scheduled) {
    label = "Scheduled";
  }
  return label;
};

export const hasHtmlTags = (str) => {
  const regex = /<\/?[a-z][\s\S]*>/i;
  return regex.test(str);
};
export const getEmbedType = (url) => {
  const youtubeRegex =
    /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^\/\n\s]+\/\S+\/|(?:v|e(?:mbed)?)\/|\S*?[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/;

  const instagramRegex =
    /(?:https?:\/\/)?(?:www\.)?instagram\.com\/(?:p|tv|reel)\/([A-Za-z0-9_-]+)/;

  const twitterRegex =
    /(?:https?:\/\/)?(?:(?:www\.|platform\.)?(?:twitter|x)\.com\/(?:(?:\w+\/status\/[0-9]+)|(?:embed\/Tweet\.html\?id=[0-9]+)))/;

  // Facebook post or video URLs
  const facebookPostOrVideoRegex =
    /(?:https?:\/\/)?(?:www\.)?facebook\.com\/(?:[^\/\n\s]+\/posts\/|(?:video\.php\?v=|watch\/))([0-9]+)/;

  if (youtubeRegex.test(url)) {
    return "youtube";
  } else if (instagramRegex.test(url)) {
    return "instagram";
  } else if (twitterRegex.test(url)) {
    return "twitter";
  } else if (facebookPostOrVideoRegex.test(url)) {
    return "facebook";
  }
};

export const extractTwitterId = (embedUrl) => {
  const match = embedUrl.split("id=")[1];
  return match;
};

export const generateAstrologySlug = (baseSlug) => {
  // Remove trailing slash from baseSlug if it exists
  const sanitizedBaseSlug = baseSlug.endsWith("/")
    ? baseSlug.slice(0, -1)
    : baseSlug;

  // Get the current date
  const now = new Date();
  const dayOfWeek = now.toLocaleString("en-US", { weekday: "long" });
  const month = now.toLocaleString("en-US", { month: "long" });
  const day = now.getDate();
  const year = now.getFullYear();

  // Construct the dynamic part based on the current date
  const dynamicPart =
    `${"daily-horoscope"}-${dayOfWeek}-${month}-${day}-${year}`.toLowerCase();

  // Concatenate the baseSlug and dynamicPart with a hyphen
  return `${sanitizedBaseSlug}-${dynamicPart}`;
};

export const generateRandomNumber = (digits = 6) => {
  const min = Math.pow(10, digits - 1);
  const max = Math.pow(10, digits) - 1;
  return Math.floor(min + Math.random() * (max - min + 1));
};

export const getAuthorText = (prefix = "By", author = [], contributor = []) => {
  const list = author.length ? author : contributor;
  const name = list[0]?.name || list[0] || "";
  const count = list.length - 1;

  return name ? `${prefix} ${name}${count ? `  +${count} More` : ""}` : "";
};

export const extractTextFromDoc = (doc) => {
  const output = [];

  function extractTextFromContent(contentArray) {
    return (
      contentArray
        ?.map((node) => {
          if (node.type === "text") return node.text || "";
          if (node.content) return extractTextFromContent(node.content);
          return "";
        })
        .join("") || ""
    );
  }

  if (Array.isArray(doc?.content)) {
    for (const node of doc?.content) {
      if (node.type === "paragraph" || node.type === "heading") {
        const text = extractTextFromContent(node.content || []);
        if (text.trim()) output.push(text.trim());
      }
    }
  }

  return output.join(" ");
};

export const convertToISTISOString = (utcISOString) => {
  if (!utcISOString) return;
  const date = new Date(utcISOString || null);

  const istOffsetMs = 5.5 * 60 * 60 * 1000;
  const istDate = new Date(date.getTime() + istOffsetMs);
  return istDate.toISOString().replace("Z", "+05:30");
};

export const escapeXml = (unsafe) => {
  return unsafe
    .replace(/&/g, "&amp;")
    .replace(/</g, "&lt;")
    .replace(/>/g, "&gt;")
    .replace(/"/g, "&quot;")
    .replace(/'/g, "&apos;");
};
