import Link from "next/link";
import React from "react";

const Button = ({ href, target, onClick, children, className = "", ...props }) => {
  const isLink = Boolean(href);

  return (
    <ul className="card-actions_actions">
      <li className="">
        {isLink ? (
          <Link
            href={href}
            target={target}
            className={`card-actions_actions_button ${className}`}
            aria-label=""
            {...props}
          >
            <span className="button_label">{children}</span>
          </Link>
        ) : (
          <button
            onClick={onClick}
            className={`card-actions_actions_button ${className}`}
            aria-label=""
            {...props}
          >
            <span className="button_label">{children}</span>
          </button>
        )}
      </li>
    </ul>
  );
};

export default Button;
