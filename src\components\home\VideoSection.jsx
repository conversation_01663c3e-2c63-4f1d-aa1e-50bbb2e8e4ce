import React, { useEffect } from "react";
import { useGSAP } from "@gsap/react";
import gsap from "gsap";
import Link from "next/link";
import { htmlParser } from "@/utils/Util";

const VideoSection = ({ title, description, data = [] }) => {
  // useEffect(() => {
  //   // Define the function that changes the class
  //   function changing() {
  //     // Only execute if the element is found
  //     const element = document.querySelector(".home-VidoeCards__slider");
  //     if (element && window.innerWidth <= 717) {
  //       element.classList.add(
  //         "owl-carousel",
  //         "owl-theme",
  //         "owl-loaded",
  //         "owl-drag"
  //       );
  //     } else if (element) {
  //       // Remove classes if window width is above 717px
  //       element.classList.remove(
  //         "owl-carousel",
  //         "owl-theme",
  //         "owl-loaded",
  //         "owl-drag"
  //       );
  //     }
  //   }

  //   // Initial check when the component mounts
  //   if (window.innerWidth <= 717) {
  //     changing();
  //   }
  //   // Add event listener for window resize
  //   window.addEventListener("resize", changing);

  //   // Cleanup the event listener when the component unmounts
  //   return () => {
  //     window.removeEventListener("resize", changing);
  //   };
  // }, []); // Empty dependency array ensures this runs once on mount

  function desktop() {
    useGSAP(() => {
      const ctx = gsap.context(() => {
        const tl = gsap.timeline({ repeat: -1, repeatDelay: 0 });
        const duration = 3;

        data.forEach((_, index) => {
          const nextIndex = (index + 1) % data.length;
          tl.to(
            `.home-VidoeCards__item_line${index + 1}`,
            {
              opacity: 1,
              visibility: "visible",
              ease: "power1.inOut",
              onStart: () => {
                if (window.innerWidth >= 717) {
                  toggleClasses(index + 1, true);
                }
              },
            },
            `start${index}`
          )
            .to(
              `.home-VidoeCards__item_line_marker${index + 1}`,
              {
                width: "100%",
                duration,
                ease: "linear",
              },
              `start${index}`
            )
            .to(
              `.home-VidoeCards__item_line${index + 1}`,
              {
                opacity: 0,
                visibility: "hidden",
                ease: "power1.inOut",
                onStart: () => {
                  if (window.innerWidth >= 717) {
                    toggleClasses(index + 1, false);
                  }
                },
              },
              `start${nextIndex}`
            );
        });
      });
      return () => {
        ctx.revert();
      };
    });
    const toggleClasses = (index, isActive) => {
      const item = document.querySelector(`.home-VidoeCards__item${index}`);
      const wrapper = document.querySelector(
        `.home-VidoeCards__item_video-wrapper${index}`
      );
      const preview = document.querySelector(
        `.home-VidoeCards__item_video-preview${index}`
      );

      if (isActive) {
        item?.classList.add("activeBlock");
        wrapper?.classList.add("activeBlockH");
        preview?.classList.add("activeBlockImageNone");
      } else {
        item?.classList.remove("activeBlock");
        wrapper?.classList.remove("activeBlockH");
        preview?.classList.remove("activeBlockImageNone");
      }
    };
  }
  desktop();

  return (
    <>
      {data.length > 0 && (
        <div className="VidoeCards_cntr">
          {/* <div className="container"> */}
          <div className="home-VidoeCards__wrapper">
            <div className="home-VidoeCards__header">
              <h2 className="home-VidoeCards__title headline headline-4">
                <span>{title}</span>
              </h2>
              <div className="home-VidoeCards__text content-block">
                {htmlParser(description)}
              </div>
            </div>
            <div className="home-VidoeCards__slider_wrapperCntr">
              <div className="home-VidoeCards__slider owl-carousel">
                {data.map((item, index) => (
                  <div
                    className={`home-VidoeCards__item home-VidoeCards__item${
                      index + 1
                    }`}
                    key={`video-item-${index}`}
                  >
                    <Link
                      href={`/videos${item?.slug ?? "#"}`}
                      className="home-VidoeCards__item_link"
                    >
                      <div
                        className={`home-VidoeCards__item_line home-VidoeCards__item_line${
                          index + 1
                        }`}
                      >
                        <div
                          className={`home-VidoeCards__item_line_marker home-VidoeCards__item_line_marker${
                            index + 1
                          }`}
                        ></div>
                      </div>
                      <div
                        className={`home-VidoeCards__item_video-wrapper home-VidoeCards__item_video-wrapper${
                          index + 1
                        } lazyloaded`}
                      >
                        <div className="home-VidoeCards__hover-overlay"></div>
                        <img
                          className="home-VidoeCards__hover-arrow ls-is-cached lazyloaded"
                          src="../../../Assets/arrow-standard-white-16px.svg"
                          alt=""
                        />
                        <img
                          className={`home-VidoeCards__item_video-preview home-VidoeCards__item_video-preview${
                            index + 1
                          } ls-is-cached lazyloaded`}
                          src={item?.coverImg ?? ""}
                          alt={item?.altName ?? "Cover Image"}
                        />
                        <video
                          playsInline
                          autoPlay
                          muted
                          loop
                          className="home-VidoeCards__item_video"
                          src={item?.src ?? ""}
                        ></video>
                        <div
                          className={`home-VidoeCards__item_content home-VidoeCards__item_content${
                            index + 1
                          }`}
                        >
                          <div className="home-VideoCards__item_title-wrapper">
                            <h4 className="home-VideoCards__item_title">
                              {item?.category ?? ""}
                            </h4>
                            <p className="home-VideoCards__item_para headline">
                              {item?.title ?? ""}
                            </p>
                          </div>
                        </div>
                      </div>
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          </div>
          {/* </div> */}
        </div>
      )}
    </>
  );
};

export default VideoSection;
