import React from "react";
import { usePathname } from "next/navigation";
import TiptapRendered from "@/components/common/TiptapRenderer";
import Button from "@/components/common/Button";

const MainContent = ({ content, tag }) => {
  const pathname = usePathname();
  const router = pathname.split("/");
  return (
    <>
      <div className="StoriesInfo_leftcntr">
        <TiptapRendered content={content?.content ?? []} router={router} />
        {tag && tag.length > 0 && (
          <div className="">
            <ul className="Readmore_actions">
              <li className="Readmore_actions_first-not-button">
                <span>READ MORE STORIES ABOUT :</span>
              </li>
              <div className="Readmore_actions_inner">
                {tag.map((item, index) => (
                  <Button href={item?.slug ?? "#"} key={`tag-${index}`}>
                    {item?.name ?? ""}
                  </Button>
                ))}
              </div>
            </ul>
          </div>
        )}
      </div>
    </>
  );
};

export default MainContent;
