import Head from "next/head";

const ImageGallerySchema = ({
  title,
  description,
  url,
  datePublished,
  data,
}) => {
  const images = data?.map((item) => ({
    "@type": "ImageObject",
    url: item?.image || "",
  }));
  const schemaData = {
    "@context": "https://schema.org",
    "@type": "ImageGallery",
    url: url,
    datePublished: datePublished,
    mainEntityOfPage: {
      "@type": "WebPage",
      "@id": url,
      headline: title,
      description: description,
    },
    image: images,
  };

  return (
    <Head>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(schemaData) }}
      ></script>
    </Head>
  );
};

export default ImageGallerySchema;
