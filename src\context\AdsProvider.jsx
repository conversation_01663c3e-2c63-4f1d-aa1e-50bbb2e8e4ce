import { useRouter } from "next/router";
import React, { useEffect, useState, useContext, createContext } from "react";

const AdsContext = createContext({
  transitioning: false,
  loadAds: () => {},
  googletag: null,
});

export default function AdsProvider({ children }) {
  const router = useRouter();
  const [googletag, setGoogleTag] = useState(null);
  const [transitioning, setTransitioning] = useState(false);

  useEffect(() => {
    const gptObject = window?.googletag;
    setGoogleTag(gptObject);
  }, []);

  useEffect(() => {
    const handleStart = () => {
      setTransitioning(true);

      if (googletag && googletag.apiReady) {
        googletag.destroySlots();
      }
    };

    const handleComplete = () => {
      setTransitioning(false);
    };

    router.events.on("routeChangeStart", handleStart);
    router.events.on("routeChangeComplete", handleComplete);

    return () => {
      router.events.off("routeChangeStart", handleStart);
      router.events.off("routeChangeComplete", handleComplete);
    };
  }, [googletag, router.events]);

  const loadAds = (id, adUnit, sizes, sizeMapping, targeting) => {
    if (googletag) {
      googletag.cmd.push(function () {
        const currentSlots = googletag.pubads().getSlots();

        const isSlotDefined = currentSlots.some(
          ({ getSlotElementId }) => getSlotElementId() === id
        );

        if (isSlotDefined) return;

        const slot = googletag.defineSlot(adUnit, sizes, id);

        if (!slot) return;

        slot.addService(googletag.pubads());

        if (sizeMapping) {
          const mapping = googletag.sizeMapping();

          sizeMapping.forEach((item) => {
            mapping.addSize(item.viewport, item.sizes);
          });
          const mappingArray = mapping.build();

          slot.defineSizeMapping(mappingArray);
        }

        if (targeting) {
          Object.entries(targeting).forEach(([key, value]) => {
            if (value) {
              slot.setTargeting(key, value);
            }
          });
        }

        googletag.pubads();
        googletag.enableServices();
        googletag.display(slot);
      });
    }
  };

  return (
    <AdsContext.Provider value={{ transitioning, loadAds, googletag }}>
      {children}
    </AdsContext.Provider>
  );
}

export const useAdsContext = () => {
  const context = useContext(AdsContext);

  if (context === undefined) {
    throw new Error("useAdsContext must be used within a AdsProvider");
  }

  return context;
};
