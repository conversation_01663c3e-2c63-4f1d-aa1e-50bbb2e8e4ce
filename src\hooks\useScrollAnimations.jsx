import { useEffect } from "react";
import { useRouter } from "next/router";
import gsap from "gsap";
import ScrollTrigger from "gsap/dist/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);

const useScrollAnimations = (animations) => {
  const router = useRouter();

  useEffect(() => {
    if (window.innerWidth >= 768) {
      const timelines = animations.map(({ trigger, target, startY }) => {
        return gsap
          .timeline({
            scrollTrigger: {
              trigger,
              start: window.innerWidth >= 768 ? "top 160%" : "top 100%",
              end: window.innerWidth >= 768 ? "top -10%" : "top -100%",
              scrub: true,
              // markers: true,
            },
          })
          .to(target, { y: startY });
      });

      return () => {
        timelines.forEach((timeline) => timeline.kill());
        ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
      };
    }
  }, [router.query.subcategory, animations]);
};

export default useScrollAnimations;
