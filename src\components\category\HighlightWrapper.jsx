import React from "react";
import HighlightSection from "@/components/sections/HighlightSection";

const HighlightWrapper = ({ data }) => {
  return (
    <>
      {data && data.length > 0 && (
        <>
          <div className="top">
            <div className="text-foreground color-changer">
              <HighlightSection data={data} />
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default HighlightWrapper;
