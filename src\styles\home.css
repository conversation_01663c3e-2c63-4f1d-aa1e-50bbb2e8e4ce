.hero_sec {
  width: 100%;
  min-height: 100vh;
  padding-top: 30vw;
  background-color: #faf7f3;
  z-index: 9;
  position: relative;
  padding: 5rem 0 5rem 0;
}
.error-text{
  color: red;
}
/* story */
.stories {
  /* background-color: #fff; */
  /* left: 50%; */
  /* margin-bottom: 170px; */
  /* margin-left: -50vw; */
  /* padding-top: 6rem; */
  position: relative;
  /* width: 100vw; */
  /* padding-bottom: 2rem; */
}
.stories .row {
  display: flex;
  flex-flow: row wrap;
  /* margin-left: auto; */
  margin-right: auto;
  /* overflow-x: auto; */
  /* max-width: 106.25rem; */
}
.stories .column,
.stories .columns {
  flex: 1 1 0px;
  min-width: 0;
  /* padding-left: 1.5625rem; */
  /* padding-right: 1.5625rem; */
}
.stories .small-12 {
  flex: 0 0 100%;
  max-width: 100%;
}
.stories .stories__column {
  padding-right: 0;
}
.glide {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  position: relative;
  width: 100%;
}
.glide__slides,
.glide__track {
  /* overflow: hidden; */
}
.glide__slides {
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  list-style: none;
  padding: 0;
  position: relative;
  /* -ms-touch-action: pan-Y; */
  /* touch-action: pan-Y; */
  /* -webkit-transform-style: preserve-3d; */
  /* transform-style: preserve-3d; */
  white-space: nowrap;
  width: 100%;
  will-change: transform;
  overflow-x: auto;
}

.glide__slides::-webkit-scrollbar {
  display: none;
}
.glide__slide {
  -ms-flex-negative: 0;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  flex-shrink: 0;
  height: 100%;
  white-space: normal;
  width: 100%;
  width: 100px;
  margin-right: 10px;
  /* background-color: pink; */
}
.glide__slide a {
  color: #000;
}
.stories__story {
  cursor: pointer;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  /* background-color: red; */
}
.stories__img {
  border: 2px solid #537b8b;
  border-radius: 50%;
  height: 100px;
  padding: 3px;
  width: 100px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stories__img img {
  border-radius: 50%;
  height: 100%;
  -o-object-fit: cover;
  object-fit: cover;
  width: 100%;
  transition: all 1s cubic-bezier(0.19, 1, 0.22, 1);
}
.glide__slide:hover .stories__img img {
  scale: 1.1;
}
.stories h5 {
  font-size: 10px;
  font-weight: 500;
  margin-top: 10px;
  overflow: hidden;
  text-align: center;
  font-family: var(--primary-font-Neue);
  /* white-space: nowrap; */
  letter-spacing: 0.02em;
}
.stories h5 span {
  font-weight: 400;
  padding: 0 7px;
  white-space: normal;
  /* background-color: green; */
  width: 100%;
  text-overflow: ellipsis;
  display: inline-block;
  /* max-height: 2.4em; */
  line-height: 1.2em;
  display: -webkit-box;
  -webkit-line-clamp: 3; /* number of lines to show */
  line-clamp: 2;
  -webkit-box-orient: vertical;
  text-align: center;
}
.glide__slide:nth-last-child(1) .stories__img {
  background-color: rgb(0, 0, 0);
  color: #fff;
}
/* .stories__story:hover .marque span {
  margin-left: -200%;
  transition: all 3s linear;
} */
.left-button_btn_story,
.right-button_btn_story {
  /* display: none; */
  position: absolute;
  cursor: pointer;
  background-color: #faf7f3;
  width: 60px;
  height: 60px;
  border-radius: 50%;
}
.left-button_btn_story img,
.right-button_btn_story img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.left-button_btn_story {
  left: -15px;
  top: 25%;
  transform: rotate(-180deg);
}
.right-button_btn_story {
  right: -5px;
  top: 25%;
}
@media only screen and (max-width: 991px) {
  /* .hero_sec {
    padding-top: 25vw;
  } */
}
@media only screen and (max-width: 767px) {
  /* .hero_sec {
    padding-top: 15vw;
  } */
  .controls {
    display: none;
  }
}
@media only screen and (max-width: 500px) {
  /* .hero_sec {
    padding-top: 30vw;
  } */
}

@media only screen and (min-width: 1024px) {
  body {
    font-size: 16px;
    line-height: 23px;
  }
  .hero_sec {
    padding-top: 30vw;
  }
  .stories {
    /* margin: 0 0 50px 0; */
    /* margin-top: 200px; */
    /* padding-top: 25vw; */
  }
  .stories .stories__column {
    padding-right: 0;
  }
  .glide__slide {
    width: 135px;
  }
  .stories__img {
    height: 130px;
    padding: 4px;
    width: 130px;
  }

  .stories h5 {
    font-size: 14px;
    line-height: 20px;
  }
  .left-button_btn_story,
  .right-button_btn_story {
    display: block;
  }
}
@media only screen and (min-width: 1300px) {
  .hero_sec {
    padding-top: 25vw;
  }
  .stories {
    /* padding-top: 20vw; */
  }
  .glide__slide {
    width: 150px;
  }
  /* .stories__story {
  } */
  .stories__img {
    height: 150px;
    padding: 4px;
    width: 150px;
  }
}
@media only screen and (min-width: 1600px) {
  .hero_sec {
    padding-top: 20vw;
  }
}
@media print, screen and (min-width: 40em) {
  .stories .column,
  .stories .columns {
    padding-left: 1.09375rem;
    padding-right: 1.09375rem;
  }
}
@media print, screen and (min-width: 64em) {
  .stories .column,
  .stories .columns {
    padding-left: 1.09375rem;
    padding-right: 1.09375rem;
  }
}
/* story */

/* home-hero */
.big-container-blog_wrapper {
  position: relative;
  padding: 1rem 0;
}
.big-container-blog {
  /* padding: 40px 0 40px; */
  background-color: #faf7f3;
  /* min-height: 60vh; */
  position: relative;
  /* padding: 0 1rem; */
}
.big-container-blog.k {
  background-color: #faf7f3;
  position: relative;
  /* padding: 0 1rem; */
}
.container {
  width: 100%;
  max-width: 1800px;
  height: 100%;
  padding-right: calc(1.5rem * 0.8);
  padding-left: calc(1.5rem * 0.8);
  margin-right: auto;
  margin-left: auto;
  position: relative;
  /* overflow: hidden; */
  /* overflow: hidden; */
}
.blog-img-container {
  width: 100%;
  /* height: 100vh; */
  /* padding: 20px; */
  /* height: 350px; */
  /* height: 100%; */
  /* height: 80vw; */
  position: relative;
  /* aspect-ratio: 4/2; */
  aspect-ratio: 16/9;
  overflow: hidden;
  /* background-size: cover;
  background-repeat: no-repeat;
  background-position: center center;
  background-image: url(/Assets/Manifest_Lead2.jpg); */
}
.blog-img-container img {
  /* max-width: 100%; */
  /* width: 100%;
  height: 100%; */
  object-fit: cover;
  object-position: top;
  background-repeat: no-repeat;
  /* transition: all 0.5s ease; */
}
.page2.main-container .blog-img-container img {
  opacity: 0;
  background-color: rgba(128, 128, 128, 0.495);
}
.blog-img-container:before {
  position: absolute;
  top: 0px;
  left: 0px;
  opacity: 1;
  z-index: 2;
  width: 100%;
  height: 100%;
  content: "";
  pointer-events: none;
  transition: opacity 0.3s;
  /* background: linear-gradient(to top, rgb(0, 0, 0), transparent 65%) 0% 0% /
    calc(100% + 1px) calc(100% + 1px); */

  /* content: "";
  width: 100%;
  height: 100%;
  background-color: #00000021;
  background: linear-gradient(
    180.53deg,
    rgba(255, 255, 255, 0.4) -100.02%,
    rgb(0 0 0 / 50%) 100.69%
  );
  top: 0;
  left: 0;
  position: absolute; */
}
.blog-banner-text {
  display: flex;
  flex-direction: column;
  /* height: 100%; */
  width: 100%;
  /* justify-content: flex-end; */
  padding: 8px 0;
  position: relative;
  z-index: 4;
  /* padding: 32px; */
  position: relative;
  top: 0;
  color: #000;
  background-color: #faf7f3;
  gap: 5px;
}
.blog-banner-text strong {
  /* color: #fff; */
  font-size: 12px;
  display: block;
  text-transform: uppercase;
  font-family: var(--primary-font-Neue);
  font-weight: 700;
  letter-spacing: 0.03em;
}
.big-container-blog .blog-banner-text .slug {
  font-weight: 500;
  font-size: 2rem;
  width: 100%;
  /* color: #fff; */
  /* font-family: var(--primary-font-scotch-display); */
  font-family: var(--primary-font-scotch-display-compress);
  /* font-family: var(--primary-font-scotch-display-compress); */
  /* margin: 8px 0;  */
  line-height: 1;
  display: block;
  font-style: normal;
}
.big-container-blog .blog-banner-text-desc {
  font-family: var(--primary-font-Neue);
  width: 100%;
  /* font-style: italic; */
  /* color: #fff; */
  font-weight: 400;
  /* margin: 1rem 0; */
  /* letter-spacing: 0.02em; */
  line-height: 1.3;
  font-size: 14px;
}
.date-author {
  font-weight: 600;
  /* color: #fff; */
  font-family: var(--primary-font-Neue-Light);
  /* letter-spacing: 0.1em; */
  font-style: normal;
  font-size: 12px;
  white-space: pre;
}
.date-author a {
  /* color: #fff; */
  font-weight: 800;
}
@media only screen and (min-width: 768px) {
  /* .big-container-blog {
    min-height: 100vh;
  } */
  /* .blog-img-container {
    height: 70vw;
  } */

  .big-container-blog .blog-banner-text .slug {
    font-size: 2rem;
    width: 100%;
  }
  .big-container-blog .blog-banner-text-desc {
    width: 70%;
    font-size: 1rem;
  }
  .blog-banner-text strong {
    font-size: 15px;
  }
  .date-author {
    font-size: 15px;
  }
}
@media only screen and (min-width: 992px) {
  .big-container-blog.k {
    padding: 0;
    height: 100%;
  }
  .big-container-blog_wrapper {
    padding: 3rem 0;
  }
  .big-container-blog {
    padding: 0;
  }

  .big-container-blog .blog-banner-text-desc {
    font-size: 1.2rem;
  }
  /* .big-container-blog {
    padding: ;
  } */
  .blog-banner-text {
    position: absolute;
    color: #fff;
    padding: 40px 2rem;
    background-color: #ffffff00;
    justify-content: flex-end;
    /* height: 100%; */
    bottom: 0;
    gap: 15px;
  }
  /* .blog-img-container {
    height: 100vh;
  } */
  .blog-img-container {
    /* height: 100vh;
    aspect-ratio: unset; */
  }
  .blog-img-container:before {
    background: linear-gradient(to top, rgb(0, 0, 0), transparent 65%) 0% 0% /
      calc(100% + 1px) calc(100% + 1px);
  }
}
@media only screen and (min-width: 1024px) {
  /* .big-container-blog {
    min-height: 60vh;
  } */
}
@media only screen and (min-width: 1300px) {
  /* .big-container-blog {
    min-height: 100vh;
  } */
  .blog-img-container img {
    object-position: top;
  }
}
@media only screen and (min-width: 992px) and (max-width: 1300px) {
  .container {
    padding: 0 30px !important;
    max-width: 1650px;
    padding-right: 0;
    padding-left: 0;
  }
  .Navbar_wrapper {
    grid-template-columns: 1fr 2fr;
  }

  .big-container-blog .blog-banner-text .slug {
    font-size: 2rem;
    width: 70%;
  }
  .big-container-blog .blog-banner-text-desc {
    width: 50%;
  }
}
@media only screen and (min-width: 1301px) and (max-width: 1400px) {
  .Navbar_wrapper {
    grid-template-columns: 1fr 2fr !important;
  }
  .container {
    max-width: 1400px;
    padding: 0 40px;
    /* padding-right: 0; */
    /* padding-left: 0; */
  }
  /* .blog-img-container {
    padding: 50px;
  } */
  .big-container-blog .blog-banner-text .slug {
    font-size: 3rem;
    width: 80%;
  }
  .big-container-blog .blog-banner-text-desc {
    width: 60%;
  }
}
@media only screen and (min-width: 1401px) and (max-width: 1650px) {
  .container {
    max-width: 1400px;
    padding-right: 0;
    padding-left: 0;
  }
  /* .blog-img-container {
    padding: 50px;
  } */
  .big-container-blog .blog-banner-text .slug {
    font-size: 3rem;
    width: 60%;
  }
  .big-container-blog .blog-banner-text-desc {
    width: 50%;
  }
  .date-author {
    font-size: 15px;
  }
  .relatedCard figure img {
    height: 200px;
  }
  .GridCardContainer {
    gap: 30px;
  }
}
@media only screen and (min-width: 1651px) {
  .container {
    max-width: 1650px;
  }
  /* .blog-img-container {
    padding: 50px;
  } */
  .big-container-blog .blog-banner-text .slug {
    font-size: 3rem;
    width: 50%;
  }
  .big-container-blog .blog-banner-text-desc {
    width: 45%;
    line-height: 1.2;
  }
}
/* home-hero */

/* yellow card */
.Yellow_bgCard_cntr {
  position: relative;
  width: 100%;
  /* height: 100vh; */
  overflow: hidden;
  padding: 1rem 0;
  background-color: #faf7f3;
}
.relatedPostHeading {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 15px;
  justify-content: space-between;
}
.relatedPostHeading_left {
  display: grid;
  flex: 1;
  gap: 12px;
}
.relatedPostHeading_left_title {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 18px;
  letter-spacing: -0.025em;
  /* line-height: 2; */
  /* font-size: 1.5rem; */
}
.relatedPostHeading_left_title h2 {
  font-family: var(--primary-font-scotch-display-compress);
  font-weight: 600;
  font-size: 2rem;
  font-style: normal;
  line-height: 1;
  letter-spacing: 0.02em;
}
.card-actions_actions {
  display: flex;
  flex-basis: auto;
  flex-wrap: wrap;
  align-items: center;
  list-style: none;
  gap: 10px;
  font-family: var(--primary-font-Neue-Light);
  position: relative;
}
.card-actions_actions span {
  font-size: 12px;
  font-family: var(--primary-font-Neue);
  letter-spacing: 0.02em;
  font-weight: 600;
  z-index: 9;
  transition: all 0.3s ease;
}
.card-actions_actions .card-actions_actions_button {
  width: 100%;
}
.card-actions_actions_button {
  position: relative;
  z-index: 0;
  display: inline-flex;
  height: 35px;
  /* width: 44px; */
  padding: 0 15px;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: 44px;
  gap: 12px;
  text-overflow: ellipsis;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1.5px solid #000000; /* Default border */
  transition: all 0.3s ease; /* Smooth transition for border color */
  cursor: pointer;
  overflow: hidden;
  color: #000;
  background-color: #faf7f3;
  line-height: 1;
}

.card-actions_actions_button:before {
  position: absolute;
  /* z-index: -1; */
  /* border: 1px solid; */
  border-radius: 0;
  content: "";
  /* inset: 0; */
  opacity: 1;
  transition: all 0.3s ease; /* Ensure transition is smooth */
  top: 100%;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
}
.card-actions_actions_button:hover {
  color: #fff;
  /* border: 1.5px solid #faf7f3; */
}
.card-actions_actions_button:hover:before {
  /* opacity: 0; */
  /* border: 1px solid rgb(189, 184, 184); */
  top: 0;
}
.button_label {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 1rem;
}
button {
  border: none;
  background: none;
  color: inherit;
}
.arrows_arrow {
  position: relative;
  z-index: 0;
  display: inline-flex;
  width: 45px;
  height: 45px;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  text-overflow: ellipsis;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border: 1px solid #000;
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
  color: #000;
  background-color: transparent;
}

.arrows_arrow:before {
  position: absolute;
  border-radius: 0;
  content: "";
  opacity: 1;
  transition: all 0.3s ease;
  top: 100%;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #000000;
}
.arrows_arrow:hover:before {
  top: 0;
}

.arrows_arrow:hover:before .arrows_arrow svg {
  color: #fff;
}

.arrows_arrow svg {
  position: relative;
  z-index: 1;
  opacity: 1 !important;
  font-size: 16px;
  transition: color 0.3s ease;
}

.arrows_arrow:hover,
.arrows_arrow:hover svg {
  color: #fff;
}

/* .relatedPostHeading h3 {
  font-size: 55px;
  line-height: 45px;
  margin-bottom: 20px;
  font-weight: bold;
} */
.followInsta {
  display: flex;
  align-items: center;
  gap: 20px;
}
.followInsta a {
  display: flex;
  align-items: center;
  gap: 20px;
  color: #fff;
  text-decoration: none;
}
.followInsta img {
  width: 40px;
}

img.lazy {
  min-height: 1px;
  transition: all 0.5s ease;
}
.GridCardContainer {
  position: relative;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  /* padding: 5rem 0; */
  row-gap: 40px;
  column-gap: 30px;
}
.author-link {
  color: inherit;
}
.relatedCard {
  /* width: 100%;
  height: 100%; */

  /* background-color: aquamarine; */
  /* display: flex; */
  /* flex-direction: column; */
}

.relatedCard figure {
  overflow: hidden;
  margin: 0 0 8px;
  /* height: 60vw; */
  /* height: 80vw; */
  aspect-ratio: 1;
  position: relative;
}
.relatedCard figure img {
  /* width: 100%;
  height: 100%; */
  object-fit: cover;
  object-position: center;
}
/* .relatedCard figure img {
  height: auto !important;
} */
.relatedCard:hover img.lazy {
  transform: scale(1.05);
}
.relatedPostContent {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1;
  gap: 8px;
}
.relatedPostContent strong {
  font-size: 12px;
  /* margin-bottom: 10px; */
  display: block;
  text-transform: uppercase;
  /* font-family: var(--primary-font-scotch-display); */
  font-family: var(--primary-font-Neue);
  font-weight: 700;
  letter-spacing: 0.03em;
  color: #000;
}
.relatedPostContent h2 {
  font-size: 1.2rem;
  line-height: 1;
  /* margin-bottom: 15px; */
  text-align: flex-start;
  /* font-family: var(--primary-font-scotch-display); */
  font-family: var(--primary-font-scotch-display-compress);
  font-weight: 600;
  letter-spacing: 0.02em;
  color: #000;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}
.relatedPostContent .date-author {
  color: #000;
  text-align: flex-start;
  font-weight: 600;
}
@media only screen and (min-width: 424px) and (max-width: 500px) {
  /* .relatedCard figure {
    height: 75vw;
  } */
}
@media only screen and (min-width: 768px) {
  .relatedPostContent strong {
    font-size: 15px;
  }
  .card-actions_actions span {
    font-size: 12px;
  }
  /* .relatedCard figure {
    height: 40vw;
  } */
  .relatedPostContent h2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
  }
  .Yellow_bgCard_cntr {
    padding: 1rem 0;
  }
}
@media only screen and (max-width: 991px) {
  .GridCardContainer {
    grid-template-columns: repeat(2, 1fr);
    /* gap: 20px; */
  }
}
@media only screen and (min-width: 992px) {
  .relatedPostContent {
    gap: 5px;
  }
  .relatedPostContent h2 {
    padding: 0 3rem 0 0;
    font-size: 22px;
    line-height: 1.2;
  }
  .relatedPostHeading_left_title h2 {
    font-size: 2.5rem;
  }
  .GridCardContainer {
    grid-template-columns: repeat(2, 1fr);
    /* gap: 20px; */
  }
  /* .relatedCard figure {
    height: 25vw;
  } */
  .relatedPostHeading {
    margin-bottom: 30px;
  }
}
@media only screen and (min-width: 1024px) {
  .Yellow_bgCard_cntr {
    padding: 2rem 0 4rem;
  }
  .relatedPostContent h2 {
    padding: 0 0rem 0 0;
  }
  .GridCardContainer {
    grid-template-columns: repeat(4, 1fr);
    row-gap: 40px;
  }
  /* .relatedCard figure {
    height: 19vw;
  } */
}
@media only screen and (min-width: 1600px) {
  /* .relatedCard figure {
    height: 16vw;
  } */
}
/* yellow card */

/* VidoeCards_cntr */
.VidoeCards_cntr {
  position: relative;
  padding: 3rem 0;
  background-color: #faf7f3;
}
.home-VidoeCards__wrapper {
  display: flex;
}
.home-VidoeCards__header {
  width: 33.8%;
  padding-right: 90px;
}
.home-VidoeCards__title {
  margin-bottom: 24px;
  font-size: 78px;
  font-weight: 900;
  /* line-height: 90%; */
  font-family: var(--primary-font-scotch-display-normal);
  font-style: normal;
}
.content-block {
  font-size: 19px;
  line-height: 120%;
  font-family: var(--primary-font-Neue);
  letter-spacing: 0.03em;
  font-style: normal;
  font-weight: 400;
}

.home-VidoeCards__slider {
  /* width: 66.2%; */
  display: flex;
  justify-content: space-between;
}
.home-VidoeCards__slider_wrapperCntr {
  position: relative;
  height: 100%;
  width: 100%;
}
.home-VidoeCards__item {
  transition: all 0.2s ease;
  width: 26.7%;
}
/* active */
.activeBlock {
  width: 42.8%;
}
.home-VidoeCards__item_line {
  opacity: 0;
  visibility: hidden;
  height: 6px;
  border-radius: 8px;
  margin-bottom: 10px;
  background-color: #141414;
  overflow: hidden;
}
.home-VidoeCards__item_line_marker {
  width: 0%;
  border-radius: 8px;
  height: 100%;
  background-color: #537b8b;
  /* transition: width 0.4s linear; */
}
.home-VidoeCards__item_video-wrapper {
  height: 340px;
  position: relative;
  border-radius: 8px;
  background-image: url(https://d1mx1c7bm5ya3h.cloudfront.net/wp-content/uploads/Frame-1726-1-1-480x713.png);
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}
.activeBlockH {
  height: 554px;
}
.home-VidoeCards__hover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 8px;
  background: rgb(0 0 0 / 37%);
  z-index: 1;
  opacity: 0;
  transition: all 0.3s;
}
.home-VidoeCards__hover-arrow {
  width: 16px;
  height: 16px;
  pointer-events: none;
  left: 20px;
  top: 20px;
  z-index: 1;
  position: absolute;
  opacity: 0;
  transition: all 0.3s;
}
.home-VidoeCards__item_video-wrapper:hover .home-VidoeCards__hover-overlay {
  opacity: 1;
}
.home-VidoeCards__item_video-wrapper:hover .home-VidoeCards__hover-arrow {
  opacity: 1;
}
.home-VidoeCards__item_video-preview {
  position: absolute;
  border-radius: 8px;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  object-fit: cover;
}
.activeBlockImageNone {
  display: none;
}
.home-VidoeCards__item_video {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.home-VidoeCards__item_content {
  position: absolute;
  left: 12px;
  bottom: 12px;
  z-index: 2;
}
/* .activeBlockVideoCards_text {
  left: 1.389vw;
  bottom: 1.389vw;
} */
.home-VideoCards__item_title {
  font-size: 1rem;
  color: #fff;
  line-height: 100%;
  transition: all 0.2s ease;
  font-family: var(--primary-font-Neue-Light);
  letter-spacing: 0.03em;
  font-weight: 700;
}
.home-VideoCards__item_para {
  font-size: 1rem;
  color: #fff;
  line-height: 100%;
  transition: all 0.2s ease;
  margin: 10px 0;
  font-family: var(--primary-font-Neue);
  letter-spacing: 0.02em;
}
/* .activeBlockitem_para {
  font-size: 3.611vw;
} */
@media only screen and (max-width: 1020px) {
  /* .home-VidoeCards__slider.owl-carousel {
    height: 33vw !important;
  } */
  .home-VidoeCards__header {
    width: 100%;
    flex: 0 0 100%;
    padding-right: 0;
    margin: 0 0 10px;
    display: flex;
  }
  .home-VidoeCards__title {
    flex: 0 0 50%;
    font-size: 2.5rem;
    line-height: 0.9;
  }
  .home-VidoeCards__wrapper {
    flex-wrap: wrap;
  }
  .home-VidoeCards__slider {
    width: 100%;
    flex: 0 0 100%;
  }
}
@media only screen and (max-width: 717px) {
  .home-VidoeCards__slider.owl-carousel {
    gap: 20px;
    position: relative;
    display: flex;
    height: 554px !important;
    overflow-x: scroll;
    scroll-snap-type: x mandatory;
  }
  .home-VidoeCards__item {
    height: 100%;
    width: 100%;
    flex: 0 0 90%;
    scroll-snap-align: start;
  }
  .home-VidoeCards__item_video-wrapper {
    height: calc(100% - 16px);
  }
}

@media only screen and (max-width: 670px) {
  .home-VidoeCards__slider.owl-carousel {
    height: 470px !important;
  }
  .home-VidoeCards__header {
    flex-wrap: wrap;
  }
  .home-VidoeCards__title {
    flex: 0 0 100%;
    margin: 0 0 20px;
  }
  .home-VidoeCards__text {
    flex: 0 0 100%;
    font-size: 18px;
  }
  .home-VidoeCards__title {
    font-size: 2rem;
  }
}
@media only screen and (min-width: 1020px) {
  .home-VidoeCards__header {
    padding-right: 11rem;
  }

  .home-VidoeCards__title {
    margin-bottom: 1.6rem;
    font-size: 3rem;
  }
  .content-block {
    font-size: 1.1vw;
  }

  .home-VidoeCards__item_line {
    height: 0.417vw;
    border-radius: 0.556vw;
    margin-bottom: 0.694vw;
  }
  .home-VidoeCards__item_line_marker {
    border-radius: 0.556vw;
  }
  .home-VidoeCards__hover-arrow {
    width: 1.111vw;
    height: 1.111vw;
    left: 1.389vw;
    top: 1.389vw;
  }
  .home-VidoeCards__item_video-wrapper {
    height: 23.611vw;
    border-radius: 0.556vw;
  }
  .home-VidoeCards__item_video-preview {
    border-radius: 0.556vw;
  }
  .home-VidoeCards__item_video {
    border-radius: 0.556vw;
  }
  .activeBlockH {
    height: 37.778vw;
  }
  .home-VidoeCards__item_content {
    left: 0.833vw;
    bottom: 0.833vw;
  }
  /* .home-VideoCards__item_para {
    font-size: 1.083vw;
  } */
  .home-VideoCards__item_para {
    font-size: 1.2vw;
  }
}
/* VidoeCards_cntr */

/* Celebrities */
.Celebrities_wrapper {
  position: relative;
  background-color: #faf7f3;
  /* height: 100vh; */
  /* padding: 100px 0; */
  /* padding: 3rem 0; */
}
.celeb-grid {
  /* position: relative; */
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  grid-column-gap: 40px;
}
.celeb-right {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.celeb-left {
  position: relative;
}

.celeb-left img {
  transition: transform 0.6s;
}

.celeb-grid img {
  /* width: 100%;
  height: 100%; */
  object-fit: cover;
}

.celeb-multi-left {
  /* background-color: red; */
  /* height: 1840px; */
}
.celeb-multi-right {
  background-color: rgb(0, 200, 255);
  /* height: 3080px; */
}
.more-trend-wrap {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 20px 1rem;
  z-index: 5;
  /* font-family: var(--primary-font-Neue-Bold); */
  color: #fff;
  gap: 10px;
  line-height: 1;
  background: linear-gradient(to bottom, transparent 30%, black);
  /* background: linear-gradient(to top, rgb(0, 0, 0), transparent 65%) 0% 0% /
    calc(100% + 1px) calc(100% + 1px); */
}
.more-trend-wrap strong {
  font-size: 12px;
  display: block;
  text-transform: uppercase;
  font-family: var(--primary-font-Neue);
  font-weight: 700;
  letter-spacing: 0.03em;
}
.more-trend-wrap .slug {
  /* font-family: var(--primary-font-scotch-display); */
  font-family: var(--primary-font-scotch-display-compress);
  font-size: 1.6rem;
  line-height: 1;
  font-weight: 400;
  /* margin-bottom: 10px; */
  color: #fff;
  text-align: flex-start;
  /* font-style: italic; */
}
.blog-banner-text-desc {
  font-family: var(--primary-font-Neue);
  /* letter-spacing: 0.02em; */
  line-height: 1.2;
  font-size: 12px;
  /* display: none; */
}
.more-trend-wrap a {
  display: flex;
  text-decoration: none;
  color: #fff;
  grid-column-gap: 6px;
  font-weight: 700;
  font-size: 1.5rem;
  line-height: 1;
  justify-content: flex-end;
}
.more-trend-wrap img {
  width: 3rem;
  filter: invert(1);
}
@media only screen and (min-width: 768px) {
  .celeb-multi-grid {
    /* grid-template-columns: repeat(1, 1fr); */
    gap: 30px;
  }
  .more-trend-wrap {
    padding: 40px 2rem;
  }
  .more-trend-wrap .slug {
    font-size: 2.5rem;
    line-height: 1;
  }
  .blog-banner-text-desc {
    display: initial;
    font-size: 1.2rem;
  }
}
@media only screen and (min-width: 992px) {
  .more-trend-wrap {
    gap: 15px;
  }
}
@media only screen and (min-width: 1024px) {
  .celeb-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .celeb-multi-grid {
    /* grid-template-columns: repeat(1, 1fr); */
  }
  .more-trend-wrap {
    padding: 40px 2rem;
  }
  .more-trend-wrap a {
    font-size: 1.2rem;
  }
  .more-trend-wrap h3 {
    text-align: flex-start;
  }
  .more-trend-wrap svg {
    width: 1.4rem;
  }
}
@media (min-width: 1200px) {
  .celeb-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .celeb-multi-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .more-trend-wrap h3 {
    font-size: 1.75rem;
  }
}
@media only screen and (min-width: 1280px) {
}
/* Celebrities */
@media only screen and (min-width: 1401px) {
  .more-trend-wrap {
    padding: 40px 2rem;
  }
}
/* FollowUp cntr */
.follow-on-yt {
  background-color: #00000000;
  padding: 0 0 5rem 0rem;
  height: 100%;
  overflow: hidden;
  position: relative;
  height: auto;
  /* display: flex;
  align-items: center;
  justify-content: center; */
  height: 100% !important;
  /* color: #fff; */
}
.follow-on-yt_inner {
  background-color: #000000;
  height: 100%;
  padding: 2rem 0;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.follow-on-yt .followInsta {
  justify-content: flex-end;
  font-family: var(--primary-font-Neue-Light);
  font-weight: 600;
  letter-spacing: 0.1em;
}
.follow-yt-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.follow-yt-icon img {
  width: 100px;
  height: auto;
  /* filter: invert(1); */
}
.vid-arrow-wrap {
  display: flex;
  justify-content: space-between;
  margin: 40px 0;
}
.vid-arrow-wrap span {
  background-color: #fff;
  border: 1px solid #000;
  padding: 4px 10px;
  align-self: baseline;
  font-family: var(--primary-font-Neue);
  text-transform: uppercase;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.5s ease;
}
.vid-arrow-wrap span:hover {
  background-color: #000000;
  color: #fff;
  border: 1px solid #ffffff;
}
.vid-arrow-wrap img {
  width: 74px;
  height: auto;
  filter: invert(1);
}
.text-image-img-wrap {
  display: grid;
  grid-template-columns: 1fr 1fr 0fr;
  grid-column-gap: 1rem;
  align-items: center;
}
.yt-text-left {
  padding-bottom: 30px;
}
.yt-text-left h2 {
  font-family: var(--primary-font-scotch-display-compress);
  font-weight: 400;
  line-height: 1;
  color: #ffffff;
  margin: 15px 0;
  /* font-style: italic; */
}
.yt-text-left p {
  font-family: var(--primary-font-Neue);
  color: #ffffff;
  /* font-size: 28px; */
  font-style: normal;
  width: 96%;
  line-height: 1.2;
  margin-bottom: 0;
}
.yt-img-wrap {
  position: relative;
  /* aspect-ratio: 5/3; */
  aspect-ratio: 16/9;
  overflow: hidden;
}
.yt-img-wrap img {
  width: 100%;
  object-fit: cover;
  object-position: center;
}

@media only screen and (max-width: 991px) {
  .follow-on-yt {
    padding: 0 0 1rem 0;
  }
  .follow-on-yt .followInsta {
    /* justify-content: flex-start; */
    margin-bottom: 10px;
  }
  .follow-yt-icon img {
    width: 50px;
  }
  .vid-arrow-wrap img {
    width: 70px;
  }
  .yt-text-left h2 {
    font-size: 2.8rem;
  }
  .yt-text-left p {
    font-size: 1.5rem;
    line-height: 1;
  }
  .text-image-img-wrap {
    grid-template-columns: 100%;
    grid-column-gap: 0;
  }
}
@media only screen and (max-width: 600px) {
  .follow-on-yt .followInsta {
    font-size: 12px;
  }
  .yt-text-left h2 {
    font-size: 2rem;
  }
  .yt-text-left p {
    font-size: 1rem;
  }
  .GridCardContainer {
    grid-template-columns: repeat(1, 1fr);
    gap: 30px;
    /* margin: 0 0 !important; */
  }
}
@media only screen and (min-width: 992px) and (max-width: 1200px) {
  .follow-on-yt_inner {
    height: 100vh;
    padding: 10rem 0;
  }
  .yt-text-left h2 {
    font-size: 3.5rem;
    line-height: 1;
  }
  .yt-text-left p {
    font-size: 19px;
  }
}
@media only screen and (min-width: 1201px) and (max-width: 1300px) {
  .follow-on-yt {
    height: 100vh;
    padding: 2rem;
  }
  .yt-text-left h2 {
    font-size: 3rem;
    line-height: 1;
  }
  .yt-text-left p {
    font-size: 26px;
  }
  .GridCardContainer {
    gap: 30px;
  }
}
@media only screen and (min-width: 1301px) and (min-width: 1440px) {
  .follow-on-yt_inner {
    height: 100vh;
    padding: 8rem 10rem;
  }
  .yt-text-left h2 {
    font-size: 4rem;
  }
  .yt-text-left p {
    font-size: 26px;
  }
}
@media only screen and (min-width: 1301px) {
  .follow-on-yt_inner {
    height: 100vh;
    padding: 10rem 0rem;
  }
  .yt-text-left h2 {
    font-size: 4rem;
  }
  .yt-text-left p {
    font-size: 22px;
  }
  .yt-img-wrap {
    aspect-ratio: 3.5/2;
  }
}
@media (orientation: landscape) {
  /* Your styles for landscape orientation */
  .follow-on-yt_inner {
    height: 100% !important;
  }
}
@media only screen and (min-width: 1601px) {
  .yt-text-left h2 {
    font-size: 5rem;
  }
}
/* FollowUp cntr */
/* NewsContact */
.fullscreen-newsletter {
  position: relative;
  display: grid;
  align-items: center;
}
.fullscreen-newsletter {
  padding: 0 20px;
  overflow: hidden;
  /* min-height: 500px; */
  /* padding-top: 100px; */
  /* padding-bottom: 100px; */
  /* background-color: #000000; */
  /* box-shadow: 0 0 70px 100px var(); */
  /* color: #ffffff; */
  padding: 3rem 1rem;
}

.fullscreen-newsletter_wrapper {
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
  gap: 32px;
  color: #000000;
}
.fullscreen-newsletter_subtitle {
  flex-basis: 100%;
  text-align: center;
}
.text-5 {
  font-size: 24px;
  letter-spacing: 0.02em;
  letter-spacing: 0;
  line-height: 1.25;
  font-family: var(--primary-font-scotch-display);
}
.fullscreen-newsletter_content {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 32px;
}
.fullscreen-newsletter_title {
  text-align: center;
  font-family: var(--primary-font-scotch-display);
  font-size: 24px;
  letter-spacing: -0.02em;
  line-height: 1.25;
}
/* .newsletter_title_rich-text_wrapper {

} */
.fullscreen-newsletter_newsletter {
  flex-shrink: 0;
}
form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  position: relative;
}
.newsletter-form_newsletter {
  display: grid;
  max-width: 500px;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
}
.input_input {
  --height: 44px;
  display: inline-flex;
  width: 100%;
  min-width: 0;
  height: var(--height);
  justify-content: center;
  padding: 0 28px;
  border: 1px solid;
  border-radius: calc(var(--height) / 2);
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background: none;
  color: rgb(0, 0, 0);
  transition: border-color 0.3s ease;
}
.text-8 {
  font-family: var(--primary-font-Neue);
  font-size: 14px;
  letter-spacing: 0;
  line-height: 1.25;
  transition: all 1s cubic-bezier(0.19, 1, 0.22, 1);
  letter-spacing: 0.1em;
  font-weight: 300;
}
.text-8:hover {
  outline: none !important;
  /* border: 1.2px solid; */
  border-color: #696969;
  /* box-shadow: 0 0 10px #ffffff; */
}
.newsletter-form_newsletter [type="email"] {
  grid-column: 1 / 3;
}
.newsletter-form_bottom {
  display: grid;
  grid-column: 1 / 3;
  /* grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)); */
  /* gap: 12px; */
}
.button_button {
  position: relative;
  z-index: 0;
  display: inline-flex;
  height: 44px;
  width: 100%;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  border-radius: 44px;
  gap: 12px;
  text-overflow: ellipsis;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  background-color: #000000;
  color: #ffffff;
  cursor: pointer;
}
.button_has-text {
  padding: 0 28px;
}
.indicator_msg {
  font-family: var(--primary-font-Neue);
  /* color: rgb(220, 56, 56); */
  color: rgb(0, 0, 0);
  padding: 5px 0;
  text-align: center;
}
@media only screen and (min-width: 640px) {
  .fullscreen-newsletter {
    /* --v-padding: 100px; */
    padding: 20px;
    /* min-height: 600px; */
  }
  .fullscreen-newsletter_wrapper {
    gap: 48px;
  }
  .fullscreen-newsletter_content {
    align-items: center;
    gap: 48px;
  }
  .text-3 {
    font-size: 40px;
  }
}
@media only screen and (min-width: 768px) {
  .fullscreen-newsletter {
    /* --v-padding: 100px; */
    padding: 40px;
    min-height: 600px;
  }
  .fullscreen-newsletter_newsletter {
    width: 500px;
  }
  .text-3 {
    font-size: 45px;
  }
}
@media only screen and (min-width: 1024px) {
  .fullscreen-newsletter {
    min-height: 500px;
    padding: 100px;
  }
  .fullscreen-newsletter_wrapper {
    flex-direction: row;
    justify-content: space-between;
  }
  .fullscreen-newsletter_subtitle,
  .fullscreen-newsletter_title_ {
    text-align: unset;
  }
  .text-5 {
    font-size: 28px;
  }
  .fullscreen-newsletter_content {
    flex-direction: row;
    align-items: unset;
    gap: 96px;
    text-align: initial;
  }
  .fullscreen-newsletter_title {
    text-align: unset;
  }
  .text-3 {
    font-size: 42px;
  }
}
@media only screen and (min-width: 1300px) {
  .fullscreen-newsletter {
    padding: 100px;
  }
  .text-3 {
    font-size: 45px;
  }
}
@media only screen and (min-width: 1600px) {
  .fullscreen-newsletter {
    padding: 200px 0;
  }
  .fullscreen-newsletter_content {
    gap: 120px;
  }
  .fullscreen-newsletter_wrapper {
    max-width: 1100px;
    margin: 0 auto;
  }
}
/* NewsContact */
/* new celeb */
.Celebrities_wrapper,
.Celebrities_wrapper2 {
  /* height: 100vh; */
  width: 100%;
  margin: 0px 0;
  overflow: hidden;
  position: relative;
  background-color: #faf7f3;
  /* margin: 5rem 0; */
  /* margin-bottom: 10rem; */
}

.Celebrities_wrapper .celeb-grid,
.Celebrities_wrapper2 .celeb-grid {
  /* height: 100%; */
  width: 100%;
  /* background-color: blue; */
  /* display: flex; */
  /* justify-content: center; */
  /* gap: 1.5rem; */
  /* position: relative; */
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  grid-column-gap: 30px;
  grid-row-gap: 1rem;
  /* padding: 32px 0; */
}
.celeb-multi-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 30px;
}
@media only screen and (min-width: 1024px) {
  .Celebrities_wrapper .celeb-grid,
  .Celebrities_wrapper2 .celeb-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-row-gap: 2rem;
  }
  .Celebrities_wrapper,
  .Celebrities_wrapper2 {
    height: 100vh;
  }
  .celeb-multi-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (min-width: 1200px) {
  .Celebrities_wrapper .celeb-grid,
  .Celebrities_wrapper2 .celeb-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .celeb-multi-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media only screen and (max-width: 600px) {
  .celeb-multi-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}
.Celebrities_wrapper .celeb-left,
.Celebrities_wrapper2 .celeb-left {
  /* height: 100%; */
  /* width: 50%; */
  /* display: flex; */
  /* justify-content: center; */
  /* align-items: end; */
  overflow: hidden;
  position: relative;
}
.celeb_StickyColumn {
  display: flex;
  /* position: sticky; */
  /* max-height: 100vh; */
  width: 100%;
  margin-bottom: auto;
  top: 0;
  height: 100%;
  cursor: pointer;
}
/* .celeb_StickyColumn::after {
  position: absolute;
  bottom: 0px;
  left: 0px;
  opacity: 1;
  z-index: 2;
  width: 100%;
  height: 30%;
  content: "";
  pointer-events: none;
  transition: opacity 0.3s;
  background: linear-gradient(to top, rgb(0, 0, 0), #00000026 100%) 0% 0%;
} */
/* .celeb_StickyColumn:hover::after {
  opacity: 0.7;
} */
.Celebrities_wrapper .leftslider,
.Celebrities_wrapper2 .leftslider {
  width: 100%;
  /* height: 100vh; */
  /* height: 50vh; */
  cursor: pointer;
  transition: all ease 0.5s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 9;
  aspect-ratio: 1;
}

.Celebrities_wrapper .image,
.Celebrities_wrapper2 .image {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  transition: all ease 0.3s;
}
.Celebrities_wrapper .image img,
.Celebrities_wrapper2 .image img {
  width: 100%;
  height: 100%;
  object-position: top;
  object-fit: cover;
}
.Celebrities_wrapper .middleside,
.Celebrities_wrapper .rightside {
  position: relative;
  overflow: hidden;
  height: 100vh;
}
.Celebrities_wrapper .middleside .middlesideinner,
.Celebrities_wrapper2 .middleside .middlesideinner,
.Celebrities_wrapper .rightside .rightsideinner,
.Celebrities_wrapper2 .rightside .rightsideinner {
  display: flex;
  flex-direction: column;
  row-gap: 1.5rem;
  position: absolute;
  top: 0;
  left: 0;
}

.Celebrities_wrapper .middlecard,
.Celebrities_wrapper2 .middlecard2,
.Celebrities_wrapper .rightcard,
.Celebrities_wrapper2 .rightcard2 {
  /* height: 100%; */
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  flex-direction: column;
  /* gap: 1rem; */
}

.Celebrities_wrapper .rightcard figure,
.Celebrities_wrapper .middlecard figure,
.Celebrities_wrapper2 .middlecard2 figure,
.Celebrities_wrapper2 .rightcard2 figure {
  overflow: hidden;
  margin: 0 0 0.5rem;
  /* height: 40vw; */
  position: relative;
  width: 100%;
  aspect-ratio: 1;
}
.Celebrities_wrapper .middlecard:hover img,
.Celebrities_wrapper2 .middlecard2:hover img {
  /* scale: 1.1; */
}
.Celebrities_wrapper .rightcard:hover img,
.Celebrities_wrapper2 .rightcard2:hover img {
  /* scale: 1.1; */
  overflow: hidden;
}
@media only screen and (min-width: 768px) {
  /* .Celebrities_wrapper .leftslider,
  .Celebrities_wrapper2 .leftslider {
    height: 100vh;
  } */
  /* .Celebrities_wrapper .leftslider,
  .Celebrities_wrapper2 .leftslider {
    height: 75vw;
  } */
  /* .Celebrities_wrapper .leftslider, .Celebrities_wrapper2 .leftslider{
    height: 100vh;
    width: 100%;
  } */
}
@media only screen and (min-width: 1024px) {
  .Celebrities_wrapper .leftslider,
  .Celebrities_wrapper2 .leftslider {
    height: 100vh;
  }
  .Celebrities_wrapper .rightcard figure,
  .Celebrities_wrapper .middlecard figure,
  .Celebrities_wrapper2 .middlecard2 figure,
  .Celebrities_wrapper2 .rightcard2 figure {
    /* height: 20vw; */
  }
}
@media only screen and (min-width: 1440px) {
  .Celebrities_wrapper .leftslider,
  .Celebrities_wrapper2 .leftslider {
    height: 100vh;
  }
  .Celebrities_wrapper .rightcard figure,
  .Celebrities_wrapper .middlecard figure,
  .Celebrities_wrapper2 .middlecard2 figure,
  .Celebrities_wrapper2 .rightcard2 figure {
    /* height: 20vw; */
  }
}
@media only screen and (min-width: 1901px) {
  .Celebrities_wrapper .rightcard figure,
  .Celebrities_wrapper .middlecard figure,
  .Celebrities_wrapper2 .middlecard2 figure,
  .Celebrities_wrapper2 .rightcard2 figure {
    /* height: 15vw; */
  }
}
/* new celeb */
