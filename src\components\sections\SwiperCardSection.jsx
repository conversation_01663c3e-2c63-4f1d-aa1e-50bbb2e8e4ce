import React, { useEffect, useRef, useState } from "react";
import Card from "@/components/cards/Card";
import { Swiper, SwiperSlide } from "swiper/react";
import { SlArrowLeft } from "react-icons/sl";
import { SlArrowRight } from "react-icons/sl";
import { FreeMode, Pagination } from "swiper/modules";
import "swiper/css/free-mode";
import "swiper/css/pagination";

const SwiperCardSection = ({ title, data }) => {
  const swiperRef = useRef(null);

  const [isAtStart, setIsAtStart] = useState(true);
  const [isAtEnd, setIsAtEnd] = useState(false);

  // Handle scrolling left (previous slide)
  const handleScrollLeft = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  // Handle scrolling right (next slide)
  const handleScrollRight = () => {
    if (swiperRef.current && swiperRef.current.swiper) {
      swiperRef.current.swiper.slideNext();
    }
  };

  useEffect(() => {
    const swiperInstance = swiperRef.current?.swiper;

    // Ensure swiper instance exists
    if (swiperInstance) {
      const updateArrowVisibility = () => {
        setIsAtStart(swiperInstance.isBeginning);
        setIsAtEnd(swiperInstance.isEnd);
      };

      // Initial arrow visibility update
      updateArrowVisibility();

      // Listen to slide change events
      swiperInstance.on("slideChange", updateArrowVisibility);

      // Cleanup the event listener when the component unmounts
      return () => {
        swiperInstance.off("slideChange", updateArrowVisibility);
      };
    }
  }, []);

  return (
    <>
      {data && data.length > 0 && (
        <>
          <div className="Yellow_bgCard_cntr">
            {/* <div className="container"> */}
            <div className="relatedPostHeading">
              <div className="relatedPostHeading_left">
                <div className="relatedPostHeading_left_title">
                  <h2>{title}</h2>
                </div>
              </div>
              <div>
                <div style={{ display: "flex", gap: "10px" }}>
                  <button
                    aria-label="Previous story"
                    className="arrows_arrow"
                    onClick={handleScrollLeft}
                    disabled={isAtStart}
                  >
                    <SlArrowLeft style={{ opacity: isAtStart ? 0 : 1 }} />
                  </button>
                  <button
                    aria-label="Previous story"
                    className="arrows_arrow"
                    onClick={handleScrollRight}
                    disabled={isAtEnd}
                  >
                    <SlArrowRight style={{ opacity: isAtStart ? 0 : 1 }} />
                  </button>
                </div>
              </div>
            </div>
            <div className="" style={{ overflow: "hidden" }}>
              <Swiper
                ref={swiperRef}
                modules={[Pagination, FreeMode]}
                // spaceBetween={10}
                className="mySwiper"
                breakpoints={{
                  1024: {
                    slidesPerView: 4,
                    spaceBetween: 30,
                  },
                  992: {
                    slidesPerView: 3,
                    spaceBetween: 30,
                  },
                  768: {
                    slidesPerView: 2,
                    spaceBetween: 20,
                  },
                  600: {
                    slidesPerView: 2.25,
                    spaceBetween: 20,
                  },
                  0: {
                    slidesPerView: 1.25,
                    spaceBetween: 20,
                  },
                }}
              >
                {data.map((item, index) => {
                  return (
                    <>
                      <SwiperSlide key={`swiper-card-item-${index}`}>
                        <Card
                          title={item?.title ?? ""}
                          image={
                            item?.croppedImg
                              ? item?.croppedImg
                              : item?.coverImg
                              ? item?.coverImg
                              : ""
                          }
                          altName={item?.altName ?? ""}
                          category={item?.category ?? ""}
                          slug={item?.slug ?? "#"}
                          timestamp={item?.timestamp ?? ""}
                          author={item?.author ?? []}
                          contributor={item?.contributor ?? []}
                        />
                      </SwiperSlide>
                    </>
                  );
                })}
              </Swiper>
            </div>
            {/* </div> */}
          </div>
        </>
      )}
    </>
  );
};

export default SwiperCardSection;
