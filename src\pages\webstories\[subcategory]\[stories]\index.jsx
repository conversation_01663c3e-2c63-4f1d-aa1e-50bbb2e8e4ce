import React, { useEffect } from "react";
import { getWebStories } from "@/pages/api/WebStoriesApi";
import { useRouter } from "next/router";
import Head from "next/head";

import { htmlParser } from "@/utils/Util";
import { dateFormateWithTimeShort } from "@/utils/Util";
import { initializeAmpRuntime, handleAmpBackNavigation } from "@/utils/ampUtils";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import ImageGallerySchema from "@/components/seo/ImageGallerySchema";
import MediaGallerySchema from "@/components/seo/MediaGallerySchema";
import { Const } from "@/utils/Constants";
import { webStoryDetailCSS } from "@/components/amp/ampCss";

export const config = { amp: true };

const WebStoryDetail = ({ data, previousData, nextData, breadcrumbs, meta }) => {
	const router = useRouter();
	const { pathname } = router;

	// Ensure AMP runtime is properly initialized
	useEffect(() => {
		initializeAmpRuntime();
	}, []);

	return (
		<>
			<style jsx>{webStoryDetailCSS}</style>
			<Head>
				<meta name="amp-to-amp-navigation" content="AMP-Redirect" />
				<script
					async
					custom-element="amp-story"
					src="https://cdn.ampproject.org/v0/amp-story-1.0.js"
				></script>
				<script
					async
					custom-element="amp-bind"
					src="https://cdn.ampproject.org/v0/amp-bind-0.1.js"
				></script>
			</Head>

			<SeoHeader meta={meta} />
			<BreadcrumbSchema itemList={breadcrumbs} />
			<ImageGallerySchema
				title={data?.slides?.[0]?.title || ""}
				description={data?.slides?.[0]?.description || ""}
				url={Const.ClientLink + pathname}
				datePublished={data?.timestamp || ""}
				data={data?.slides || []}
			/>
			<MediaGallerySchema
				title={data?.slides?.[0]?.title || ""}
				description={data?.slides?.[0]?.description || ""}
				data={data?.slides || []}
			/>

			<amp-state id="storyState">
				<script
					type="application/json"
					dangerouslySetInnerHTML={{
						__html: JSON.stringify({
							currentSlide: 0,
							totalSlides: data?.slides?.length || 0,
							hasPrevious: !!previousData?.slug,
							hasNext: !!nextData?.slug,
						}),
					}}
				/>
			</amp-state>

			<amp-story
				standalone
				title={data?.title || ""}
				publisher="Manifest"
				publisher-logo-src="/logo.png"
				poster-portrait-src={data?.coverImg || ""}
			>
				{data?.slides?.map((slide, index) => (
					<amp-story-page key={index} id={`page-${index}`}>
						<amp-story-grid-layer template="fill">
							<amp-img
								src={slide?.image || ""}
								width="720"
								height="1280"
								layout="fill"
								alt={slide?.altName || ""}
							></amp-img>
						</amp-story-grid-layer>

						{index === 0 && (
							<amp-story-grid-layer template="vertical">
								<div className="brand-logo">
									<h2>Manifest</h2>
								</div>
							</amp-story-grid-layer>
						)}

						<amp-story-grid-layer template="vertical" className="story-content">
							<div className="story-text">
								<h1>{slide?.title || ""}</h1>
								{slide?.description && (
									<div dangerouslySetInnerHTML={{ __html: slide.description }} />
								)}
								{slide?.contributor?.length > 0 && (
									<small>Photo Credit: {slide.contributor.join(", ")}</small>
								)}
								{index === 0 && slide?.timestamp && (
									<small>Published: {dateFormateWithTimeShort(slide.timestamp)}</small>
								)}
							</div>
						</amp-story-grid-layer>
					</amp-story-page>
				))}
				{/* Navigation buttons outside story pages */}
				{/* {previousData?.slug && (
					<div className="nav-arrow nav-prev">
						<a href={previousData.slug}>
							<span>‹</span>
						</a>
					</div>
				)}
				{nextData?.slug && (
					<div className="nav-arrow nav-next">
						<a href={nextData.slug}>
							<span>›</span>
						</a>
					</div>
				)} */}
			</amp-story>
		</>
	);
};

export default WebStoryDetail;
WebStoryDetail.config = { amp: true };

export async function getServerSideProps(context) {
	const { stories } = context.params;
	const url = `/${stories}`;
	try {
		const storiesRes = await getWebStories(url);

		if (!storiesRes || Object.keys(storiesRes.data).length === 0) {
			return {
				notFound: true,
			};
		}

		const storyData = storiesRes.data.current.data;
		const newObject = {
			title: storyData.title,
			description: "",
			image: storyData.coverImg,
			altName: storyData.altName,
			sequence: -1,
			contributor: [],
			timestamp: storyData.timestamp,
		};

		if (Array.isArray(storyData.slides)) {
			storyData.slides.unshift(newObject);
		}

		return {
			props: {
				data: storyData ?? {},
				previousData: storiesRes.data.previous ?? {},
				nextData: storiesRes.data.next ?? {},
				breadcrumbs: storiesRes.data.current.breadcrumbs ?? [],
				tag: storiesRes.data.current.tag ?? [],
				meta: storiesRes.data.current.meta ?? {},
			},
		};
	} catch (error) {
		console.error("Error fetching data:", error.message);
		return {
			notFound: true,
		};
	}
}
