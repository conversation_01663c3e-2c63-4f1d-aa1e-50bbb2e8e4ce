import React from "react";
import Link from "next/link";
import Image from "next/image";

const HighlightSection = ({ data }) => {
  const highlightData = data && data.length > 0 ? data[0] : {};
  return (
    <>
      {highlightData && (
        <>
          <div className="follow-on-yt">
            <div className="follow-on-yt_inner">
              <div className="container">
                <div className="follow-yt-icon">
                  <Image
                    className="lazy loaded"
                    src="/Assets/star.jpg"
                    // data-src="../../../public/Assets/star.jpg"
                    alt="instagram"
                    data-was-processed="true"
                    // fill
                    width={1000}
                    height={50}
                    sizes="(max-width: 768px) 100vw, 33vw"
                  />
                  <div className="followInsta">
                    <Link
                      target="_blank"
                      href={"https://www.instagram.com/manifest.ind/"}
                      rel="noopener noreferrer"
                    >
                      <div>
                        <span>Follow on Insta</span> <br />
                        <span>@manifest.ind</span>
                      </div>
                    </Link>
                  </div>
                </div>
                <div className="vid-arrow-wrap">
                  <span>{highlightData?.category ?? ""}</span>
                  {/* <Link
                    href={highlightData?.slug ?? "#"}
                    className="arrow_image_cntr"
                  >
                    <Image
                      className="lazy loaded"
                      alt="icon"
                      src="https://www.manifestmagazine.in/wp-content/themes/manifest/images/right-arrow.svg"
                      data-src="https://www.manifestmagazine.in/wp-content/themes/manifest/images/right-arrow.svg"
                      data-was-processed="true"
                      // fill
                      width={100}
                      height={50}
                    />
                  </Link> */}
                </div>
                <Link
                  className="text-image-img-wrap"
                  href={highlightData?.slug ?? "#"}
                >
                  <div className="yt-text-left">
                    <h2>{highlightData?.title ?? ""}</h2>
                    <p>{highlightData?.excerpt}</p>
                  </div>
                  <div className="yt-img-wrap">
                    <Image
                      className="lazy loaded"
                      src={
                        highlightData?.coverImg
                          ? highlightData?.coverImg
                          : highlightData?.croppedImg
                          ? highlightData?.croppedImg
                          : ""
                      }
                      // data-src={
                      //   highlightData?.croppedImg
                      //     ? highlightData?.croppedImg
                      //     : highlightData?.coverImg
                      //     ? highlightData?.coverImg
                      //     : ""
                      // }
                      alt={highlightData?.altName}
                      data-was-processed="true"
                      fill
                    />
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default HighlightSection;
