import React from "react";
import { usePathname } from "next/navigation";
import { Ads } from "@/components/ads/Ads";

const Advertisement = ({ isAstrology = false }) => {
  const pathname = usePathname();
  const router = pathname.split("/");
  return (
    <>
      <div
        className={`StoriesInfo_rightcntr ${
          isAstrology ? "sidebar_advert_start" : ""
        }`}
      >
        <div className="article-sidebar sticky-post">
          <Ads
            id="div-gpt-ad-stories-rhs"
            style={{ margin: "0px auto" }}
            adUnits={[
              {
                adUnit: "/23290324739/Manifest-Desktop-RHS-300",
                sizes: [[300, 600]],
                sizeMapping: [
                  {
                    viewport: [0, 0],
                    sizes: [[300, 600]],
                  },
                ],
                minWidth: 1024,
                maxWidth: Infinity,
              },
              {
                adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                sizes: [
                  [300, 250],
                  [300, 100],
                ],
                sizeMapping: [
                  {
                    viewport: [1023, 0],
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                  },
                ],
                minWidth: 768,
                maxWidth: 1023,
              },
              {
                adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                sizes: [
                  [300, 250],
                  [300, 100],
                ],
                sizeMapping: [
                  {
                    viewport: [767, 0],
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                  },
                ],
                minWidth: 0,
                maxWidth: 767,
              },
            ]}
            targeting={{
              section: [router?.[1] || null],
              "sub-section": [router?.[2] || null],
            }}
          />
          {/* <h1 style={{ color: "gray" }}>ADVERTISEMENT</h1>
          <img
            loading="lazy loaded"
            decoding="async"
            src="https://www.manifestmagazine.in/wp-content/themes/manifest/images/verticlebnner.jpg"
            alt=""
          /> */}
        </div>
      </div>
    </>
  );
};

export default Advertisement;
