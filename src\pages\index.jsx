import React from "react";
import { useRouter } from "next/router";
import Se<PERSON>Header from "@/components/seo/SeoHeader";
import WebStories from "@/components/home/<USER>";
import SwiperCardSection from "@/components/sections/SwiperCardSection";
// import VideoSection from "@/components/home/<USER>";
import GlimpseSection from "@/components/home/<USER>";
import Banner from "@/components/home/<USER>";
import HighlightSection from "@/components/sections/HighlightSection";
import { getFlaggedPage } from "@/pages/api/HomeApi";
import TransparentSection from "@/components/common/TransparentSection";
import { Ads } from "@/components/ads/Ads";
// import StaticAds from "@/components/ads/StaticAds";

const Home = ({ meta, data }) => {
  const router = useRouter();
  const bannerData = data[1]?.stories.slice(0, 1) ?? [];
  const keepExploringData = data[1]?.stories.slice(1) ?? [];
  return (
    <>
      <SeoHeader meta={meta} />
      <div className="hero_sec">
        <div className="container">
          <Ads
            id="div-gpt-ad-homepage-mobile-top"
            style={{ margin: "30px auto" }}
            adUnits={[
              {
                adUnit: "/23290324739/Manifest-Mobile-Top-320",
                sizes: [
                  [320, 50],
                  [320, 100],
                ],
                sizeMapping: [
                  {
                    viewport: [1023, 0],
                    sizes: [
                      [320, 50],
                      [320, 100],
                    ],
                  },
                ],
                minWidth: 768,
                maxWidth: 1023,
              },
              {
                adUnit: "/23290324739/Manifest-Mobile-Top-320",
                sizes: [
                  [320, 50],
                  [320, 100],
                ],
                sizeMapping: [
                  {
                    viewport: [767, 0],
                    sizes: [
                      [320, 50],
                      [320, 100],
                    ],
                  },
                ],
                minWidth: 0,
                maxWidth: 767,
              },
            ]}
            targeting={{
              section: ["home"],
              "sub-section": [null],
            }}
          />
          <WebStories
            data={data[0]?.stories ?? []}
            seeAll={data[0]?.seeAll ?? "/webstories"}
          />
          {/* <StaticAds
            adUnits={[
              {
                adImagePath: "/ads/super-leaderboard-ad.jpeg",
                adSize: [970, 90],
                minWidth: 1024,
                maxWidth: Infinity,
              },
              {
                adImagePath: "/ads/super-leaderboard-ad.jpeg",
                adSize: [728, 90],
                minWidth: 768,
                maxWidth: 1023,
              },
              {
                adImagePath: "/ads/leaderboard-ad.png",
                adSize: [300, 250],
                minWidth: 0,
                maxWidth: 767,
              },
            ]}
          /> */}
          <Ads
            id="div-gpt-ad-homepage-top"
            style={{ margin: "50px auto 0px" }}
            adUnits={[
              {
                adUnit: "/23290324739/Manifest-Desktop-TOP-970",
                sizes: [[970, 90]],
                sizeMapping: [
                  {
                    viewport: [0, 0],
                    sizes: [[970, 90]],
                  },
                ],
                minWidth: 1024,
                maxWidth: Infinity,
              },
              {
                adUnit: "/23290324739/Manifest-Mobile-Top-300",
                sizes: [
                  [300, 100],
                  // [320, 100],
                ],
                sizeMapping: [
                  {
                    viewport: [1023, 0],
                    sizes: [[300, 100]],
                  },
                ],
                minWidth: 768,
                maxWidth: 1023,
              },
              {
                adUnit: "/23290324739/Manifest-Mobile-Top-300",
                sizes: [[300, 100]],
                sizeMapping: [
                  {
                    viewport: [767, 0],
                    sizes: [[300, 100]],
                  },
                ],
                minWidth: 0,
                maxWidth: 767,
              },
            ]}
            targeting={{
              section: ["home"],
              "sub-section": [null],
            }}
          />
          <Banner data={bannerData} />
          <SwiperCardSection
            title={"Keep Exploring"}
            data={keepExploringData}
          />
          {/* <VideoSection
            title={"Videos"}
            description={"Interviews, BTS, Insider Access, Highlights and more"}
            data={data[2]?.stories ?? []}
          /> */}
          <GlimpseSection
            data={data[3]?.stories ?? []}
            triggerId={"glimpseSection1"}
            mbltriggerClass={"celeb-multi-grid"}
          />
          {/* <StaticAds
            adUnits={[
              {
                adImagePath: "/ads/ad2.jpg",
                adSize: [970, 250],
                minWidth: 1024,
                maxWidth: Infinity,
              },
              {
                adImagePath: "/ads/ad2.jpg",
                adSize: [970, 250],
                minWidth: 768,
                maxWidth: 1023,
              },
              {
                adImagePath: "/ads/leaderboard-ad.png",
                adSize: [300, 250],
                minWidth: 0,
                maxWidth: 767,
              },
            ]}
          /> */}
          <Ads
            id="div-gpt-ad-homepage-middle"
            style={{ margin: "50px auto 0px" }}
            adUnits={[
              {
                adUnit: "/23290324739/Manifest-Desktop-TOP-970",
                sizes: [[970, 250]],
                sizeMapping: [
                  {
                    viewport: [0, 0],
                    sizes: [[970, 250]],
                  },
                ],
                minWidth: 1024,
                maxWidth: Infinity,
              },
              {
                adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                sizes: [
                  [300, 250],
                  [300, 100],
                ],
                sizeMapping: [
                  {
                    viewport: [1023, 0],
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                  },
                ],
                minWidth: 768,
                maxWidth: 1023,
              },
              {
                adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                sizes: [
                  [300, 250],
                  [300, 100],
                ],
                sizeMapping: [
                  {
                    viewport: [767, 0],
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                  },
                ],
                minWidth: 0,
                maxWidth: 767,
              },
            ]}
            targeting={{
              section: ["home"],
              "sub-section": [null],
            }}
          />
          <Banner data={data[4]?.stories ?? []} />
        </div>

        <HighlightSection data={data[5]?.stories ?? []} />
        <div className="container">
          <GlimpseSection
            data={data[6]?.stories ?? []}
            triggerId={"glimpseSection2"}
          />
          {/* <StaticAds
            classAdd={"addstyles"}
            adUnits={[
              {
                adImagePath: "/ads/ad1 (1).jpg",
                adSize: [970, 250],
                minWidth: 1024,
                maxWidth: Infinity,
              },
              {
                adImagePath: "/ads/ad1 (1).jpg",
                adSize: [970, 250],
                minWidth: 768,
                maxWidth: 1023,
              },
              {
                adImagePath: "/ads/leaderboard-ad.png",
                adSize: [300, 250],
                minWidth: 0,
                maxWidth: 767,
              },
            ]}
          /> */}
          <Ads
            id="div-gpt-ad-homepage-bottom"
            style={{ margin: "50px auto 0px" }}
            adUnits={[
              {
                adUnit: "/23290324739/Manifest-Desktop-Bottom-300",
                sizes: [[300, 250]],
                sizeMapping: [
                  {
                    viewport: [0, 0],
                    sizes: [[300, 250]],
                  },
                ],
                minWidth: 1024,
                maxWidth: Infinity,
              },
              {
                adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                sizes: [
                  [300, 100],
                  [300, 250],
                ],
                sizeMapping: [
                  {
                    viewport: [1023, 0],
                    sizes: [
                      [300, 100],
                      [300, 250],
                    ],
                  },
                ],
                minWidth: 768,
                maxWidth: 1023,
              },
              {
                adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                sizes: [
                  [300, 100],
                  [300, 250],
                ],
                sizeMapping: [
                  {
                    viewport: [767, 0],
                    sizes: [
                      [300, 100],
                      [300, 250],
                    ],
                  },
                ],
                minWidth: 0,
                maxWidth: 767,
              },
            ]}
            targeting={{
              section: ["home"],
              "sub-section": [null],
            }}
          />
        </div>
      </div>
      <TransparentSection />
    </>
  );
};

export default Home;

export async function getServerSideProps() {
  try {
    const response = await getFlaggedPage("/");
    if (response.status === "success") {
      const { sections, meta } = response.data;

      return {
        props: {
          data: sections && sections.length > 0 ? sections : [],
          meta: meta ?? {},
        },
      };
    }
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      props: {
        data: {},
        meta: {
          title:
            "Manifest India - Wedding News, Celebrity Weddings, Tips &amp; Trends",
          description:
            "Manifest India is your source for wedding news, celebrity wedding updates, fashion trends, wedding skincare, rituals and more.",
          keywords: [],
          author: "RPSG Media",
          robots: "index,follow",
        },
      },
    };
  }
}
