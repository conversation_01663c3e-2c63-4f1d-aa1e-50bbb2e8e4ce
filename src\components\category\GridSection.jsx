import React from "react";
import Card from "@/components/cards/Card";

const GridSection = ({ data, paddingbtm, paddingtop }) => {
  return (
    <>
      {data && data?.length > 0 && (
        <>
          <div className={`container5 ${paddingbtm} ${paddingtop}`}>
            <div className="bg-background text-foreground color-changer">
              <div className="GridCardContainer" style={{ overflow: "hidden" }}>
                {data?.map((item, index) => (
                  <Card
                    key={`item-${index}`}
                    title={item?.title ?? ""}
                    image={
                      item?.croppedImg
                        ? item?.croppedImg
                        : item?.coverImg
                        ? item?.coverImg
                        : ""
                    }
                    altName={item?.altName ?? ""}
                    category={item?.category ?? ""}
                    slug={item?.slug ?? ""}
                    timestamp={item?.timestamp ?? ""}
                    author={item?.author ?? []}
                    contributor={item?.contributor ?? []}
                  />
                ))}
              </div>
            </div>
          </div>
        </>
      )}
    </>
  );
};

export default GridSection;
