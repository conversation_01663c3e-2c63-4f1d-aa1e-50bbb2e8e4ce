import React, { useEffect, useState } from "react";
import gsap from "gsap";
import { useRouter } from "next/router";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import Hero from "@/components/category/Hero";
import BannerWrapper from "@/components/category/BannerWrapper";
import GridSection from "@/components/category/GridSection";
import HighlightWrapper from "@/components/category/HighlightWrapper";
import Button from "@/components/common/Button";
import { Const } from "@/utils/Constants";
import { getCategory, getSubmenus } from "@/pages/api/CategoryApi";
import TransparentSection from "@/components/common/TransparentSection";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import { Ads } from "@/components/ads/Ads";

gsap.registerPlugin(ScrollTrigger);
const Category = ({
  meta,
  title,
  initialData,
  initialCount,
  breadcrumbs,
  submenus,
}) => {
  const router = useRouter();
  const [data, setData] = useState(initialData);
  const [heading, setHeading] = useState(title);
  const [totalCount, setTotalCount] = useState(initialCount);
  const [hasMore, setHasMore] = useState(initialData.length < initialCount);
  const [loading, setLoading] = useState(false);
  const [offset, setOffset] = useState(Const.Limit);
  const [mainCntrArray, setMainCntrArray] = useState([]);
  const timelines = [];

  useEffect(() => {
    setHeading(title);
    setData(initialData);
    setTotalCount(initialCount);
    setOffset(Const.Limit);
    setHasMore(initialCount > initialData.length);
  }, [router.query.category]);

  const handleLoadMore = async () => {
    if (!hasMore || loading) return;

    setLoading(true);

    const payload = {
      slug: `/${router.query.category}`,
      limit: Const.Limit,
      offset: offset,
    };

    try {
      const response = await getCategory(payload);
      const newData = response?.data?.data || [];
      const newTotalCount = response?.data?.count || 0;

      setData((prevData) => [...prevData, ...newData]);
      setTotalCount(newTotalCount);
      setOffset((prevOffset) => prevOffset + Const.Limit);
      setHasMore(data.length + newData.length < newTotalCount);
    } catch (error) {
      console.error("Error loading more data:", error);
    } finally {
      setLoading(false);
    }
  };

  const [bannerData1, bannerData2, bannerData3] = [
    data.slice(0, 1),
    data.slice(9, 10),
    data.slice(18, 19),
  ];

  const [cardGridData1, cardGridData2, cardGridData3] = [
    data.slice(1, 9),
    data.slice(10, 18),
    data.slice(19),
  ];
  useEffect(() => {
    // Only run this on pages where the window width is >= 992
    if (window.innerWidth >= 992) {
      const MainCntr = document.querySelectorAll(".main-container");

      if (MainCntr.length > 0 && mainCntrArray.length === 0) {
        setMainCntrArray([...MainCntr]);
      }

      // Delay animation setup until the DOM is updated (use requestAnimationFrame)
      setTimeout(() => {
        requestAnimationFrame(() => {
          if (mainCntrArray.length > 0) {
            MainCntr.forEach((container) => {
              const topElement = container.querySelector(".top");
              if (topElement) {
                const tl = gsap.timeline({
                  scrollTrigger: {
                    trigger: container,
                    scroller: "body",
                    start: "top 0%",
                    end: "top -80%",
                    scrub: 0.5,
                    pin: true,
                    pinSpacer: false,
                  },
                });
                timelines.push(tl);
                tl.to(topElement, { top: "-100%", duration: 1 }, "a");
              }
            });
          } else {
            console.log("page not found");
          }
        });
        ScrollTrigger.refresh();
        ScrollTrigger.sort();
      }, 1000);
    }

    return () => {
      timelines.forEach((tl) => tl.kill());
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, [mainCntrArray]);

  useEffect(() => {
    const titleheadingAnim = document.querySelector(".h1-header h1");
    const BannerImg = document.querySelectorAll(
      ".page2.main-container .blog-img-container img"
    );
    const SubCat = document.querySelector(".category-sub-cat ul");

    const animation = gsap.timeline();

    animation
      .to(
        SubCat,
        {
          opacity: 1,
          duration: 2,
          ease: "linear",
        },
        "a"
      )
      .to(
        titleheadingAnim,
        {
          duration: 1.5,
          transform: "translateY(0)",
          ease: "power4",
          // stagger: 0.08, // Uncomment if stagger is needed
        },
        "a"
      );

    // Apply animation to each image
    BannerImg.forEach((img) => {
      animation.to(
        img,
        {
          opacity: 1,
          duration: 1.5,
          delay: 0.5,
        },
        "a"
      );
    });

    // Listen for route changes (optional, to ensure effects on each route change)
    const handleRouteChange = () => {
      animation.restart(); // Restart the animation on route change
    };

    router.events.on("routeChangeStart", handleRouteChange);

    return () => {
      router.events.off("routeChangeStart", handleRouteChange); // Cleanup listener
      animation.kill();
    };
  }, [router.events]);
  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div id="page-wrapper">
        <div id="page-content">
          <Hero title={heading} submenus={submenus} />
          <div>
            <Ads
              id="div-gpt-ad-category-top"
              style={{ margin: "0px auto 50px" }}
              adUnits={[
                {
                  adUnit: "/23290324739/Manifest-Desktop-TOP-970",
                  sizes: [[970, 90]],
                  sizeMapping: [
                    {
                      viewport: [0, 0],
                      sizes: [[970, 90]],
                    },
                  ],
                  minWidth: 1024,
                  maxWidth: Infinity,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Top-320",
                  sizes: [
                    [320, 50],
                    [320, 100],
                  ],
                  sizeMapping: [
                    {
                      viewport: [1023, 0],
                      sizes: [
                        [320, 50],
                        [320, 100],
                      ],
                    },
                  ],
                  minWidth: 768,
                  maxWidth: 1023,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Top-320",
                  sizes: [
                    [320, 50],
                    [320, 100],
                  ],
                  sizeMapping: [
                    {
                      viewport: [767, 0],
                      sizes: [
                        [320, 50],
                        [320, 100],
                      ],
                    },
                  ],
                  minWidth: 0,
                  maxWidth: 767,
                },
              ]}
              targeting={{
                section: [router?.query?.category || null],
                "sub-section": [null],
              }}
            />
          </div>
          <section className="all-page-section">
            {bannerData1.length !== 0 && cardGridData1.length !== 0 ? (
              <div className="page2 main-container">
                {bannerData1.length > 0 && <BannerWrapper data={bannerData1} />}
                {cardGridData1.length > 0 && (
                  <GridSection data={cardGridData1} />
                )}
              </div>
            ) : null}
            <div>
              <Ads
                id="div-gpt-ad-category-middle"
                style={{ margin: "0px auto 50px" }}
                adUnits={[
                  {
                    adUnit: "/23290324739/Manifest-Desktop-TOP-970",
                    sizes: [[970, 250]],
                    sizeMapping: [
                      {
                        viewport: [0, 0],
                        sizes: [[970, 250]],
                      },
                    ],
                    minWidth: 1024,
                    maxWidth: Infinity,
                  },
                  {
                    adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                    sizeMapping: [
                      {
                        viewport: [1023, 0],
                        sizes: [
                          [300, 250],
                          [300, 100],
                        ],
                      },
                    ],
                    minWidth: 768,
                    maxWidth: 1023,
                  },
                  {
                    adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                    sizeMapping: [
                      {
                        viewport: [767, 0],
                        sizes: [
                          [300, 250],
                          [300, 100],
                        ],
                      },
                    ],
                    minWidth: 0,
                    maxWidth: 767,
                  },
                ]}
                targeting={{
                  section: [router?.query?.category || null],
                  "sub-section": [null],
                }}
              />
            </div>
            {bannerData2.length !== 0 && cardGridData2.length !== 0 ? (
              <div className="page2 main-container">
                {bannerData2.length > 0 && <BannerWrapper data={bannerData2} />}
                {cardGridData2.length > 0 && (
                  <GridSection data={cardGridData2} />
                )}
              </div>
            ) : null}

            {bannerData3.length !== 0 && cardGridData3.length !== 0 ? (
              <div className="page2 main-container">
                {bannerData3.length > 0 && (
                  <HighlightWrapper data={bannerData3} />
                )}
                {cardGridData3.length > 0 && (
                  <GridSection
                    paddingbtm={"page2lastcntr"}
                    data={cardGridData3.slice(0, 8)}
                  />
                )}
              </div>
            ) : null}
            <div className={cardGridData3.length > 8 ? "see_more_section" : ""}>
              <GridSection
                paddingtop={"paddingtop"}
                data={cardGridData3.slice(8)}
              />
              <div>
                <Ads
                  id="div-gpt-ad-category-bottom"
                  style={{ margin: "0px auto 50px" }}
                  adUnits={[
                    {
                      adUnit: "/23290324739/Manifest-Desktop-Bottom-300",
                      sizes: [[300, 250]],
                      sizeMapping: [
                        {
                          viewport: [0, 0],
                          sizes: [[300, 250]],
                        },
                      ],
                      minWidth: 1024,
                      maxWidth: Infinity,
                    },
                    {
                      adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                      sizes: [
                        [300, 100],
                        [300, 250],
                      ],
                      sizeMapping: [
                        {
                          viewport: [1023, 0],
                          sizes: [
                            [300, 100],
                            [300, 250],
                          ],
                        },
                      ],
                      minWidth: 768,
                      maxWidth: 1023,
                    },
                    {
                      adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                      sizes: [
                        [300, 100],
                        [300, 250],
                      ],
                      sizeMapping: [
                        {
                          viewport: [767, 0],
                          sizes: [
                            [300, 100],
                            [300, 250],
                          ],
                        },
                      ],
                      minWidth: 0,
                      maxWidth: 767,
                    },
                  ]}
                  targeting={{
                    section: [router?.query?.category || null],
                    "sub-section": [null],
                  }}
                />
              </div>
              {hasMore && (
                <div className="flex-all">
                  <Button onClick={handleLoadMore} disabled={loading}>
                    {loading ? "Loading..." : "SEE MORE"}
                  </Button>
                </div>
              )}
            </div>
          </section>
        </div>
      </div>
      <TransparentSection />
    </>
  );
};

export default Category;

export async function getServerSideProps({ params }) {
  const slug = `/${params.category}`;
  const payload = {
    slug,
    limit: Const.Limit,
    offset: Const.Offset,
  };

  try {
    const [categoryRes, submenusRes] = await Promise.all([
      getCategory(payload),
      getSubmenus(slug),
    ]);

    if (!categoryRes?.data?.isExists) return { notFound: true };

    return {
      props: {
        initialData: categoryRes?.data?.data || [],
        initialCount: categoryRes?.data?.count || 0,
        breadcrumbs: categoryRes?.data?.breadcrumbs || [],
        submenus: submenusRes?.data?.submenus || [],
        title: submenusRes?.data?.title || "",
        meta: categoryRes?.data?.meta || {},
      },
    };
  } catch (error) {
    console.error("Error fetching category data:", error);
    return { notFound: true };
  }
}
