import Image from "next/image";
import Link from "next/link";
import React from "react";
import { dateFormateWithTimeShort, getAuthorText } from "@/utils/Util";

const Webcard = ({
  title,
  image,
  altName,
  category,
  slug,
  timestamp,
  author = [],
  contributor = [],
  key,
}) => {
  return (
    <div className="webstories_itemCard" key={key}>
      {" "}
      <Link href={slug ?? "#"} className="relatedCard">
        <figure>
          <Image
            className="lazy loaded"
            src={image ?? ""}
            data-src={image ?? ""}
            alt={altName ?? ""}
            data-was-processed="true"
            fill
          />
        </figure>
        <div className="gradient">
          {/* <strong>{category ?? ""}</strong> */}
          <h2>{title ?? ""}</h2>
          <span className="date-author">
            {`${getAuthorText("// By", author, contributor)}`}</span>
        </div>
      </Link>
    </div>
  );
};

export default Webcard;


{/* <div className="gradient">
<strong>{category ?? ""}</strong>
<h2>{title ?? ""}</h2>
<span className="date-author">
  {`${dateFormateWithTimeShort(
  timestamp
)} ${getAuthorText("// By", author, contributor)}`}</span>
</div> */}