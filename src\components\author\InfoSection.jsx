import React from "react";
import Image from "next/image";
import Link from "next/link";
import { FaInstagram } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";

const InfoSection = ({ data }) => {
  return (
    <>
      <div className="container">
        <div className="authorWrapper">
          <figure className="authorCntr_inner">
            <picture>
              <Image
                className="authorCntr_inner_img"
                src={data?.image || "/placeholder/author_placeholder.jpg"}
                alt={data?.name || "Author Image"}
                width={5472}
                height={3648}
              />
            </picture>
          </figure>
          <h1 className="authorCntr_inner_name">{data?.name || ""}</h1>
          <div className="Author_followUs">
            {data?.social?.instagram && (
              <Link
                href={`https://www.instagram.com/${data?.social?.instagram}`}
                target="_blank"
              >
                <FaInstagram />
              </Link>
            )}
            {data?.social?.twitterX && (
              <Link
                href={`https://twitter.com/${data?.social?.twitterX}`}
                target="_blank"
              >
                <FaXTwitter />
              </Link>
            )}
          </div>
          {data?.aboutus && (
            <div className="authorCntr_inner_para">
              <p>{data?.aboutus || ""}</p>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default InfoSection;
