import React, { useState } from "react";
import { usePathname } from "next/navigation";
import { astroCardData } from "@/helper/Data";
import Image from "next/image";
import Link from "next/link";
import AstroTiptapRenderer from "@/components/astrology/AstroTiptapRenderer";
import Calendar from "@/components/common/Calendar";
import Button from "../common/Button";

const AstroWidget = ({ key, attrs, content }) => {
  const pathname = usePathname();
  const activeHoroscope = pathname.split("/")[3].split("-")[0];
  const dateFormattedSlug = pathname
    .split("/")[3]
    .split("-")
    .slice(1)
    .join("-");
  // const [isOpen, setIsOpen] = useState(false);
  const [openCalendar, setOpenCalendar] = useState(false);
  // const handleClick = () => {
  //   setIsOpen(!isOpen); // Toggle the state between true/false
  // };
  return (
    <>
      <header className="HoroscopeHeader_container" key={key}>
        <div className="Grid_container">
          <div className="Card_container">
            <div className="Card_text">
              <div className="Card_avatar">
                <img
                  alt={attrs?.alt ?? ""}
                  fetchpriority="high"
                  width={1920}
                  height={1280}
                  decoding="async"
                  data-nimg={1}
                  className="Card_image"
                  sizes="(min-width: 768px) 80px,  64px"
                  srcSet={attrs?.image ?? ""}
                  src={attrs?.image ?? ""}
                  style={{ color: "transparent" }}
                />
              </div>
              <div className="Card_heading">
                <h2 className="Card_title">Today’s Horoscope</h2>
                <div className="Card_controls">
                  {/* <button>Select a date</button> */}
                  <Button
                    className="Card_button"
                    data-active="false"
                    onClick={() => setOpenCalendar((prev) => !prev)}
                  >
                    Select a date
                  </Button>
                </div>
              </div>
              <div className="Card_content">
                {openCalendar ? (
                  <Calendar
                    openCalendar={openCalendar}
                    setOpenCalendar={setOpenCalendar}
                  />
                ) : (
                  <AstroTiptapRenderer content={content} />
                )}
              </div>
            </div>
            <div className="Card_hero">
              <Image
                alt={attrs?.alt ?? ""}
                fetchpriority="high"
                width={1280}
                height={1920}
                decoding="async"
                data-nimg={1}
                className="Card_image"
                sizes="(min-width: 1280px) 360px, (min-width: 768px) 33.33333vw, 100vw"
                src={attrs?.image ?? ""}
                style={{ color: "transparent" }}
              />
            </div>
          </div>
          {astroCardData && astroCardData.length > 0 && (
            <>
              <div className="List_container_Icons">
                <ol className="List_list">
                  {astroCardData.map((item, index) => {
                    const currentHoroscope = item.slug
                      .split("/")[3]
                      .split("-")[0];
                    return (
                      <>
                        <li
                          className="List_item"
                          key={`astro-item-icon-${index}`}
                        >
                          <Link
                            className="List_link"
                            data-active={
                              activeHoroscope === currentHoroscope
                                ? true
                                : false
                            }
                            href={
                              `${
                                item?.slug
                                  ? `${item?.slug}-${dateFormattedSlug}`
                                  : ""
                              }` ?? ""
                            }
                          >
                            <div className="List_hero">
                              <Image
                                alt={item?.altName ?? ""}
                                fetchpriority="high"
                                width={480}
                                height={480}
                                decoding="async"
                                data-nimg={1}
                                className="List_image"
                                sizes="(min-width: 768px) 48px,  64px"
                                src={item?.coverImg ?? ""}
                                style={{ color: "transparent" }}
                              />
                            </div>
                            <div>{item?.title ?? ""}</div>
                          </Link>
                        </li>
                      </>
                    );
                  })}
                </ol>
                {/* <button className="List_button" onClick={handleClick}>
                  {isOpen ? "Show Less" : "Show More"}
                  <span>
                    <svg
                      width={8}
                      height={8}
                      viewBox="0 0 8 8"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M1 2L4 5L7 2"
                        stroke="currentColor"
                        strokeWidth="1.5"
                      />
                    </svg>
                  </span>
                </button> */}
              </div>
            </>
          )}
        </div>
      </header>
    </>
  );
};

export default AstroWidget;
