.Celebrities_wrappers,
.Celebrities_wrappers2 {
  /* height: 100vh; */
  width: 100%;
  margin: 0px 0;
  /* overflow: hidden; */
  position: relative;
  background-color: #faf7f3;
  /* margin: 5rem 0; */
  /* margin-bottom: 10rem; */
}
.Celebrities_wrappers .celeb_gridd,
.Celebrities_wrappers2 .celeb_gridd {
  /* height: 100%; */
  width: 100%;
  /* background-color: blue; */
  /* display: flex
  ; */
  /* justify-content: center; */
  /* gap: 1.5rem; */
  /* position: relative; */
  display: grid;
  grid-template-columns: repeat(1, 1fr);
  grid-column-gap: 30px;
  grid-row-gap: 1rem;
  /* padding: 32px 0; */
}
.celeb_leftt {
  position: relative;
}
.Celebrities_wrappers .celeb_leftt,
.Celebrities_wrappers2 .celeb_leftt {
  /* height: 100%; */
  /* width: 50%; */
  /* display: flex
  ; */
  /* justify-content: center; */
  /* align-items: end; */
  overflow: hidden;
  position: relative;
}
.Celebrities_wrappers .leftsliderr,
.Celebrities_wrappers2 .leftsliderr {
  width: 100%;
  /* height: 100vh; */
  height: 50vh;
  cursor: pointer;
  transition: all ease 0.5s;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  position: relative;
  z-index: 9;
  /* aspect-ratio: 1; */
}

.celeb_multi_gridd {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-gap: 30px;
  gap: 30px;
}
.Celebrities_wrappers .middlesidee,
.Celebrities_wrappers .rightsidee {
  position: relative;
  overflow: hidden;
  height: 100vh;
}
.Celebrities_wrappers .middlesidee .middlesideeinner,
.Celebrities_wrappers2 .middlesidee .middlesideeinner,
.Celebrities_wrappers .rightsidee .rightsideeinner,
.Celebrities_wrappers2 .rightsidee .rightsideeinner {
  display: flex;
  flex-direction: column;
  row-gap: 1.5rem;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
}
.Celebrities_wrappers .middlecardd,
.Celebrities_wrappers2 .middlecardd2,
.Celebrities_wrappers .rightcardd,
.Celebrities_wrappers2 .rightcardd2 {
  /* height: 100%; */
  width: 100%;
  overflow: hidden;
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
  flex-direction: column;
  /* gap: 1rem; */
}
.Celebrities_wrappers .rightcardd figure,
.Celebrities_wrappers .middlecardd figure,
.Celebrities_wrappers2 .middlecardd2 figure,
.Celebrities_wrappers2 .rightcardd2 figure {
  overflow: hidden;
  margin: 0 0 0.5rem;
  /* height: 40vw; */
  position: relative;
  width: 100%;
  aspect-ratio: 1;
}
.relatedPostContentt {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  line-height: 1;
  gap: 8px;
}
.relatedPostContentt strong {
  font-size: 12px;
  display: block;
  text-transform: uppercase;
  font-family: var(--primary-font-Neue);
  font-weight: 700;
  letter-spacing: 0.03em;
  color: #000;
}
img.lazy {
  min-height: 1px;
  transition: all 0.5s ease;
}
.celeb_gridd img {
  object-fit: cover;
}
.more_trend_wrapp {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 20px 1rem;
  z-index: 5;
  color: #fff;
  gap: 10px;
  line-height: 1;
  background: linear-gradient(180deg, transparent 30%, #000);
}
.blog_banner_text_descc {
  font-family: var(--primary-font-Neue);
  line-height: 1.2;
  font-size: 12px;
}
.date_authorr {
  font-weight: 600;
  font-family: var(--primary-font-Neue-Light);
  font-style: normal;
  font-size: 12px;
  white-space: pre;
}
.more_trend_wrapp strong {
  font-size: 12px;
  display: block;
  text-transform: uppercase;
  font-family: var(--primary-font-Neue);
  font-weight: 700;
  letter-spacing: 0.03em;
}
.more_trend_wrapp .slug {
  font-family: var(--primary-font-scotch-display-compress);
  font-size: 1.6rem;
  line-height: 1;
  font-weight: 400;
  color: #fff;
  text-align: flex-start;
}
.date_authorr {
  font-weight: 600;
  font-family: var(--primary-font-Neue-Light);
  font-style: normal;
  font-size: 12px;
  white-space: pre;
}
.relatedPostContentt .date_authorr,
.relatedPostContentt h2 {
  text-align: flex-start;
  font-weight: 600;
  color: #000;
}
.relatedPostContentt h2 {
  font-size: 1.2rem;
  line-height: 1;
  font-family: var(--primary-font-scotch-display-compress);
  letter-spacing: 0.02em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}
@media only screen and (min-width: 768px) {
  .celeb_multi_gridd {
    /* grid-template-columns: repeat(1, 1fr); */
    gap: 30px;
  }
  .more_trend_wrapp {
    padding: 40px 2rem;
  }
  .blog_banner_text_descc {
    display: initial;
    font-size: 1.2rem;
  }
  .more_trend_wrapp .slug {
    font-size: 2.5rem;
    line-height: 1;
  }
  .blog_banner_text strong,
  .relatedPostContentt strong,
  .date_authorr {
    font-size: 15px;
  }
  .relatedPostContentt h2 {
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    line-clamp: 3;
    -webkit-box-orient: vertical;
}
}
@media only screen and (min-width: 992px) {
  .more_trend_wrapp {
    gap: 15px;
  }
  .relatedPostContentt h2 {
    padding: 0 3rem 0 0;
    font-size: 22px;
    line-height: 1.2;
}
}
@media only screen and (min-width: 1024px) {
  .Celebrities_wrappers .celeb_gridd,
  .Celebrities_wrappers2 .celeb_gridd {
    grid-template-columns: repeat(2, 1fr);
    grid-row-gap: 2rem;
  }
  .celeb_multi_gridd {
    grid-template-columns: repeat(2, 1fr);
  }
  .more_trend_wrapp {
    padding: 40px 2rem;
  }
  .Celebrities_wrappers .leftsliderr,
  .Celebrities_wrappers2 .leftsliderr {
    height: 100vh;
  }
  .relatedPostContentt h2 {
    padding: 0;
}
}
@media (min-width: 1200px) {
  .Celebrities_wrappers .celeb_gridd,
  .Celebrities_wrappers2 .celeb_gridd {
    grid-template-columns: repeat(2, 1fr);
  }
  .celeb_multi_gridd {
    grid-template-columns: repeat(2, 1fr);
  }
}
