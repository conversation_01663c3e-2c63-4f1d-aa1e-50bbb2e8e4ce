import React from "react";
import { usePathname } from "next/navigation";
import TiptapRendered from "@/components/common/TiptapRenderer";
import AstroTiptapRenderer from "@/components/astrology/AstroTiptapRenderer";

const AstroContentWrapper = ({ content, tag }) => {
  const pathname = usePathname();
  const router = pathname.split("/");
  const astroBlock = content.content.slice(0, 1);
  const blockData = content.content.slice(1);
  const hasValidContent = blockData?.some(
    (block) => Array.isArray(block.content) && block.content.length > 0
  );
  return (
    <>
      <AstroTiptapRenderer content={astroBlock ?? []} />
      {hasValidContent && (
        <section className="Content_container">
          <div className="Content_body">
            <TiptapRendered content={blockData ?? []} router={router} />
          </div>
        </section>
      )}
    </>
  );
};

export default AstroContentWrapper;
