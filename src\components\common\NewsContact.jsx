import React from "react";
import { HiMiniPlusSmall } from "react-icons/hi2";
const NewsContact = () => {
  return (
    <div className="fullscreen-newsletter">
      <div className="fullscreen-newsletter_wrapper">
        <div className="fullscreen-newsletter_subtitle">
          <div className="text-5 text-serif text-lhcrop">Join the club</div>
        </div>
        <div className="fullscreen-newsletter_content">
          <div className="fullscreen-newsletter_title text-3 text-serif text-lhcrop">
            <div className="newsletter_title_rich-text_wrapper">
              <p>
                Like this story? You’ll (probably) love our monthly newsletter.
              </p>
            </div>
          </div>
          <div className="fullscreen-newsletter_newsletter">
            <form action="" className="newsletter-form_newsletter">
              <input
                className="text-8 input_input"
                placeholder="First name"
                autoComplete="given-name"
                name="firstName"
                required=""
              />
              <input
                className="text-8 input_input"
                placeholder="Last name"
                autoComplete="family-name"
                name="lastName"
                required=""
              />
              <input
                className="text-8 input_input"
                placeholder="Your email address"
                type="email"
                name="email"
                autoComplete="email"
                required=""
              />
              <div className="newsletter-form_bottom">
                {/* <button className="card-actions_actions">
                  <div className="card-actions_actions_button">
                    <span className="button_label">
                      Tell us more <HiMiniPlusSmall />
                    </span>
                  </div>
                </button> */}
                <button
                  className="text-8 button_button button_has-background button_has-text"
                  type="submit"
                >
                  <span className="button_label">Submit</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NewsContact;
