import Link from "next/link";
import React, { useState } from "react";
import { RiInstagramFill } from "react-icons/ri";
import { FaLinkedin, FaYoutube } from "react-icons/fa";
import { FaFacebookF } from "react-icons/fa";
import { <PERSON>a<PERSON>hreads, FaXTwitter } from "react-icons/fa6";
import { addNewsLetterSubscription } from "@/pages/api/HomeApi";
const Footer = () => {
	// State for form fields
	const [formData, setFormData] = useState({
		firstName: "",
		lastName: "",
		email: "",
	});
	const [response, setResponse] = useState({ type: "", message: "" });

	// State for validation errors
	const [errors, setErrors] = useState({});

	// Handle input change
	const handleChange = (e) => {
		setFormData({ ...formData, [e.target.name]: e.target.value });
		setErrors({ ...errors, [e.target.name]: "" }); // Clear error when user types
	};

	// Validate form fields
	const validateForm = () => {
		let newErrors = {};
		if (!formData.firstName.trim()) newErrors.firstName = "First name is required.";
		if (!formData.lastName.trim()) newErrors.lastName = "Last name is required.";
		if (!formData.email.trim()) {
			newErrors.email = "Email is required.";
		} else if (!/\S+@\S+\.\S+/.test(formData.email)) {
			newErrors.email = "Enter a valid email address.";
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	// Handle form submission
	const handleSubmit = async (e) => {
		e.preventDefault();
		if (validateForm()) {
			// Proceed with API call or any further actions
			try {
				const data = await addNewsLetterSubscription(formData);
				setFormData({
					firstName: "",
					lastName: "",
					email: "",
				});
				setResponse({
					type: "success",
					message: "Thank you for subscribing to our newsletter!",
				});
			} catch (error) {
				setResponse({
					type: "error",
					message: "Something went wrong!",
				});
			} finally {
				setTimeout(() => {
					setResponse({
						type: "",
						message: "",
					});
				}, 5000);
			}
			// console.log("Form submitted successfully!", formData);
		}
	};

	return (
		<div className="l-footer l-footer--type-default footer_wrapper">
			<div className="fullscreen-newsletter">
				<div className="fullscreen-newsletter_wrapper">
					<div className="fullscreen-newsletter_subtitle">
						<div className="text-5 text-serif text-lhcrop">Join the club</div>
					</div>
					<div className="fullscreen-newsletter_content">
						<div className="fullscreen-newsletter_title text-3 text-serif text-lhcrop">
							<div className="newsletter_title_rich-text_wrapper">
								<p>Like this story? You’ll (probably) love our monthly newsletter.</p>
							</div>
						</div>
						<div className="fullscreen-newsletter_newsletter">
							<form
								onSubmit={handleSubmit}
								// className="newsletter-form_newsletter"
							>
								<div className="newsletter-form_newsletter">
									<div>
										<input
											className={`text-8 input_input ${errors.firstName ? "input-error" : ""}`}
											placeholder="First name"
											autoComplete="given-name"
											name="firstName"
											value={formData.firstName}
											onChange={handleChange}
										/>
										{errors.firstName && <p className="error-text">{errors.firstName}</p>}
									</div>

									<div>
										<input
											className={`text-8 input_input ${errors.lastName ? "input-error" : ""}`}
											placeholder="Last name"
											autoComplete="family-name"
											name="lastName"
											value={formData.lastName}
											onChange={handleChange}
										/>
										{errors.lastName && <p className="error-text">{errors.lastName}</p>}
									</div>
								</div>
								<div>
									<input
										className={`text-8 input_input ${errors.email ? "input-error" : ""}`}
										placeholder="Your email address"
										type="email"
										name="email"
										autoComplete="email"
										value={formData.email}
										onChange={handleChange}
									/>
									{errors.email && <p className="error-text">{errors.email}</p>}
								</div>

								<div className="newsletter-form_bottom">
									<button
										className="text-8 button_button button_has-background button_has-text"
										type="submit"
									>
										<span className="button_label">Subscribe</span>
									</button>
									<div className="indicator_msg">
										{response.message && (
											<p className={response.type == "error" ? "error-text" : ""}>
												{response.message}
											</p>
										)}
									</div>
								</div>
							</form>
						</div>
					</div>
				</div>
			</div>
			<div>
				<div className="l-footer__grid">
					<div className="l-footer__contact">
						<div className="l-footer__cell">
							<span className="l-footer__number">
								<Link href="/about-us">About Us</Link>
							</span>
						</div>
						<div className="l-footer__cell">
							<span className="l-footer__number">
								<Link href={"/contact-us"}>Contact Us</Link>
							</span>
						</div>
						<div className="l-footer__cell">
							<span className="">
								<Link href="https://shop.yudirect.biz/ManifestLife/Subscribe.php" target="blank">
									Subscribe
								</Link>
							</span>
						</div>
						{/* <div className="l-footer__cell l-footer__cell--spacer"></div> */}
					</div>
					<div className="l-footer__navigation">
						<nav className="c-navigation-footer">
							<ul className="c-navigation-footer__list">
								<li className="c-navigation-footer__item">
									<Link href={"/privacy-policy"} className="c-navigation-footer__link">
										Privacy Policy
									</Link>
								</li>
								<li className="c-navigation-footer__item">
									<Link href={"/terms-of-use"} className="c-navigation-footer__link">
										Terms of Use
									</Link>
								</li>
								<li className="c-navigation-footer__item">
									<Link href={"/disclaimer"} className="c-navigation-footer__link">
										Disclaimer
									</Link>
								</li>
							</ul>
						</nav>
					</div>
				</div>
				<div className="l-footer__colophon">
					<ul className="c-social-media l-footer__social-media">
						<li className="c-social-media__item">
							<Link
								rel="noopener"
								target="_blank"
								href={"https://www.instagram.com/manifest.ind/"}
								className="c-social-media__link"
							>
								<span className="social-media__icon">
									<RiInstagramFill />
								</span>
								<span className="social-media__text">instagram</span>
							</Link>
						</li>
						{/* <li className="c-social-media__item">
                <Link
                  rel="noopener"
                  target="_blank"
                  href={""}
                  className="c-social-media__link"
                >
                  <span className="social-media__icon">
                    <FaLinkedin />
                  </span>
                  <span className="social-media__text">LinkedIn</span>
                </Link>
              </li> */}
						<li className="c-social-media__item">
							<Link
								rel="noopener"
								target="_blank"
								href={"https://www.facebook.com/profile.php?id=61559407741751"}
								className="c-social-media__link"
							>
								<span className="social-media__icon">
									<FaFacebookF />
								</span>
								<span className="social-media__text">facebook</span>
							</Link>
						</li>
						<li className="c-social-media__item">
							<Link
								rel="noopener"
								target="_blank"
								href={"https://x.com/_manifestind"}
								className="c-social-media__link"
							>
								<span className="social-media__icon">
									<FaXTwitter />
								</span>
								<span className="social-media__text">Twitter</span>
							</Link>
						</li>
						<li className="c-social-media__item">
							<Link
								rel="noopener"
								target="_blank"
								href="https://www.threads.net/@manifest.ind"
								className="c-social-media__link"
							>
								<span className="social-media__icon">
									<FaThreads />
								</span>
								<span className="social-media__text">Thread</span>
							</Link>
						</li>
						<li className="c-social-media__item">
							<Link
								rel="noopener"
								target="_blank"
								href="https://www.youtube.com/@ManifestInd"
								className="c-social-media__link"
							>
								<span className="social-media__icon">
									<FaYoutube />
								</span>
								<span className="social-media__text">Youtube</span>
							</Link>
						</li>
					</ul>
					<div className="l-footer__colophon-info">
						<small className="l-footer__copyright">&copy; {new Date().getFullYear()}</small>
					</div>
				</div>
			</div>
		</div>
	);
};

export default Footer;
