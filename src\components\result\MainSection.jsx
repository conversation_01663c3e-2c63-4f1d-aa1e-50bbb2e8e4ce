import React, { useEffect } from "react";
import { gsap } from "gsap/dist/gsap";
import { IoIosArrowBack } from "react-icons/io";
import { IoIosArrowForward } from "react-icons/io";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import { useRouter } from "next/router";
import { getResults } from "@/pages/api/ResultApi";
import Card from "@/components/cards/Card";

const MainSection = ({
  newData,
  query,
  pageNo,
  setPageNo,
  resultsLimit,
  totalCounts,
  setNewData,
  filterSelected,
  setFilterSelected,
}) => {
  const router = useRouter();
  useEffect(() => {
    gsap.registerPlugin(ScrollTrigger);
    gsap.to(".image-zoom", {
      scale: 1,
      duration: 0.3,
      ease: "none",
      scrollTrigger: {
        trigger: ".image-zoom-parent",
        start: "top 60%",
        // markers: true,
      },
    });
  }, []);
  // let isPreviousValid = pageNo == 0 ? false : true;
  // let isNextValid = (pageNo + 1) * resultsLimit > totalCounts ? false : true;

  let isPreviousValid = pageNo > 0;
  let isNextValid = newData?.length === resultsLimit && (pageNo + 1) * resultsLimit < totalCounts;


  let decrement = async () => {
    const newPage = pageNo - 1;
    const data = await getResults(query, newPage, resultsLimit, filterSelected);
    setNewData(data?.data);
    setPageNo(newPage);

    router.push(
      {
        pathname: router.pathname,
        query: {
          ...router.query,
          page: newPage,
        },
      },
      undefined,
      { shallow: true }
    );
  };

  let increment = async () => {
    const newPage = pageNo + 1;
    const data = await getResults(query, newPage, resultsLimit, filterSelected);
    setNewData(data?.data);
    setPageNo(newPage);

    // Update the URL without reloading the page
    router.push(
      {
        pathname: router.pathname,
        query: {
          ...router.query,
          page: newPage,
        },
      },
      undefined,
      { shallow: true }
    );
  };

  return (
    <>
      <div className="sorted_row secmt">
        {newData && newData.length > 0 && (
          <>
            {newData.map((item, key) => {
              return (
                <div
                  key={`result-item-${item?._id ?? key}`}
                  className="sorted_row_4 mb-3"
                >
                  <Card
                    title={item?.title ?? ""}
                    image={
                      item?.croppedImg
                        ? item?.croppedImg
                        : item?.coverImg
                        ? item?.coverImg
                        : ""
                    }
                    altName={item?.altName ?? ""}
                    category={item?.category?.name ?? ""}
                    slug={item?.slug ?? ""}
                    timestamp={item?.timestamp ?? ""}
                    author={item?.writer || []}
                    contributor={item?.contributor || []}
                    key={`result-item-${item?._id ?? key}`}
                  />
                </div>
              );
            })}
          </>
        )}
      </div>
      <div className="paginator">
        {isPreviousValid ? (
          <p className="paginator-p" onClick={decrement}>
            <IoIosArrowBack /> Previous
          </p>
        ) : (
          <p></p>
        )}
        <div className="paginator-sq">
          {/* <span className="paginator-p paginator-active">1</span>
            <span className="paginator-p">2</span>
            <span className="paginator-p">3</span> */}
        </div>
        {isNextValid ? (
          <p className="paginator-p" onClick={increment}>
            Next <IoIosArrowForward />
          </p>
        ) : (
          <p></p>
        )}
      </div>
    </>
  );
};

export default MainSection;
