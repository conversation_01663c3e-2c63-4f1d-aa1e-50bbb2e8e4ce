import React from "react";
import gsap from "gsap";
import { useGSAP } from "@gsap/react";
import HoroscopeCard from "@/components/cards/HoroscopeCard";
import { generateAstrologySlug } from "@/utils/Util";

const DailyHoroscopeSection = ({ data }) => {
  useGSAP(() => {
    gsap.set(".CategoryGrid_item", {
      opacity: 1,
    });
    gsap.from(".CategoryGrid_item .CategoryGrid_link", {
      ease: "linear",
      duration: 1,
      delay: 0.2,
      x: -500,
    });
  });
  if (data && data.length === 0) return null;
  return (
    <>
      <section className="CategoryGrid_container">
        <div className="Heading_container" data-gsap-id="heading">
          {/* <span className="Heading_subtitle">Reveal Your Future</span> */}
          <h2 className="Heading_title">Daily Horoscope</h2>
        </div>
        <ul className="CategoryGrid_list">
          {data.map((item, index) => {
            return (
              <li
                className="CategoryGrid_item"
                data-gsap-id="item"
                key={`horoscopes-item-${index}`}
              >
                <HoroscopeCard
                  title={item?.title ?? ""}
                  description={item?.description ?? ""}
                  coverImg={item?.coverImg ?? ""}
                  slug={generateAstrologySlug(item?.slug ?? "")}
                />
              </li>
            );
          })}
        </ul>
      </section>
    </>
  );
};

export default DailyHoroscopeSection;
