import React, { useEffect, useRef, useCallback, useState } from "react";
import { useRouter } from "next/router";

let gptScriptPromise = null;

const loadGPTScript = () => {
  if (typeof window === "undefined") return Promise.resolve();
  if (window.googletag?.apiReady) return Promise.resolve();

  if (!gptScriptPromise) {
    gptScriptPromise = new Promise((resolve, reject) => {
      const script = document.createElement("script");
      script.src = "https://securepubads.g.doubleclick.net/tag/js/gpt.js";
      script.async = true;
      script.onload = () => {
        const checkReady = setInterval(() => {
          if (window.googletag?.apiReady) {
            clearInterval(checkReady);
            resolve();
          }
        }, 100);
      };
      script.onerror = () => reject("GPT Script failed to load.");
      document.head.appendChild(script);
    });
  }

  return gptScriptPromise;
};

const DynamicAd = ({ adUnits = [], targeting = {}, style = {} }) => {
  const router = useRouter();
  const [currentAd, setCurrentAd] = useState(null);
  const renderedRef = useRef(false);
  const adContainerRef = useRef(null);

  const matchAdUnit = useCallback(() => {
    const width = window.innerWidth;
    const matched = adUnits.find(
      (ad) => width >= ad.minWidth && width <= ad.maxWidth
    );
    setCurrentAd(matched || null);
  }, [adUnits]);

  const destroyAds = useCallback(() => {
    if (window.googletag?.apiReady) {
      const googletag = window.googletag;
      googletag.cmd.push(() => {
        googletag.destroySlots();
      });
    }

    if (currentAd && adContainerRef.current) {
      adContainerRef.current.innerHTML = "";
    }
    renderedRef.current = false;
  }, []);

  const renderAd = useCallback(() => {
    if (!currentAd || renderedRef.current) return;
    const googletag = window.googletag;
    const adDiv = document.getElementById(currentAd.divId);
    if (!adDiv) return;

    googletag.cmd.push(() => {
      const slot = googletag
        .defineSlot(currentAd.adUnitPath, currentAd.adSize, currentAd.divId)
        ?.addService(googletag.pubads());
      if (slot) {
        Object.entries(targeting).forEach(([key, val]) => {
          slot.setTargeting(key, Array.isArray(val) ? val : [val]);
        });

        if (!window.__gptServicesEnabled) {
          googletag.pubads().enableSingleRequest();
          googletag.enableServices();
          window.__gptServicesEnabled = true;
        }

        googletag.display(currentAd.divId);
        renderedRef.current = true;
      }
    });
  }, [currentAd, targeting]);

  useEffect(() => {
    matchAdUnit();
  }, []);

  useEffect(() => {
    if (!currentAd) return;
    loadGPTScript().then(() => {
      setTimeout(() => {
        renderAd();
      }, 100);
    });
  }, [currentAd, renderAd]);

  useEffect(() => {
    const handleRouteChange = () => {
      destroyAds();
      matchAdUnit();
    };
    router.events.on("routeChangeComplete", handleRouteChange);
    window.addEventListener("resize", handleRouteChange);
    return () => {
      router.events.off("routeChangeComplete", handleRouteChange);
      window.removeEventListener("resize", handleRouteChange);
    };
  }, [router, destroyAds, matchAdUnit]);
  if (!currentAd) return null;
  return (
    <div ref={adContainerRef}>
      <div
        id={currentAd?.divId}
        className="ad-flex-all"
        style={{
          width: "100%",
          maxWidth: `${currentAd?.adSize?.[0]} || 300px`,
          position: "relative",
          ...style,
        }}
      />
    </div>
  );
};

export default DynamicAd;
