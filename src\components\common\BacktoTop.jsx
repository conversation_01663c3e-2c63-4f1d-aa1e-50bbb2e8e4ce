import gsap from "gsap";
import Link from "next/link";
import React, { useEffect } from "react";
import { FaArrowUpLong } from "react-icons/fa6";
import ScrollToPlugin from "gsap/dist/ScrollToPlugin";

gsap.registerPlugin(ScrollToPlugin);
const BackToTop = () => {
  function btntop() {
    const btn = document.querySelector("#buttonTop");
    // Scroll event listener
    window.addEventListener("scroll", () => {
      if (window.scrollY > 300) {
        btn.classList.add("show");
      } else {
        btn.classList.remove("show");
      }
    });

    // Click event listener
    btn.addEventListener("click", (e) => {
      e.preventDefault();

      // Using GSAP for smooth scroll animation
      gsap.to(window, { scrollTo: { y: 0 }, duration: 1, ease: "slow" });
    });
  }

  useEffect(() => {
    btntop();
  }, []);
  return (
    <div id="buttonTop" className="card-actions_actions_button buttonTop">
      <FaArrowUpLong />
    </div>
  );
};

export default BackToTop;
