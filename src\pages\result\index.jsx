import React, { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/router";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import Sort from "@/components/result/Sort";
import MainSection from "@/components/result/MainSection";
import TransparentSection from "@/components/common/TransparentSection";
import { getResults, getFilters } from "@/pages/api/ResultApi";
import { IoMdSearch } from "react-icons/io";
import MobileDrawerFilter from "@/components/mobiledrawerfilter/MobileDrawerFilter";
import Button from "@/components/common/Button";

const Result = ({
  meta,
  data = [],
  totalCounts = 0,
  query = "",
  filterData = {},
  resultsLimit = 12,
}) => {
  const router = useRouter();
  const [showFilter, setShowFilter] = useState(false);
  const [text, setText] = useState(query);
  const [newFilterData, setNewFilterData] = useState(filterData);
  const [pageNo, setPageNo] = useState(0);
  const [newTotalCounts, setNewTotalCounts] = useState(totalCounts);
  const [newData, setNewData] = useState(data);
  const [filterSelected, setFilterSelected] = useState({
    writerIds: [],
    sectionIds: [],
    sortBy: -1,
    dateRange: 0,
    subcategoryIds: [],
    tagIds: [],
  });

  const fetchData = useCallback(async () => {
    try {
      const query = router.query.query ?? "";
      const page = parseInt(router.query.page) || 0;

      const [results, filters] = await Promise.all([
        getResults(query, page, resultsLimit, filterSelected),
        getFilters(query, filterSelected),
      ]);

      setNewData(results?.data ?? []);
      setNewTotalCounts(results?.totalCounts ?? 0);
      setNewFilterData(filters);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  }, [router.query.query, router.query.page, filterSelected]);
  useEffect(() => {
    if (router.query.query) {
      setText(router.query.query);
      fetchData();
    }
  }, [router.query.query, router.query.page]);

  const handleSearch = () => {
    router.push(
      `/result?query=${text}&pageNo=${pageNo}&resultsLimit=${resultsLimit}`
    );
  };
  const [openFilter, setOpenFilter] = useState(false);
  const FilterOpenHandler = () => {
    setOpenFilter(true);
  };
  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <section id="all-tab" className="result-page-main-div">
        <div className="container">
          <div className="tag-head">
            <p className="tag-head-p-small">
              Showing results{" "}
              {newTotalCounts === 0
                ? 0
                : `${pageNo * resultsLimit + 1} - ${Math.min(
                    (pageNo + 1) * resultsLimit,
                    newTotalCounts
                  )}`}{" "}
              of {newTotalCounts} for
            </p>
            <h2 className="tag-head-h2">{query}</h2>
            <div className="search-tag">
              <div className="search-div-tag">
                <form
                  style={{ width: "100%" }}
                  onSubmit={(e) => {
                    e.preventDefault();
                    handleSearch();
                  }}
                >
                  <input
                    className="input-tag"
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                  />
                </form>
                <IoMdSearch
                  style={{ cursor: "pointer", fontSize: "18px" }}
                  onClick={handleSearch}
                />
              </div>
            </div>
            {/* <button className="save-filter-btn" onClick={handleFilterToggle}>
              Show Filter
            </button> */}
          </div>
          <div className="sorted_row">
            <div className="sorted_row_4 border-b sort-main-tag pl-0 mt-0">
              <Sort
                classes="moblienone"
                setShowFilter={setShowFilter}
                showFilter={showFilter}
                setNewFilterData={setNewFilterData}
                query={query}
                newFilterData={newFilterData?.data}
                filterSelected={filterSelected}
                setFilterSelected={setFilterSelected}
                setNewData={setNewData}
                setNewTotalCounts={setNewTotalCounts}
              />
              <div className="filterBtn" onClick={FilterOpenHandler}>
                <Button>
                  <span>Filters</span>
                </Button>
              </div>
            </div>
            <div className="sorted_row_12 ln-cont mainsection-tag">
              <MainSection
                newData={newData}
                query={query}
                pageNo={pageNo}
                setPageNo={setPageNo}
                resultsLimit={resultsLimit}
                setNewData={setNewData}
                totalCounts={newTotalCounts}
                filterSelected={filterSelected}
                setFilterSelected={setFilterSelected}
              />
            </div>
          </div>
        </div>
      </section>
      <TransparentSection />
      <MobileDrawerFilter
        openFilter={openFilter}
        setOpenFilter={setOpenFilter}
        setShowFilter={setShowFilter}
        showFilter={showFilter}
        setNewFilterData={setNewFilterData}
        query={query}
        newFilterData={newFilterData?.data}
        filterSelected={filterSelected}
        setFilterSelected={setFilterSelected}
        setNewData={setNewData}
        setNewTotalCounts={setNewTotalCounts}
      />
    </>
  );
};

export default Result;

// Server-side data fetching
export async function getServerSideProps(context) {
  const resultsLimit = 12;
  const { query, pageNo = 0 } = context.query;
  const pageCount = parseInt(pageNo, 10) || 0;
  const metaData = {
    title: "Result - Manifest Magazine",
    description: "Result - Manifest Magazine",
    keywords: "Result",
    author: "Manifest India",
    robots: "index,follow",
  };
  try {
    // Fetch results and filters in parallel for better performance
    const [data, filterData] = await Promise.all([
      getResults(query, pageCount, resultsLimit, { sortBy: -1 }),
      getFilters(query, {}),
    ]);

    return {
      props: {
        meta: metaData,
        data: data?.data ?? [],
        totalCounts: data?.totalCounts ?? 0,
        query: query || "",
        filterData: filterData || {},
        resultsLimit,
      },
    };
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      props: {
        meta: metaData,
        data: [],
        totalCounts: 0,
        query: "",
        filterData: {},
        resultsLimit,
      },
    };
  }
}
