import React from "react";
import Se<PERSON>Header from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import TransparentSection from "@/components/common/TransparentSection";

const Custom404 = ({ meta }) => {
  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div className="not-found-div">
        <div className="container">
          <div className="pageInner">
            <div className="error-head">
              <div
                className=""
                style={{
                  display: "flex",
                  alignItems: "center",
                  flexDirection: "column",
                }}
              >
                <p className="error-p-main">404</p>
                <p className="error-p-main2">Not Found</p>
              </div>

              <p className="error-p-main3">
                The page you are looking for could not be located. Please
                double-check the URL for any errors. In the meantime, feel free
                to scroll down and explore our latest stories.
              </p>
            </div>
          </div>
        </div>
      </div>
      <TransparentSection />
    </>
  );
};

export default Custom404;
export async function getStaticProps() {
  const meta = {
    title: "Page Not Found - Manifest Magazine",
    description:
      "Oops, the page you are looking for could not be located. Please double-check the URL for any errors.",
    keywords: [],
    author: "Manifest India",
    robots: "noindex,nofollow",
  };
  return { props: { meta: meta } };
}
