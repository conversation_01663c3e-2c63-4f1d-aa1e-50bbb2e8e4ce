import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Tag List
export const getTag = async (body) => {
  const res = await fetch(Const.Link + `api/tenant/tag-list?slug=${body.slug}&limit=${body.limit}&offset=${body.offset}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Latest Tag Articles
export const getTagLatest = async (slug) => {
  const res = await fetch(Const.Link + `api/tenant/tag-latest?slug=${slug}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Tag Sitemap
export const getTagSitemap = async (slug) => {
  const res = await fetch(Const.Link + "api/tenant/tag-sitemap", new Headers("GET"));
  return ProcessAPI(res);
};

