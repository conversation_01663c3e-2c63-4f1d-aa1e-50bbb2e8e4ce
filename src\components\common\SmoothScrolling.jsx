// import { React<PERSON>enis } from "@studio-freight/react-lenis";

// function SmoothScrolling({ children }) {
//   const lenisOptions = {
//     lerp: 0.1, // Controls how smooth the scrolling is
//     duration: 1, // Slows down or speeds up the scrolling
//     smoothTouch: false, // Disable smooth scroll on touch devices
//     smooth: true, // Smooth scroll for desktop (obviously)
//   };

//   return (
//     <ReactLenis
//       root
//       // options={{
//       //   lerp: 0.1,
//       //   duration: 1.5,
//       //   smoothTouch: true,
//       //   smoothWheel: true,

//       //   // syncTouchLerp: 5,
//       // }}
//       options={lenisOptions}
//     >
//       {children}
//     </ReactLenis>
//   );
// }

// export default SmoothScrolling;

import React, { useEffect } from "react";

const SmoothScrolling = ({ children }) => {
  useEffect(() => {
    let scroll;
    import("locomotive-scroll").then((locomotiveModule) => {
      scroll = new locomotiveModule.default({
        el: document.querySelector("[data-scroll-container]"),
        smooth: 2,
        smoothMobile: false,
        resetNativeScroll: true,
      });
    });

    // `useEffect`'s cleanup phase
    return () => {
      if (scroll) scroll.destroy();
    };
  }, []);

  return <div>{children}</div>;
};

export default SmoothScrolling;
