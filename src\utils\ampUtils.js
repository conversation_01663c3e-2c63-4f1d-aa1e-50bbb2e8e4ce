/**
 * Utility functions for handling AMP pages and navigation
 */

/**
 * Check if a URL is an AMP web story
 * @param {string} url - The URL to check
 * @returns {boolean} - True if it's a web story URL
 */
export const isWebStoryUrl = (url) => {
  return url && url.includes('/webstories/');
};

/**
 * Handle navigation to AMP pages by forcing full page reload
 * @param {string} url - The URL to navigate to
 */
export const navigateToAmpPage = (url) => {
  if (typeof window !== 'undefined') {
    window.location.href = url;
  }
};

/**
 * Initialize AMP runtime if available
 */
export const initializeAmpRuntime = () => {
  if (typeof window !== 'undefined' && window.AMP) {
    try {
      // Trigger AMP runtime initialization
      window.AMP.attachShadowDoc(document, document.body, {});
    } catch (error) {
      console.warn('AMP runtime initialization failed:', error);
    }
  }
};

/**
 * Handle back navigation for AMP pages
 */
export const handleAmpBackNavigation = () => {
  if (typeof window !== 'undefined') {
    // Check if there's a referrer, otherwise go to web stories home
    const referrer = document.referrer;
    if (referrer && !referrer.includes('/webstories/')) {
      window.history.back();
    } else {
      window.location.href = '/webstories';
    }
  }
};
