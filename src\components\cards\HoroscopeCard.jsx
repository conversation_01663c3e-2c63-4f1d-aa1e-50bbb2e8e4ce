import React, { useEffect } from "react";
import Image from "next/image";
import Link from "next/link";
import gsap from "gsap";

const HoroscopeCard = ({ title, description, coverImg, slug }) => {
  useEffect(() => {
    const cards = document.querySelectorAll(".CategoryGrid_item");

    // Event handler for mousemove
    const handleMouseMove = (e) => {
      // Get the bounding rect of the card
      const card = e.currentTarget;
      const cardRect = card.getBoundingClientRect();

      // Get mouse position relative to the card
      const xWidth = e.clientX - cardRect.left; // X position within the card
      const yHeight = e.clientY - cardRect.top; // Y position within the card

      // Calculate the rotation based on mouse position
      const xRotation = 30 * ((xWidth - cardRect.width / 2) / cardRect.width);
      const yRotation =
        30 * ((yHeight - cardRect.height / 2) / cardRect.height);

      // Apply the transform to the card
      gsap.to(card, {
        scale: 1.04,
        rotateX: `${yRotation}deg`,
        rotateY: `${xRotation}deg`,
      });
    };

    // Event handler for mouseout (reset transform)
    const handleMouseOut = (e) => {
      const card = e.currentTarget;
      gsap.to(card, {
        scale: 1,
        rotateX: 0,
        rotateY: 0,
      });
    };

    // Add event listeners to each card
    cards.forEach((card) => {
      card.addEventListener("mousemove", handleMouseMove);
      card.addEventListener("mouseout", handleMouseOut);
    });

    // Cleanup event listeners on component unmount
    return () => {
      cards.forEach((card) => {
        card.removeEventListener("mousemove", handleMouseMove);
        card.removeEventListener("mouseout", handleMouseOut);
      });
    };
  }, []); // Empty dependency array ensures this effect runs only once after initial mount
  return (
    <>
      <Link className="CategoryGrid_link" href={slug ?? "#"}>
        <div className="CategoryGrid_hero">
          <Image
            alt="Aries Astrological Icon"
            data-gsap-id="image"
            fetchpriority="high"
            width={480}
            height={480}
            decoding="async"
            data-nimg={1}
            className="CategoryGrid_image"
            sizes="(max-width: 768px) 128px, 96px"
            src={coverImg}
          />
        </div>
        <div className="CategoryGrid_text">
          <div className="CategoryGrid_title">{title}</div>
          <div className="CategoryGrid_subtitle">{description}</div>
        </div>
      </Link>
    </>
  );
};

export default HoroscopeCard;
