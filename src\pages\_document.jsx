import Document, { Html, <PERSON>, Main, NextScript } from "next/document";

class MyDocument extends Document {
  static async getInitialProps(ctx) {
    const initialProps = await Document.getInitialProps(ctx);
    const isAmp =
      ctx.req?.url?.includes("/amp") || ctx.req?.url?.includes(" ?amp=1");
    return { ...initialProps, isAmp };
  }

  render() {
    const { isAmp } = this.props;
    return (
      <Html lang="en" {...(isAmp ? { amp: "" } : {})}>
        <Head>
          {isAmp ? (
            // AMP-specific <head> content
            <>
              <script async src="https://cdn.ampproject.org/v0.js" />
              <style amp-boilerplate="">
                {`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}
              </style>
              <noscript>
                <style amp-boilerplate="">
                  {`body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}`}
                </style>
              </noscript>
            </>
          ) : (
            // Normal <head> content (your current one)
            <>
              <link
                rel="stylesheet"
                href="https://use.typekit.net/ben8rqm.css"
              />
              <link
                rel="preload"
                href="https://securepubads.g.doubleclick.net/tag/js/gpt.js"
                as="script"
              />
              <script
                async
                src="https://www.googletagmanager.com/gtag/js?id=G-YJC4Z5844L"
              ></script>
              <script
                dangerouslySetInnerHTML={{
                  __html: `
                window.dataLayer = window.dataLayer || [];
                function gtag(){dataLayer.push(arguments);}
                gtag('js', new Date());
                gtag('config', 'G-YJC4Z5844L');
              `,
                }}
              />
              <script
                dangerouslySetInnerHTML={{
                  __html: `!function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
                n.callMethod.apply(n,arguments):n.queue.push(arguments)};
                if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
                n.queue=[];t=b.createElement(e);t.async=!0;t.src=v;
                s=b.getElementsByTagName(e)[0];
                s.parentNode.insertBefore(t,s)}(window, document,'script',
                'https://connect.facebook.net/en_US/fbevents.js');
                fbq('init', '957856323160808');
                fbq('track', 'PageView');`,
                }}
              />
              <noscript>
                <img
                  height="1"
                  width="1"
                  style={{ display: "none" }}
                  src="https://www.facebook.com/tr?id=957856323160808&ev=PageView&noscript=1"
                />
              </noscript>
            </>
          )}
        </Head>
        <body>
          <Main />
          <NextScript />
          {!isAmp && (
            <script
              async
              src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"
            />
          )}
        </body>
      </Html>
    );
  }
}

export default MyDocument;
