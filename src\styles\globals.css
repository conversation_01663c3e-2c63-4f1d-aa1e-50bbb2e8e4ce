* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

@font-face {
  font-family: "NeueHaasDisplayBold";
  src: url(../../public/Assets/NeueHaasDisplayBold.ttf);
}
/* @font-face {
  font-family: "NeueHaasDisplayBoldItalic";
  src: url(../../public/Assets/NeueHaasDisplayBoldItalic.ttf);
} */
@font-face {
  font-family: "NeueHaasMedium";
  src: url(../../public/Assets/NeueHaasDisplayMediu.ttf);
}
@font-face {
  font-family: "NeueHaasDisplayLight";
  src: url(../../public/Assets/NeueHaasDisplayLight.ttf);
}

:root {
  --primary-font-Neue: "neulis-neue", sans-serif;
  --primary-font-scotch-display: "scotch-display-condensed", sans-serif;
  --primary-font-scotch-display-compress: "scotch-display-compressed",
    sans-serif;
  /* --primary-font-scotch-display-normal: "scotch-display", sans-serif; */
  --primary-font-Neue-Bold: "NeueHaasDisplayBold";
  --primary-font-Neue-Medium: "NeueHaasMedium";
  --primary-font-Neue-MediumItalic: "NeueHaasDisplayMediumItalic";
  --primary-font-Neue-Light: "NeueHaasDisplayLight";
  --secondary-font-Bitter: "Bitter-VariableFont";
}
html,
body {
  width: 100%;
  height: 100%;
  background-color: #faf7f3;
  -webkit-font-smoothing: antialiased;
  position: relative;
}
@media (prefers-color-scheme: dark) {
  html,
  body {
    background-color: #faf7f3;
  }
}

::-webkit-scrollbar {
  display: none;
}
a {
  text-decoration: none;
}

.flex-all {
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}
.swiper {
  width: 100%;
  height: 100%;
  overflow: visible !important;
  margin-left: 0 !important;
  /* overflow-x: auto; */

  /* white-space: nowrap; */
  /* background-color: red; */
  /* display: flex; */
  cursor: grab;
}
.swiper-wrapper {
  /* flex-wrap: wrap; */
}
.swiper-slide {
  text-align: flex-start;
  font-size: 18px;
  /* width: 33% !important; */
  /* Center slide text vertically */
  display: flex;
  justify-content: center;
  align-items: center;
}

.swiper-slide img {
  display: block;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.swiper-pagination {
  display: none;
}
/* tansparentSec */
.transparentSec {
  width: 100%;
  height: 100vh;
  position: relative;
  pointer-events: none;
  opacity: 0;
  z-index: 2;
}
/* tansparentSec */
/* Footer */
.l-footer.l-footer--type-default {
  position: sticky;
  /* position: fixed; */
  /*   top: 100vh; */
  left: 0;
  right: 0;
  /* top: 0; */
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  /* min-height: 100vh; */
  /* padding-top: 3rem; */
  /* border-top: 1px solid #000; */
  padding-top: 4rem;
  z-index: 1;
  width: 100%;
  /* height: 100vh; */
  overflow: hidden;
  background-color: #faf7f3;
}
.l-footer__grid {
  display: flex;
  /* margin-top: auto; */
}
.l-footer__contact {
  width: 50%;

  /* display: flex; */
  /* flex-direction: column; */
}
.l-footer__cell,
.l-footer__cell--spacer {
  flex-basis: 0;
  flex-grow: 1;
  font-family: var(--primary-font-Neue-Light);
  font-weight: 400;
  font-style: normal;
  font-size: 1rem;
  /* line-height: 1.5; */
  letter-spacing: 0.02em;
  padding: 0.75rem 1.5rem 0.75rem 3rem;
  border-top: 1px solid #000000;
}
.c-navigation-footer__item {
  border-top: 1px solid #000000;
}
.l-footer__cell a {
  color: #000000;
}
.l-footer__grid a {
  transition: all 0.5s ease;
}
.l-footer__grid a:hover {
  color: gray;
}
.c-navigation-footer__link {
  font-family: var(--primary-font-Neue-Light);
  font-weight: 400;
  font-style: normal;
  font-size: 1rem;
  /* line-height: 1.5; */
  letter-spacing: 0.02em;
  display: block;
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  color: #000000;
}
.l-footer__colophon {
  width: 100%;
  padding: 1rem 1rem;
  background-color: #000000;
  color: #ffffff;
  display: flex;
  justify-content: space-between;
}
.l-footer__social-media {
  grid-column: span 8 / span 8;
}
.c-social-media,
.c-social-media--full {
  display: grid;
  grid-template-columns: repeat(12, minmax(0, 1fr));
  gap: 0 1.5rem;
}
.c-social-media__item {
  grid-column: span 2 / span 2;
}
.c-social-media__link {
  font-family: var(--primary-font-Neue-Bold);
  font-weight: 700;
  font-style: normal;
  font-size: 0.875rem;
  line-height: 1.25;
  letter-spacing: 0.06em;
  text-transform: uppercase;
}
.l-footer__colophon a {
  text-decoration: none;
  color: #ffffff;
}
/* .l-footer__colophon-info {
  display: flex;
  justify-content: center;
  gap: 1rem;
} */
.l-footer__copyright {
  font-family: Degular Display, Verdana, sans;
  font-weight: 700;
  font-style: normal;
  font-size: 1rem;
  /* line-height: 1.25; */
  letter-spacing: 0.06em;
  text-transform: uppercase;
  color: #ffffff;
}

#category-hero-section .h1-header h1 {
  color: #000;
  /* transform: translateY(100%); */
}
.l-footer__navigation {
  width: 50%;
}
.social-media__text {
  display: none; /* Hide the Instagram icon on mobile */
}
.social-media__icon {
  display: initial; /* Show the Instagram text on mobile */
  font-size: 1.5rem;
}
.menunavImg {
  background-color: #000 !important;
}
@media (min-width: 48.0625rem) {
  /* .l-footer__contact {
    width: 50%;
  }
  .l-footer__navigation {
    width: 50%;
  } */
  .l-footer__colophon {
    /* display: grid;
    grid-template-columns: repeat(12, minmax(0, 1fr)); */
    display: flex;
    justify-content: space-between;
    gap: 0 3rem;
  }
  /* .l-footer__colophon-info {
    grid-column: span 4 / span 4;
    justify-content: flex-end;
  } */
  .c-social-media,
  .c-social-media--full {
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }
  .social-media__text {
    display: initial; /* Hide the Instagram icon on mobile */
  }
  .social-media__icon {
    display: none; /* Show the Instagram text on mobile */
  }
}
@media (max-width: 48rem) {
  .l-footer__cell,
  .l-footer__cell--spacer {
    padding: 0.75rem 1.5rem;
  }
  /* .l-footer__grid {
    flex-direction: column;
  } */
  /* .l-footer__cell--spacer {
    display: none;
  } */
}
@media (max-width: 64rem) {
  .l-footer__cell,
  .l-footer__cell--spacer {
    font-size: 1rem;
    /* line-height: 1.57; */
  }
}
@media (max-width: 80rem) {
  .l-footer__colophon {
    /* padding-left: 3rem; */
    /* padding-right: 3rem; */
  }
}
/* Footer */

/* DropdownMenu-wrapper */
.DropdownMenu-wrapper {
  width: 100%;
  height: 100vh;
  background-color: brown;
  position: absolute;
  overflow: hidden;
  top: -100%;
  z-index: 99;
  display: none;
}
.DropdownMenu-cntr {
  width: 100%;
  height: 100vh;
  background-color: #faf7f3;
}
.DropdownMenu-cntr-content {
  position: absolute;
  width: 100%;
  /* height: calc(100% - 30%); */
  height: 100%;
  background-color: #faf7f3;
  z-index: 9;
  /* bottom: 0; */
  left: 0;
  overflow-y: auto;
  /* padding: 5rem 0; */
  /* padding: 32px 60px; */
  /* display: flex;
  gap: 24px; */
}
.dropdown-wrapper {
  /* background-color: #f2f1ee; */
  width: 100%;
  border-radius: 8px;
  position: relative;
  /* padding: 10rem 0; */
}
.logo-wrapper {
  overflow: hidden;
}
.Navbar_logo-menu {
  filter: invert(1);
  overflow: hidden;
  transform: translateY(300px);
}
.dropdown-top-links {
  padding: 0rem 0px;
  display: flex;
  gap: 24px;
  font-family: var(--primary-font-scotch-display);
  justify-content: flex-end;
  /* align-items: flex-end; */
  position: absolute;
  margin: auto;
  width: 100%;
  /* bottom: 0; */
  /* padding: 0 0 10rem 0; */
  padding: 1rem 0 0 0;
  top: 300px;
}
/* .dropdown-nav-1 {
  padding: 15rem 0;
} */
.dropdown-top-links .single-dropdown-col {
  flex: 1;
}
.dropdown-top-links .single-dropdown-col > p,
.dropdown-top-links .single-dropdown-col > p a {
  font-size: 18px;
  line-height: 21px;
  font-weight: 500;
  margin: 0 0 25px;
  font-size: 1.2rem;
  line-height: 120%;
  color: #020308;
  letter-spacing: 0.5px;
}
.dropdown-top-links .single-dropdown-col .dropdown-links {
  display: flex;
  flex-direction: column;
  gap: 8px;
  transition: all 0.5s ease;
}

.dropdown-top-links .single-dropdown-col .dropdown-links a {
  font-size: 16px;
  display: inline-block;
  padding: 0 0 4px;
  color: #020308;
  letter-spacing: 0.5px;
  transition: transform 0.3s ease;
}
.dropdown-top-links .single-dropdown-col .dropdown-links a:hover {
  transform: translateY(-5px);
}
.logo-wrapper-nav-1 {
  display: block;
}
.ad-parent-dynamic {
  /* margin: 30px auto; */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
}
.ad-flex-all {
  /* position: relative; */
  display: flex;
  align-items: center;
  justify-content: center;
}
.ad-text::before {
  color: #8c8c8c;
  content: "ADVERTISEMENT";
  display: block;
  font-family: Arial, sans-serif;
  font-size: 9px;
  font-weight: 400;
  letter-spacing: 1px;
  line-height: 1.2;
  margin: 5px auto;
  text-align: center;
  text-transform: uppercase;
  -webkit-font-smoothing: antialiased;
}

span.author {
  display: block;
  font-size: 16px;
  text-transform: capitalize;
  font-weight: 600;
  color: var(--primary-color);
  font-family: var(--primary-font-Neue-Light);
}
@media only screen and (min-width: 768px) {
  .swiper {
    width: 100%;
    margin-left: auto;
  }
  .ad-parent-dynamic {
    height: auto;
  }
}
@media only screen and (min-width: 1024px) {
  .DropdownMenu-cntr-content {
    /* height: calc(100% - 30%); */
    height: 100%;
  }
  /* .dropdown-nav-1 {
    padding: 20rem 0;
  } */
  .logo-wrapper-nav-1 {
    display: none;
  }
  .DropdownMenu-wrapper {
    display: initial;
  }
  .dropdown-top-links .single-dropdown-col .dropdown-links {
    gap: 15px;
  }
  .dropdown-top-links .single-dropdown-col .dropdown-links a {
    font-size: 1rem;
  }
}
/* DropdownMenu-wrapper */
/* mobile menu */
.dropdown-menu-main_M {
  position: fixed;
  top: 50%;
  left: 0%;
  transform: translate(0%, -50%);
  /* border-radius: 0; */
  /* max-height: 100vh; */
  max-height: calc(100vh - 0.5rem * 2);
  overflow-x: scroll;
  width: 0%;
  -ms-overflow-style: none;
  scrollbar-width: none;
  z-index: 999;
  /* border-radius: 5px; */
  font-family: var(--primary-font-scotch-display);
  transition: width 0.5s ease;
  padding: 10px;
  height: 100vh;
}
.dropdown-menu-main_M.open {
  width: 100%; /* Full width when menu is open */
}
.dropdown-menu-main_M.visible {
  opacity: 1;
}
.dropdown-menu-main_M .dropdown-wrapper_M {
  background-color: #000000;
  max-width: 1200px;
  width: 100%;
  border-radius: 8px;
  position: relative;
  padding-top: 80px;
  /* height: 100vh; */
}
.arrow-btn_M {
  /* border: 1px solid rgba(2, 3, 8, 0.2); */
  width: 40px;
  height: 40px;
  /* border-radius: 50%; */
  display: inline-flex;
  justify-content: center;
  align-items: center;
  /* background-color: #fcfbf6; */
  padding: 0;
  transition: 0.3s ease-out;
}
.dropdown-menu-main_M .close-btn_M {
  position: absolute;
  top: 30px;
  right: 12px;
  background-color: transparent;
}
.arrow-btn_M svg {
  width: 35px;
  height: auto;
  transition: 0.3s ease-out;
  filter: invert(0);
  cursor: pointer;
  color: #fff;
}
.dropdown-menu-main_M .dropdown-top-links_M .single-dropdown-col_M {
  flex: 1;
  display: flex;
  gap: 16px;
  border-bottom: 1px solid rgb(255 255 255 / 36%);
  padding-bottom: 16px;
  justify-content: space-between;
  opacity: 0; /* Initially hidden links */
  transition: opacity 0.5s ease-out 0.5s;
}
.dropdown-menu-main_M .dropdown-top-links_M {
  padding: 32px 60px;
  display: flex;
  gap: 24px;
  flex-direction: column;
}
.dropdown-menu-main_M .dropdown-top-links_M .single-dropdown-col_M > p,
.dropdown-menu-main_M .dropdown-top-links_M .single-dropdown-col_M > p a {
  font-size: 18px;
  line-height: 21px;
  font-weight: 500;
  color: #ffffff;
}
.dropdown-menu-main_M
  .dropdown-top-links_M
  .single-dropdown-col_M
  .dropdown-links_M
  div {
  display: flex;
  flex-direction: column;
  gap: 16px;
  text-align: right;
}
.Mobile_social_links {
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.Mobile_social_links_inner {
  display: flex;
  gap: 20px;
}
.Mobile_social_links_inner svg {
  color: #ffffff;
  font-size: 1.5rem;
}
.animated-link-underline_M {
  color: #ffffff;
}
@media (min-width: 1024px) {
  .dropdown-menu-main_M {
    display: none;
  }
}
@media (max-width: 1280px) {
}
@media (max-width: 990px) {
}
@media (max-width: 1023px) {
  /* .dropdown-menu-main_M {
    position: fixed;
    top: 50%;
    left: 3%;
    transform: translate(0%, -50%);
    border-radius: 0;
  
    max-height: calc(100vh - 0.5rem * 2);
    overflow-x: scroll;
    width: 95%;
    -ms-overflow-style: none;
    scrollbar-width: none;
    z-index: 999;
    border-radius: 5px;
  } */
  .dropdown-menu-main_M .dropdown-wrapper_M {
    border-radius: 5px;
    padding: 80px 0 15px 0;
    height: 100%;
    overflow: auto;
  }
  .dropdown-menu-main_M .dropdown-top-links_M,
  .Mobile_social_links {
    /* flex-direction: column; */
    padding: 24px;
  }
  /* .dropdown-menu-main_M .dropdown-top-links_M .single-dropdown-col_M {
    display: flex;
    gap: 16px;
    border-bottom: 1px solid rgba(2, 3, 8, 0.1);
    padding-bottom: 16px;
  } */
  /* .dropdown-menu-main_M .dropdown-top-links_M .single-dropdown-col_M > p {
    text-decoration: underline;
  } */
  .dropdown-menu-main_M
    .dropdown-top-links_M
    .single-dropdown-col_M
    .dropdown-links_M {
    flex: 1;
    text-align: right;
    color: #ffffff;
  }
}
.dropdown-menu-main_M.open {
  opacity: 1;
  visibility: visible;
}
.dropdown-menu-main_M .dropdown-top-links_M .single-dropdown-col_M.visible {
  opacity: 1; /* Fully visible when menu is open */
}
/* Handle closing the navbar */
.dropdown-menu-main_M:not(.open)
  .dropdown-menu-main_M
  .dropdown-top-links_M
  .single-dropdown-col_M {
  opacity: 0; /* Fade links out when navbar is closed */
  transition: opacity 0.5s ease-out; /* Add fade out transition */
}

.dropdown-menu-main_M:not(.open) {
  width: 0; /* Shrink navbar to 0% when closed */
  transition: width 0.5s ease 0.7s; /* Add a delay to width transition */
}
/* mobile menu */

/* Aboutus */

.AboutandTeamCntr {
  position: relative;
  overflow: hidden;
  z-index: 9;
  background-color: #faf7f3;
  padding: 8rem 0;
  border-bottom: 1px solid #000;
}
.AboutUscntr {
  position: relative;
  overflow: hidden;
}
.pageInner {
  width: 60%;
  margin: 0 auto;
  /* margin-top: 100px; */
}
.pageInner h1 {
  padding-bottom: 50px;
  text-align: center;
  font-family: var(--primary-font-scotch-display-compress);
  font-weight: 600;
  font-size: 2rem;
  font-style: normal;
}
.editor-styles-wrapper,
.entry-content {
  counter-reset: footnotes;
}
.entry-content p {
  font-family: var(--primary-font-Neue);
  font-size: 20px;
  font-weight: 300;
  line-height: 36px;
  margin-top: 0;
  margin-bottom: 1rem;
}

@media (min-width: 1200px) {
  .pageInner h1 {
    font-size: 2.5rem;
  }
  .AboutandTeamCntr {
    padding: 8rem 0;
  }
}
@media only screen and (min-width: 1401px) and (max-width: 1650px) {
  .entry-content p {
    font-size: 20px;
    font-weight: 300;
    line-height: 30px;
  }
}
@media only screen and (max-width: 991px) {
  .content-wrapper {
    width: 100% !important;
  }
  .pageInner {
    width: 100%;
  }
  .entry-content p {
    font-size: 18px;
    line-height: 24px;
  }

  .pageInner h1 {
    padding-bottom: 20px;
  }
}

/* Aboutus */
/* teamCntr */
.TeamCntr {
  position: relative;
  overflow: hidden;
}
.TeamCntr_inner {
  position: relative;
  margin: 8% 0;
  display: grid;
  grid-template-columns: 1fr;
  /* justify-content: space-between; */
  flex-wrap: wrap;
  /* align-items: center; */
}
.TeamCntr_left {
  /* width: 35%; */
  align-self: flex-start;
  font-family: var(--primary-font-scotch-display);
}
.TeamCntr_left_inner {
  max-width: 20.5em;
  width: 100%;
}
.TeamCntr_left_inner h3 {
  font-size: 20px;
  line-height: 1;
  font-weight: 600;
  font-size: 2.5rem;
  font-style: normal;
}
.TeamCntr_left_inner p {
  font-family: var(--primary-font-Neue);
  margin: 1rem 0;
  font-size: 18px;
}
.TeamCntr_right {
  /* width: 35%; */
  align-self: flex-start;
  font-family: var(--primary-font-scotch-display);
}
.TeamCntr_right_wrapper {
  margin-bottom: 5rem;
}
.TeamCntr_right h3 {
  font-size: 20px;
  line-height: 1;
  font-weight: 600;
  font-size: 2.5rem;
  font-style: normal;
}
.TeamCntr_right_inner {
  /* width: 60%; */
  display: grid;
  grid-template-columns: 1fr;
  gap: 0.5rem;
  /* align-self: flex-start; */
  /* justify-content: space-between; */
  /* flex-wrap: wrap; */
}
.TeamCntr_right_inner:not(:first-child) {
  margin-top: 1rem;
}
.Teamcard_cntr {
  /* width: 49%; */
  /* margin-bottom: 1em; */
  align-self: flex-start;
  position: relative;
  cursor: pointer;
}
.Teamcard_cntr .cover2 {
  display: block;
  position: relative;
  /* padding-bottom: 109%; */
  aspect-ratio: 1;
  overflow: hidden;
}
.Teamcard_cntr .cover2 img {
  object-fit: cover;
  object-position: center;
}
.Teamcard_text {
  margin-top: 1em;
  font-family: var(--primary-font-Neue);
  position: relative;
}
.Teamcard_text h5 {
  font-size: 1rem;
}
.Teamcard_text h6 {
  font-size: 1rem;
  font-weight: 300;
  color: gray;
}
.Teamcard_text_chief {
  margin-top: 0em;
  font-family: var(--primary-font-scotch-display);
  position: relative;
}
.Teamcard_text_chief h5 {
  font-size: 2.5rem;
  font-style: normal;
  font-weight: 600;
  line-height: 1.5;
}
.Teamcard_text_chief h6 {
  font-family: var(--primary-font-Neue);
  font-size: 1.2rem;
  font-weight: 300;
  color: gray;
}
.cover2:after {
  content: "";
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  background-color: #000;
  transition: all 0.5s cubic-bezier(0.9, 1, 0.22, 1);
  opacity: 0;
}
.cover2_text {
  position: absolute;
  inset: 0%;
  width: 100%;
  height: 100%;
  font-family: var(--primary-font-Neue-Bold);
  display: inline-flex;
  flex-direction: column;
  justify-content: center;
  padding: 0 1rem;
  color: #fff;
  overflow: hidden;
  /* opacity: 0; */
}

.cover2_text .line {
  width: 100%;
  /* height: 50px; */
  position: relative;
  overflow: hidden;
}
.cover2_text .line span {
  position: relative;
  font-size: 1rem;
  display: block;
  /* transform: translateY(100px), skewY(20); */
}
.Teamcard_cntr:hover .cover2:after {
  opacity: 1;
}
@media only screen and (min-width: 768px) {
  .TeamCntr_inner {
    /* grid-template-columns: 1fr 2fr; */
    grid-template-columns: 1fr 1fr;
  }
  .TeamCntr_right_inner {
    /* grid-template-columns: 1fr 1fr 1fr; */
    grid-template-columns: 1fr 1fr;
  }
  .TeamCntr_right_inner:not(:first-child) {
    margin-top: 1rem;
  }
  .TeamCntr_right_inner {
    width: 100%;
  }
}
@media screen and (max-width: 1023px) {
  .TeamCntr_left {
    width: 100%;
    margin-bottom: 2em;
  }
  /* .TeamCntr_right_inner {
    width: 100%;
  } */
  .Teamcard_text_chief h5 {
    font-size: 1.8rem;
  }
  .Teamcard_text_chief h6 {
    font-size: 1rem;
  }
  .TeamCntr_right h3 {
    font-size: 1.8rem;
  }
}
@media screen and (max-width: 767px) {
  .TeamCntr_right_wrapper {
    margin-bottom: 3rem;
  }
  .TeamCntr_inner {
    margin: 3em 0 !important;
  }
  .Teamcard_text_chief {
    margin-top: 1em;
  }
  span.author {
    font-size: 12px;
  }
}

/* teamCntr */
/* webstorieReels */
.webstorieReels {
  left: 0;
  position: fixed;
  right: 0;
  top: 0;
  z-index: 9999;
  background-color: #faf7f3;
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  width: 100%;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  /* cursor: none; */
  overflow: hidden;
}
.reel-cursor {
  position: absolute;
  top: 0;
  left: 0;
  width: 100px;
  height: 100px;
  border-radius: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(133 128 128 / 50%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  pointer-events: none;
  z-index: 999;
}
.reel-cursor p {
  font-size: 1rem;
  transform: uppercase;
  font-family: var(--primary-font-Neue);
  font-weight: 900;
}
.story_content {
  /* width: 100%; */
  height: 90%;
  position: relative;
  /* padding: 4rem 0; */
  /* top: 50%;
  left: 50%;
  transform: translate(-50%, -50%); */
  /* width: 30rem; */
  background-color: #000;
  overflow: hidden;
  border-radius: 0;
  aspect-ratio: 9/16;
  z-index: 99;
}
/* .story_image_contentCntr .story_imgs {
  clip-path: polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%);
}
.story_image_contentCntr .story_imgs:first-child {
  clip-path: polygon(100% 0%, 0% 0%, 0% 100%, 100% 100%);
} */

.story_image_contentCntr {
  position: absolute;
  width: 100%;
  height: 100%;
}
.story_content .story_imgs {
  position: absolute;
  width: 100%;
  height: 100%;
  aspect-ratio: 1;
  left: 50%;
  transform: translateX(-50%);
}
.story_content .story_imgs img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  /* position: absolute; */
  /* left: 0; */
  /* clip-path: polygon(0 0, 0 0, 0 100%, 0% 100%); */
  /* transform-origin: left; */
  /* inset: 0; */
}
.story_reelstext_cntr {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  z-index: 9;
  padding: 2rem 1rem;
  background: linear-gradient(to bottom, transparent 60%, black);
}

.indices {
  width: 100%;
  height: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.25em;
  position: absolute;
  z-index: 9;
  padding: 1rem;
}
.indices::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  background: linear-gradient(rgba(0, 0, 0, 0.5), transparent);
}
.indices .index {
  width: 100%;
  position: relative;
  height: 2px;
  background-color: rgba(255, 255, 255, 0.25);
}
.indices .index .index-highlight {
  position: absolute;
  top: 0;
  left: 0;
  width: 0%;
  height: 100%;
  background-color: #fff;
  transform: scaleX(100%);
  transition: width 1.5s linear;
}
.reel_profile {
  position: relative;
  width: 100%;
  height: 60px;
  display: flex;
  gap: 1em;
  align-items: center;
  color: #fff;
  font-family: var(--primary-font-Neue-Light);
}
.reel_profile .profile-icon {
  position: relative;
  width: 40px;
  height: 40px;
  border-radius: 100%;
  overflow: hidden;
  background-color: #fff;
}
.reel_profile .profile-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}
.reel_profile .profile-name {
  position: relative;
  width: 200px;
  height: 20px;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}
/* .story_content_row {
  position: absolute;
} */
/* new code */
.main_div {
  margin: 10px;
  width: 300px;
  height: 70px;
  position: relative;
  border-radius: 45px;
  transition: all 0.3s ease;
  cursor: pointer;

  color: black; /* background-color: black; */
}

.content-div {
  display: flex;
  height: 100%;
}
.img-div {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: yellow;
  overflow: hidden;
  z-index: 2;
}
.img-div img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}
.btn-div {
  width: calc(100% - 70px);
  height: 100%;
  display: flex;
  align-items: center;
  color: black;
  /* justify-content: center; */
  padding: 10px;

  font-family: var(--primary-font-Neue-Light);
  font-size: 1.3rem;
}
.nextbtn_div {
  justify-content: flex-end;
}
.inner-text-div {
  position: absolute;
  top: 0;
  /* left: 70px; */
  width: 230px;
  height: 100%;
  align-items: center;
  justify-content: center;
  display: flex;
  padding: 2px 10px;
  /* padding-left: 80px; */
  color: white;
  font-family: var(--primary-font-Neue-Light);
}
.prev_divtext {
  left: 70px;
}
.text-div {
  position: absolute;
  top: 0;
  /* left: 0; */
  width: 70px;
  height: 70px;
  z-index: 1;
  border-radius: 50px;
  transition: all 0.3s ease;
  overflow: hidden;
  background-color: black;
}
.next_divtextcntr {
  right: 0 !important;
}
.next_divtext {
  right: 70px !important;
  text-align: right;
}
.main_div:hover .text-div {
  width: 100%;
}
.main_div:hover .next_divtext {
  /* width: 100%; */
}
/* .next_div:hover .text-div {
  width: 100%;
} */
/* new code */
.story_content_row.bottom a {
  color: #fff !important;
  text-decoration: underline;
  font-weight: 600;
  letter-spacing: 0.05em;
}
.story_content_row.bottom {
  color: #fff;
  font-family: var(--primary-font-Neue-Light);
  text-align: center;
  display: flex;
  align-items: center;
  flex-direction: column;
  gap: 1rem;
}
.story_content_title-cntr h1 {
  font-size: 1rem;
  margin-bottom: 5px;
  letter-spacing: 0.05em;
}
.story_content_title-cntr p span {
  font-size: 1rem;
  margin-bottom: 5px;
  /* letter-spacing: 0.05em; */
  color: #fff !important;
}
/* .story_content_title-cntr p span em {
  color: #fff !important;
} */
.story_content_title-cntr em {
  color: #fff !important;
}
.story_content_row_credit a {
  color: #fff;
}
.story_content_title {
  position: relative;
  width: 100%;
  /* height: 42px; */
  clip-path: polygon(0 0, 100% 0, 100% 100%, 0% 100%);
}
.webstorieReels_bottom {
  width: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 5rem;
  bottom: 100px;
  z-index: 9;
  /* right: 100px; */
}
.prev_btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
  height: fit-content;
  position: relative;
}
.cylinderical_bg {
  width: 300px;
  height: 100px;
  /* background-color: red; */
  position: relative;
  color: #000000;
  padding: 0 5rem;
  font-family: var(--primary-font-Neue);
}
.cylinderical_bg_inner {
  display: flex;
  align-items: center;
  /* justify-content: center; */
  /* gap: 2rem; */
  width: 100%;
  height: 100%;
  /* position: relative; */
  /* z-index: 9; */
  cursor: pointer;
}
.cylinderical_bg_curved {
  width: 50px;
  height: 50px;
  position: absolute;
  top: 50%;
  right: 0;
  background-color: rgb(0, 0, 0);
  border-radius: 50%/50%;
  transform: translateY(-50%);
  display: flex;
  justify-content: flex-end;
  transition: all 0.3s ease;
  /* overflow: hidden; */
  color: #fff;
  pointer-events: none;
}
.cylinderical_bg_curved h1 {
  padding: 0 1.2rem;
  font-size: 1.5vw;
  font-family: var(--primary-font-Neue);
  position: relative;
  z-index: 8;
}
.prev_btn .cylinderical_bg_curved {
  left: 0 !important;
  justify-content: space-between;
  align-items: center;
}
.prev_btn .cylinderical_bg_inner {
}
.cylinderical_bg_inner._next {
  justify-content: flex-end;
}
.reel_profile_next {
  position: relative;
  /* width: 100%; */
  display: flex;
  gap: 1em;
  align-items: center;
  color: #fff;
  height: 100%;
  z-index: 9;
}
.reel_profile_next .profile-icon_next {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 100%;
  overflow: hidden;
  background-color: #000000;
}
.circle_icon {
  align-items: center;
  bottom: 0;
  color: #0d0e13;
  color: gray;
  display: flex;
  justify-content: center;
  left: 0;
  /* opacity: 0; */
  overflow: hidden;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}
.circle_icon svg {
  height: 2vw;
  width: 1.5vw;
  transition: all 0.3s ease;
}
.reel_profile_next .profile-icon_next img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  /* scale: 0; */
  border-radius: 50%;
  transition: all 0.3s ease;
}
.prevNextBtnWrapper {
  width: 60%;
}
@media only screen and (min-width: 1199px) {
  .prevNextBtnWrapper {
    width: 50%;
  }
}
@media only screen and (min-width: 1400px) {
  .prevNextBtnWrapper {
    width: 40%;
  }
}
@media only screen and (min-width: 1800px) {
  .prevNextBtnWrapper {
    width: 35%;
  }
}
.arrowsDir {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 99;
  /* background: red; */
  /* width: 50%; */
}
.arrowsDir svg {
  font-size: 16px;
}
.prevCntrArrow {
  left: 0;
  background-color: #000000;
  color: #fff;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  flex-shrink: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: -1rem;
  cursor: pointer;
}
.nextCntrArrow {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  right: 0;
  background-color: #000000;
  color: #fff;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  flex-shrink: 1;
  margin-right: -1rem;
  cursor: pointer;
}
.nextCntr,
.prevCntr {
  position: absolute;
  bottom: 0;
  cursor: pointer;
  width: 50%;
  pointer-events: all;
  height: 100%;
  z-index: 9;
  cursor: none;
}
.prevCntr {
  left: 0;
}
.nextCntr {
  right: 0;
}
.webstorieclose {
  position: fixed;
  right: 5rem;
  top: 3rem;
  z-index: 99999 !important;
  padding: 10px;
  background-color: black;
  border-radius: 50%;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer !important;
}
.webstorieclose svg {
  font-size: 1.5rem;
}
.prev_btn:hover .cylinderical_bg_curved {
  width: 100%;
  /* height: 100%; */
  border-radius: 999px;
  cursor: none;
}
.prev_btn .cylinderical_bg_curved .title-mask {
  position: absolute;
  right: 15%;
  /* display: none; */
  /* visibility: hidden; */
}
.title-mask .title-line {
  height: 1.2rem;
  overflow: hidden;
}
.title-mask .title-line h4 {
  font-size: 1rem;
  transform: translateY(100%);
}
/* .prev_btn:hover .cylinderical_bg_curved .title-mask {
  opacity: 1;
  display: initial;
  visibility: visible;
} */
.prev_btn:hover .circle_icon svg {
  /* scale: 0; */
}
.prev_btn:hover .reel_profile_next .profile-icon_next img {
  /* scale: 1; */
}
.next_btn {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 1rem;
  position: relative;
}
.next_btn .cylinderical_bg_curved {
  right: 0 !important;
  /* justify-content: space-between; */
  justify-content: flex-end;
  align-items: center;
}
.next_btn:hover .cylinderical_bg_curved {
  width: 100%;
  height: 100%;
  border-radius: 999px;
  cursor: none;
}
.next_btn .cylinderical_bg_curved .title-mask {
  position: absolute;
  left: 15%;
  /* display: none; */
  /* visibility: hidden; */
}
/* .next_btn:hover .cylinderical_bg_curved .title-mask {
  display: initial;
  visibility: visible;
} */
.next_btn:hover .circle_icon svg {
  scale: 0;
}
.next_btn:hover .reel_profile_next .profile-icon_next img {
  scale: 1;
}
@media only screen and (max-width: 767px) {
  .webstorieReels_bottom {
    display: none;
  }
  .webstorieReels {
    cursor: none;
  }
  .reel-cursor {
    display: none;
  }
  .webstorieclose {
    top: 2rem;
    right: 1rem;
  }
  .story_content {
    position: relative;
    width: 100%;
    height: 100%;
    inset: 0;
    transform: translate(0);
  }
  .MuiBox-root {
    width: 20% !important;
  }
  .cylinderical_bg_inner h1 {
    color: #fff;
  }
}
@media only screen and (max-width: 992px) {
  /* .story_content {
    height: 90%;
  }
  .cylinderical_bg_inner h1 {
    color: #000000;
  }
  .story_content {
    border-radius: 5px;
  } */
  .main_div {
    width: 250px;
  }
  .webstorieReels_bottom {
    padding: 0 1rem;
  }
  .inner-text-div {
    font-size: 12px;
    width: 175px;
  }
  .btn-div {
    font-size: 1rem;
  }
}
/* webstorieReels

/* tag_cntr */
.authorWapper_mainCntr {
  position: relative;
  background-color: #faf7f3;
  z-index: 9;
  padding: 8rem 0 0;
}
.authorWrapper {
  position: relative;
  overflow: hidden;
  /* border-bottom-right-radius: 1.25rem; */
  /* border-bottom-left-radius: 1.25rem; */
  /* padding-top: 4rem !important; */
  background-color: #faf7f3;
  margin: 0 auto;
  max-width: 75rem;
  padding: 0 0.938rem 0.625rem 0.938rem;
}
.authorWrapper img {
  display: block;
  object-fit: cover;
  width: 125px;
  height: 125px;
  border-radius: 50%;
  margin: 0 auto;
}

.authorCntr_inner_name {
  text-align: center;
  font-weight: 500;
  margin-bottom: 0;
  font-size: 2em;
  margin: 0.67em 0;
  font-family: var(--primary-font-scotch-display);
}
.Author_followUs {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-bottom: 10px;
}
.Author_followUs a {
  position: relative;
  width: 25px;
  height: 25px;
  padding: 2px;
  color: inherit !important;
}
.Author_followUs svg {
  width: 25px;
  height: 25px;
  color: #db242a;
  transition: all 0.3s ease;
}
.authorCntr_inner_para {
  text-align: center;
  font-size: 1.1875rem;
  line-height: 1.6;
  font-family: var(--primary-font-Neue);
}
@media (min-width: 40.625rem) {
  .authorCntr_inner_name {
    font-size: 2.125rem;
    line-height: 1.2;
  }
  .authorWapper_mainCntr {
    padding: 8rem 0 0;
  }
}
@media (min-width: 48rem) {
  .authorWrapper {
    padding: 0 2.5rem 0.9375rem 2.5rem;
  }
}
@media (min-width: 64rem) {
  .authorCntr_inner_name {
    font-size: 2.75rem;
    line-height: 1.2;
  }
}

@media (min-width: 750px) {
  .authorCntr_text_inner {
    gap: 1.875rem;
    position: relative;
    z-index: 10;
  }
}
@media only screen and (min-width: 1000px) {
  .authorCntr {
    position: absolute;
    height: 100%;
    width: 50%;
    background-color: rgba(255, 223, 225);
  }
  .authorCntr_text {
    /* padding-left: 1.25rem; */
    /* padding-right: 1.25rem; */
    /* padding-left: 0.9375rem; */
    /* padding-right: 0.9375rem; */
  }
  .authorCntr_text_inner_col {
    grid-column: span 8 / span 8;
    padding-left: 45px;
    padding-right: 45px;
    height: 100svh;
  }

  .authorCntr_text_inner_colbtm {
    padding-bottom: 3.75rem;
  }
  .wysiwyg {
    font-size: 2rem;
    line-height: 1;
    font-family: var(--primary-font-Neue-Medium);
  }
  .authorCntr_text_title {
    font-size: 5vw;
  }
}
/* tag_cntr */

/* tagsWrapper */
.tagsWrapper {
  width: 100%;
  /* height: 100vh; */
  background-color: #faf7f3;
  position: relative;
  z-index: 9;
  padding: 8rem 0;
  overflow: hidden;
  border-bottom: 1px solid #000;
}
.tagsTitle {
  font-size: 1.5rem !important;
  font-family: var(--primary-font-scotch-display-compress);
  text-align: center;
  padding: 3rem 0;
  font-weight: bold;
}
.tagsTitle h1 {
  display: inline;
  font-size: 1.5rem !important;
}
@media only screen and (min-width: 992px) {
  .tagsTitle {
    font-size: 2.5rem !important;
    padding: 4rem 0;
    font-weight: bold;
  }
  .tagsTitle h1 {
    font-size: 2.5rem !important;
  }
}
/* tagsWrapper */

/* webstoriesPage_cntr */
.webstoriesPage_cntr {
  background-color: #faf7f3;
  overflow: hidden;
  z-index: 9;
  position: relative;
  border: 1px solid #000;
  padding: 3rem 0;
}
.webstories_grid_wrapper_desktop {
  display: none;
}
.webstories_grid_wrapper_mbl {
  background-color: #faf7f3;
  position: relative;
  z-index: 9;
}

/* webstoriesPage_cntr */

/* webstories_itemCard new */
.webstories_itemCard {
  position: relative;
  overflow: hidden;
  font-family: var(--primary-font-Neue-Medium);
}
.webstories_itemCard a {
  display: block;
  color: #fff;
  text-decoration: none;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  border-radius: 5px;
  box-shadow: 2px 2px 6px 0 rgba(0, 0, 0, 0.15);
  overflow: hidden;
  position: relative;
}
.webstories_itemCard a .gradient {
  padding: 10px;
  display: -webkit-flex;
  display: flex;
  /* flex-wrap: wrap; */
  flex-direction: column;
  align-items: center;
  align-items: unset;
  justify-content: space-between;
  padding: 30px 12px 12px;
  position: absolute;
  left: 0;
  bottom: 0;
  background: linear-gradient(to top, rgb(0, 0, 0), transparent 100%) 0% 0% /
    calc(100% + 1px) calc(100% + 1px);
  /* border-top: dashed 5px #fff; */
  border-width: 1px 0 0 0;
  border-image-source: url(/res/images/border-dash.png);
  border-image-slice: 1;
  border-image-repeat: round;
  width: 100%;
  /* min-height: 118px; */
  box-sizing: border-box;
  gap: 1rem;
}
.gradient h2 {
  font-size: 1.1rem;
  line-height: 1;
  font-family: var(--primary-font-Neue);
}
.webstories_grid_wrapper_mbl .webstories_itemCard .relatedCard figure {
  aspect-ratio: 4/5 !important;
  margin: 0;
}
/* webstories_itemCard new */
@media only screen and (min-width: 992px) {
  .webstories_grid_wrapper_desktop {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(3, auto);
    gap: 20px;
    background-color: #faf7f3;
    position: relative;
    width: 80%;
    margin: 0 auto;
  }
  .col {
    flex: 1;
    overflow: hidden; /* Ensure content overflow is hidden */
    /* height: 100%;
    display: grid;
    overflow: hidden; */
    display: flex;
    row-gap: 2rem;
    overflow: hidden;
    flex-direction: column;
  }
  /* .col_2 {
    margin-top: 5rem;
  } */
  .webstories_grid_wrapper_mbl {
    display: none;
  }
  .webstories_grid_wrapper_desktop .webstories_itemCard .relatedCard figure {
    aspect-ratio: 4/7 !important;
    margin: 0;
  }
}
/* privacypolicy */

.PrivacypolicyCntr {
  position: relative;
  overflow: hidden;
  z-index: 9;
  background-color: #faf7f3;
  padding: 8rem 0;
  border-bottom: 1px solid #000;
}

.privacypolicy_heading {
  text-align: center;
  font-weight: 500;
  margin-bottom: 0;
  font-size: 2em;
  /* margin: 0.67em 0; */
  font-family: var(--primary-font-scotch-display);
}
.entry_content {
  counter-reset: footnotes;
}
.entry_content p {
  font-size: 20px;
  font-weight: 300;
  line-height: 36px;
  font-family: var(--primary-font-Neue);
  margin-bottom: 1rem;
}
.entry-content strong {
  font-weight: bolder;
}

/* privacypolicy */
/* TermsUseCntr */
.TermsUseCntr {
  position: relative;
  overflow: hidden;
  z-index: 9;
  background-color: #faf7f3;
  padding: 8rem 0;
  border-bottom: 1px solid #000;
}
.TermsUse_heading {
  text-align: center;
  font-weight: 500;
  margin-bottom: 0;
  font-size: 2em;
  /* margin: 0.67em 0; */
  font-family: var(--primary-font-scotch-display);
}

/* TermsUseCntr */

/* DisclaimerCntr */
.DisclaimerCntr {
  position: relative;
  overflow: hidden;
  z-index: 9;
  background-color: #faf7f3;
  padding: 8rem 0;
  border-bottom: 1px solid #000;
}
.pageInner li {
  font-size: 20px;
  font-weight: 300;
  margin-bottom: 20px;
  font-family: var(--primary-font-Neue);
  line-height: 1.3;
}
.pageInner li a {
  color: rgb(13 110 253);
  text-decoration: underline;
}
/* DisclaimerCntr */

/* ContactUs */
.ContactUsCntr {
  position: relative;
  overflow: hidden;
  z-index: 9;
  background-color: #faf7f3;
  padding: 8rem 0;
  border-bottom: 1px solid #000;
}
.ContactUs_heading {
  text-align: center;
  font-weight: 500;
  margin-bottom: 0;
  font-size: 2em;
  margin: 0.67em 0;
  font-family: var(--primary-font-scotch-display);
}
.pageInner a {
  color: rgb(13 110 253);
  text-decoration: underline;
}
/* ContactUs */
@media only screen and (min-width: 1401px) and (max-width: 1650px) {
  .entry_content p {
    font-size: 20px;
    font-weight: 300;
    line-height: 30px;
  }
}
@media only screen and (max-width: 991px) {
  .PrivacypolicyCntr,
  .TermsUseCntr,
  .DisclaimerCntr,
  .AboutandTeamCntr {
    padding: 8rem 0;
  }
  .entry_content p {
    font-size: 18px;
    line-height: 24px;
  }
  .pageInner {
    width: 100%;
  }
}
/* Page Loader */
.loader-cont {
  position: fixed;
  z-index: 20;
  top: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #faf7f3;
}
.MuiLinearProgress-root {
  position: relative;
  overflow: hidden;
  display: block;
  z-index: 0;
  background-color: rgb(220, 220, 220) !important;
  height: 2px !important;
}

.MuiBox-root {
  width: 15% !important;
}

.MuiLinearProgress-bar {
  background-color: #000 !important;
}

.MuiLinearProgress-bar1 {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  -webkit-transition: -webkit-transform 0.2s linear;
  transition: transform 0.2s linear;
  transform-origin: left;
  background-color: #000;
  width: auto;
  -webkit-animation: animation-1 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395)
    infinite;
  animation: animation-1 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite;
}

.MuiLinearProgress-bar2 {
  width: 100%;
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  -webkit-transition: -webkit-transform 0.2s linear;
  transition: transform 0.2s linear;
  transform-origin: left;
  --LinearProgressBar2-barColor: #000;
  background-color: var(--LinearProgressBar2-barColor, currentColor);
  width: auto;
  -webkit-animation: animation-2 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s
    infinite;
  animation: animation-2 2.1s cubic-bezier(0.165, 0.84, 0.44, 1) 1.15s infinite;
}

@keyframes animation-1 {
  0% {
    left: -35%;
    right: 100%;
  }

  60% {
    left: 100%;
    right: -90%;
  }

  100% {
    left: 100%;
    right: -90%;
  }
}

@keyframes animation-2 {
  0% {
    left: -200%;
    right: 100%;
  }

  60% {
    left: 107%;
    right: -8%;
  }

  100% {
    left: 107%;
    right: -8%;
  }
}

/* notfound */
.not-found-div {
  padding-top: 4rem;
  position: relative;
  z-index: 9;
  background-color: #faf7f3;
  border-bottom: 1px solid #000;
}
.error-head {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  /* margin-top: 150px; */
  height: 100vh;
  /* margin-inline: 250px; */
  line-height: 1;
  gap: 2rem;
}
.error-p-main {
  font-size: 100px;
  /* font-size: 18px; */
  font-family: var(--primary-font-scotch-display);
  font-weight: 300;
  font-style: normal;
}
.error-p-main2 {
  font-size: 50px;
  /* margin: -30px 0 0; */
  font-weight: 500;
  font-family: var(--primary-font-scotch-display);
}
.error-p-main3 {
  font-size: 1.2rem;
  font-family: var(--primary-font-Neue);
  text-align: center;
}
/* @media only screen and (min-width: 1024px) {
  body {
    font-size: 16px;
    line-height: 23px;
  }
} */
/* notfound */
.card-actions_actions_button.arrowbtn {
  /* z-index: 999; */
  border-radius: 50% !important;
  height: 55px;
  width: 55px;
  background-color: #ffffff;
  color: #000000;
  border: 1.5px solid #000000;
}
.card-actions_actions_button.arrowbtn:before {
  background-color: #000000;
}
.card-actions_actions_button.arrowbtn svg {
  font-size: 25px;
  z-index: 9;
  color: #000000;
  transition: all 0.3s ease;
  opacity: 1 !important;
  /* mix-blend-mode: difference; */
}
.card-actions_actions_button.arrowbtn:hover {
  background-color: #000000;
  border: 1.5px solid #ffffff;
  color: #ffffff;
}
.card-actions_actions_button.arrowbtn:hover svg {
  color: #fff;
}
/* searchNavbar */
.card-actions_actions_button.searchbtn {
  z-index: 999;
  border-radius: 50% !important;
  height: 55px;
  width: 55px;
  background-color: #000;
  color: #fff;
  position: fixed;
  right: 2rem;
  bottom: 7rem;
  /* border: 1px solid #faf7f3; */
}
.card-actions_actions_button.searchbtn:before {
  background-color: #fff;
}
.card-actions_actions_button.searchbtn svg {
  font-size: 25px;
  z-index: 9;
  color: #fff;
  transition: all 0.3s ease;
  mix-blend-mode: difference;
}
.card-actions_actions_button.searchbtn:hover {
  background-color: #fff;
  color: #000;
}
.card-actions_actions_button.buttonTop {
  z-index: 999;
  border-radius: 50% !important;
  height: 55px;
  width: 55px;
  background-color: #000;
  color: #fff;
  position: fixed;
  right: 2rem;
  bottom: 2rem;
  /* border: 1px solid #faf7f3; */
}
.card-actions_actions_button.buttonTop:before {
  background-color: #fff;
}
.card-actions_actions_button.buttonTop svg {
  font-size: 25px;
  z-index: 9;
  color: #fff;
  transition: all 0.3s ease;
  mix-blend-mode: difference;
}
.card-actions_actions_button.buttonTop:hover {
  background-color: #fff;
  color: #000;
}
.searchNavbar {
  position: fixed;
  top: 0;
  height: 100vh;
  width: 100%;
  transition: all 0.4s ease;
  z-index: 9999;
  /* background-color: rgba(0, 0, 0, 0); */
}
.menu-inner {
  width: 40%;
  margin-left: auto;
  background-color: #000;
  /* transform: translateX(100%); */
  transition: transform 0.3s ease-in-out;
}
.manu-inner-block,
.menu-inner {
  height: 100vh;
  transition: all 0.3s ease-out;
}
.manu-inner-block {
  width: 100%;
}
.menu-top {
  display: flex;
  gap: 15px;
  padding: 3vh 3vw;
  height: 80px;
  align-items: center;
  justify-content: space-between;
}
.menu-search-close {
  display: flex;
  align-items: center;
  gap: 20px;
}
.flex-all {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1rem 0;
}
.t3 {
  transition: all 0.3s ease;
}
.menu-search {
  color: #000 !important;
  font-size: 13px;
  font-family: var(--primary-font-scotch-display);
  cursor: pointer;
  font-weight: 500;
  letter-spacing: 2px;
  gap: 4px;
  width: 120px;
  background-color: #fff;
  height: 40px;
  border-radius: 25px;
  font-style: normal;
  text-transform: uppercase;
  border: 1px solid #fff;
}
.menu-search:hover {
  background-color: #000000;
  color: #fff !important;
}
.menu-search svg {
  font-size: 20px;
}
.menu-close {
  width: 40px;
  transition: all 0.3s ease;
  height: 40px;
  border-radius: 50%;
  font-size: 19px;
  background-color: #fff;
  cursor: pointer;
}
.menu-main {
  height: calc(100% - 80px - 60px);
  overflow: auto;
  padding: 3vh 4vw;
  width: 100%;
}
.search-cont {
  padding: 30px 3vw;
}
.search-group {
  position: relative;
  display: flex;
  overflow: hidden;
  border-bottom: 1px solid #fff;
  transition: all 0.3s ease;
}
.search-bar {
  outline: none;
  color: #fff;
  border: none;
  background: none;
  width: 100%;
  font-size: 18px;
  font-family: var(--primary-font-scotch-display);
  transition: all 0.3s ease;
  font-weight: 400;
  letter-spacing: 1px;
  padding: 0.5rem 0;
}

.search-group:focus-within {
  border-bottom: 1px solid #888888; /* Change to your desired color */
}
.search-icon {
  position: absolute;
  color: #fff;
  right: 0;
  top: 6px;
  width: 25px;
  height: 25px;
  cursor: pointer;
}
.ts-cont {
  margin: 50px 0;
}
.ts-title {
  color: #fff;
  opacity: 0.8;
  font-size: 13px;
  font-family: var(--primary-font-scotch-display);
  font-weight: 400;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
}
.ts-title svg {
  font-size: 20px;
}
.ts-items {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 20px 0;
}
.ts-item {
  padding: 5px 23px;
  font-size: 14px;

  color: #fff !important;
  font-family: var(--primary-font-Neue);
  font-weight: 500;
  letter-spacing: 1px;
  cursor: pointer;
  border: 1px solid #fff;
  transition: all 0.3s ease;
  border-radius: 25px;
}
.ts-item:hover {
  color: #000 !important;
  background-color: #fff;
}

@media screen and (max-width: 900px) {
  .menu-inner {
    width: 100% !important;
  }
  .card-actions_actions_button.searchbtn {
    right: 0.5rem;
    bottom: 6.5rem;
    height: 50px;
    width: 50px;
  }
  .card-actions_actions_button.buttonTop {
    right: 0.5rem;
    bottom: 2.5rem;
    height: 50px;
    width: 50px;
  }
  .card-actions_actions_button.arrowbtn {
    width: 45px;
    height: 45px;
  }
}
/* searchNavbar */
/* result-page-main-div */

.result-page-main-div {
  padding: 100px 0px 50px;
  position: relative;
  z-index: 9;
  background-color: #faf7f3;
  border: 1px solid #000;
}
.tag-head {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 30px;
}
.tag-head-p-small {
  font-size: 1rem;
  font-family: var(--primary-font-Neue) !important;
}
.tag-head-h2 {
  font-family: var(--primary-font-scotch-display) !important;
  letter-spacing: 1.5px;
  font-weight: 500 !important;
  margin: 15px 0 30px 0;
  font-size: 30px;
}
.search-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.search-div-tag {
  width: 100%;
  border: 1px solid;
}
.search-div-tag {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-inline: 22px;
  border-radius: 40px;
  transition: border-bottom 0.3s ease;
}
.input-tag {
  padding: 6.2px 0px;
  width: 100%;
  border: none;
  font-size: 18px;
  border-radius: 40px;
  font-family: var(--primary-font-Neue) !important;
  background-color: #faf7f3;
  outline: none;
  font-weight: 300;
}
.search-div-tag:focus-within {
  border-color: darkgray;
}

.sorted_row {
  display: block;
  flex-wrap: nowrap;
}

.sort-main-tag {
  margin-top: 1rem !important;
  border-right: 1px solid rgb(200, 200, 200);
  width: 100% !important;
  padding-right: 30px;
  margin: 1rem 0;
}
.sort-div2 {
  /* border-bottom: 1px solid rgb(200, 200, 200); */
  /* padding-block: 20px; */
  padding-bottom: 20px;
  font-family: var(--primary-font-scotch-display);
}
.tag-select-div {
  width: 100%;
  height: 40px;
  padding-inline: 5px;
  border: 1px solid black;
  display: flex;
  font-size: 18px;
  font-family: var(--primary-font-Neue);
  padding: 0 1rem;
  border-radius: 20px;
}
.tag-select {
  width: 100%;
  font-size: 16px;
  border: none;
  font-family: var(--primary-font-Neue);
  outline: none;
  background: none;
  font-weight: 300;
}
.sort-div {
  /* border-bottom: 1px solid rgb(200, 200, 200); */
  padding-block: 20px;
  font-family: var(--primary-font-scotch-display);
}
.sd {
  margin-bottom: 5px;
}
.author-sort {
  width: 100%;
  border-bottom: 1px solid rgb(220, 220, 220);
  padding-block: 20px;
  transition: 0.4s;
  cursor: pointer;
}
.pannelfilter {
  display: block;
  /* max-height: 0; */
  overflow: hidden;
  transition: all 0.3s ease;
}
.sort-top {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-family: var(--primary-font-scotch-display);
}
.sort-clear {
  font-size: 13px;
  color: rgb(120, 120, 120);
  cursor: pointer;
  font-family: var(--primary-font-Neue) !important;
}
.sort_hide {
  height: 200px;
  overflow: hidden;
}
.pl-0 {
  padding-left: 0px !important;
}
.mt-0 {
  margin-top: 0px !important;
}
.mb-3 {
  margin-bottom: 1.5rem !important;
}
.sorted_row_4 {
  /* flex: 0 0 auto; */
  /* width: 33.33333333%; */
  /* padding: 0px 15px; */
}
.sorted_row_12 {
  flex: 0 0 auto;
  width: 100%;
}
.mainsection-tag {
  width: 100% !important;
}
.paginator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-inline: 0px;
}
.paginator-sq {
  display: flex;
  align-items: center;
  gap: 40px;
  color: rgb(120, 120, 120);
  font-size: 20px;
}
.paginator-p {
  font-family: var(--primary-font-scotch-display) !important;
  cursor: pointer;
  font-size: 22px;
  display: flex;
}
.row-sort {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 5px 0;
  font-family: var(--primary-font-Neue) !important;
  font-weight: 300;
}
.checkbox-filtername {
  display: flex;
  align-items: center;
  gap: 10px;
}
.filter-name-tag {
  font-size: 18px;
  cursor: pointer;
  margin: 0 !important;
  display: -webkit-box;
  max-width: 100%;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
@media (min-width: 768px) {
  .sorted_row {
    display: flex;
  }
  .sorted_row_4 {
    flex: 0 0 auto;
    width: 33.33333333%;
    padding: 0px 15px;
  }
  .sorted_row_12 {
    flex: 0 0 auto;
    width: 100%;
  }
  .sorted_row {
    display: flex;
    flex-wrap: wrap;
  }
  .sort-main-tag {
    width: 25% !important;
  }

  .mainsection-tag {
    width: 75% !important;
  }
  .paginator {
    padding-inline: 170px;
  }
}

/* result-page-main-div */

/* new bannerwrapper */
#page1 {
  position: relative;
  z-index: 9;
  height: 80vh;
  width: 100%;
  background-color: rgb(203, 203, 203);
}
.page2 {
  position: relative;
  z-index: 3;
  min-height: 100vh;
  width: 100%;
  padding: 0 1rem;
}
.see_more_section {
  position: relative;
  z-index: 3;
  min-height: 100vh;
  width: 100%;
  padding: 0 1rem;
}
.container5 {
  width: 100%;
  /* min-height: 100%; */
  /* display: flex;
  align-items: start;
  justify-content: space-between;
  flex-wrap: wrap; */
  /* margin-top: 6vw; */
  padding: 2rem 0;
  position: relative;
}
.card {
  width: 30%;
  height: 40vh;
  margin-bottom: 2vw;
  background-size: cover;
  background-image: url(https://images.unsplash.com/photo-1735369931158-4b54e5c7070b?q=80&w=1973&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D);
}
.top {
  position: relative;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 9;
  /* background-color: rgb(3, 13, 24); */
}
.page2lastcntr {
  padding-bottom: 0 !important;
}
.paddingtop {
  padding-top: 0px !important;
}
@media only screen and (min-width: 992px) {
  .top {
    position: absolute;
    height: 100vh;
  }
  .page2 {
    padding: 0 2rem;
    min-height: 100vh;
    position: relative;
  }

  .see_more_section {
    padding: 0 2rem;
    min-height: 100vh;
    position: relative;
  }
  .container5 {
    padding: 5rem 0;
    /* transform: translateY(-60px); */
  }
  .paddingbtm {
    padding-bottom: 0 !important;
  }
  .paddingtop {
    padding-top: 40px !important;
  }
}
/* new bannerwrapper */

/* testimonial */
.new_testimonial_design {
  position: relative;
  z-index: 9;
  background-color: #000000;
  border-bottom: 1px solid #ffffff;
  color: #fff;
}
.section__color-wrapper {
  padding-bottom: 24px;
  padding-top: 70px;
  /* display: grid; */
  /* gap: 10px; */
  /* grid-template-columns: 1fr 1fr; */
  overflow: hidden;
}
.content-wrapper {
  position: relative;
  display: grid;
  grid-template-columns: 1fr;
  grid-template-rows: 1fr;
  grid-template-areas: ".";
  width: 70%;
  /* justify-items: center; */
  /* align-items: center; */
}
.content-wrapper .content {
  /* padding: $g-spacing; */
  opacity: 0;
  width: 100%;
  /* max-width: 900px; */
  transition-duration: 1.5s, 0.6s;
  transition-property: opacity, transform;
  color: #ffffff;
}
.content-wrapper .content:first-child {
  margin: 0;
}
.swiper-slide {
  margin: 0;
  height: auto;
  width: 100%;
}
.content-wrapper .content {
  opacity: 1;
  /* text-align: center; */
  display: grid;
  /* justify-items: center; */
  /* align-items: center; */
}
.swiper-nav-wrapper {
  position: relative;
  bottom: 0;
  left: 0;
  /* right: 0; */
  display: flex;
  /* justify-content: center; */
  /* align-items: center; */
  /* margin: 0 auto; */
  width: 100%;
  z-index: 9;
  gap: 20px;
  margin-top: 32px;
}
.swiper-button-next,
.swiper-button-prev {
  top: 0;
  top: auto;
  left: auto;
  right: auto;
  position: relative !important;
}
.testimonial-list_wrapper {
  display: grid;
  grid-template-columns: 1fr;
  /* gap: 40px; */
  position: relative;
}
.swiper-horizontal {
  overflow: hidden !important;
}
.swiper-wrapper {
  /* width: 60% !important; */
  /* overflow: hidden; */
  /* position: relative; */
}
.testimonial-list_wrapper_main {
  display: grid;
  grid-template-columns: 1fr;
  gap: 30px;
}
.testimonial__content {
  font-size: 1.8rem;
  font-weight: 400;
  margin-bottom: 25px;
  color: #ffffff;
  line-height: 1;
  font-family: var(--primary-font-scotch-display);
}
.testimonial__content label {
  font-size: 1.8rem;
  font-weight: 300;
  color: #fff;
  line-height: 1;
  font-family: var(--primary-font-scotch-display);
}
/* .testimonial__content label span {
  font-size: 3rem;
  margin-top: 15px;
  font-family: var(--secondary-font-Bitter);
} */
.testimonial__content span {
  display: block;
  font-family: var(--primary-font-scotch-display);

  /* font-size: 2.2rem; */
  /* font-weight: bold; */
  /* margin-top: 15px; */
}
.testimonial__content h3 {
  display: block;
  font-family: var(--primary-font-scotch-display);

  font-size: 1.6em;
}
.testimonial__star_main {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 15px;
}
.testimonial_title_image {
  position: relative;
  width: 14rem;
  height: 8rem;
  overflow: hidden;
}
.testimonial_title_image img {
  filter: invert(1);
  object-fit: contain;
}
.testimonial__star_main svg {
  height: 24px;
  width: 24px;
}
.content p {
  font-family: var(--primary-font-Neue);
}
.testimonial__author {
  /* margin-top: 30px; */
  font-family: var(--primary-font-Neue);
}
.testimonial_product {
  /* background-color: #fff; */
}
/* .tp_wrapper {
  padding: 44px 44px;
} */
.tp_media {
  display: grid;
  place-content: center;
}
.tp_image {
  /* max-width: 100%; */
  /* margin-left: auto; */
  /* margin-right: auto; */
  position: relative;
  width: 320px;
  height: 420px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.tp_image img {
  aspect-ratio: 1;
  object-fit: contain;
}
.tp_image_navImg {
  position: relative;
  /* width: 160px; */
  /* height: 205px; */
  /* height: 190px; */
  overflow: hidden;
  width: 130px;
  height: 160px;
}
.tp_button {
  width: 100%;
  margin: auto;
  margin-top: 5px;
  border-radius: 999px;
}
.tp_button li {
  width: 100%;
}
.tp_button li button {
  width: 100%;
}
.tp_button li button:hover {
  border: 1px solid #fff;
}
.tp_button li button span {
  font-size: 16px;
}
.new_testimonial_design .arrows_arrow svg {
  color: #0057d9 !important;
  font-size: 20px;
}
/* .borderWhtarrow {
  border: 1.5px solid #fff !important;
} */
@media screen and (max-width: 768px) {
  .section__color-wrapper {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }
  .testimonial__content {
    font-size: 1rem;
  }
  .testimonial__content label {
    font-size: 1.3rem;
  }
  .testimonial__content label span {
    font-size: 1.5rem;
  }
  .testimonial__content span {
    font-size: 1.2rem;
  }
  .testimonial__content h3 {
    font-size: 1.4em;
  }
  .testimonial__author {
    font-size: 12px;
  }
  .swiper-nav-wrapper {
    margin-top: 0px;
  }
  .testimonial_title_image {
    width: 6rem;
    height: 4rem;
  }
  .tp_image {
    width: 156px;
    height: 200px;
  }
}
@media screen and (min-width: 992px) {
  .section__color-wrapper {
    gap: 10px;
    /* grid-template-columns: 2fr 1fr; */
    padding-bottom: 94px;
    padding-top: 94px;
  }
  .testimonial-list_wrapper_main {
    grid-template-columns: 3fr 1fr;
  }
  .testimonial-list_wrapper {
    display: grid;
    grid-template-columns: 1fr;
  }
  /* .testimonial-list__nav {
    margin-top: 32px;
  } */
  .content-wrapper .content {
    gap: 15px;
  }
  .swiper-nav-wrapper {
    margin-top: 0;
  }
  /* .swiper-nav-wrapper {
    position: absolute;
  } */
}
/* testimonial */

.addstyles {
  /* background-color: red; */
  margin: 2.5rem 0;
}

/* backtotopbutton */
/* .buttonTop {
  display: inline-block;
  background-color: #ffffff;
  width: 55px;
  height: 55px;
  text-align: center;
  border-radius: 50%;
  position: fixed;
  top: 90%;
  right: 30px;
  transition: background-color 0.3s, opacity 0.5s, visibility 0.5s;
  opacity: 0;
  visibility: hidden;
  z-index: 999;
  display: block;
  place-content: center;
  mix-blend-mode: difference;
} */
/* .buttonTop::after {
  content: "\f077";
  font-family: FontAwesome;
  font-weight: normal;
  font-style: normal;
  font-size: 2em;
  line-height: 50px;
  color: #fff;
} */
/* .buttonTop svg {
  font-size: 1.5rem;
}
.buttonTop:hover {
  cursor: pointer;
  background-color: #333;
  color: #fff;
}
.buttonTop:active {
  background-color: #555;
}
.buttonTop.show {
  opacity: 1;
  visibility: visible;
} */
/* @media only screen and (max-width: 991px) {
  .buttonTop {
    top: 90%;
    right: 10px;
    width: 50px;
    height: 50px;
  }
  .buttonTop svg {
    font-size: 1.5rem;
  }
} */
/* backtotopbutton */

/* modalfilter */
.modalfilterwrapper {
  display: flex;
  position: fixed;
  left: 0px;
  top: 0px;
  margin: 0px;
  width: 100%;
  height: 100vh;
  z-index: 99;
  overflow-y: scroll;
  padding: 0px;
  /* --tw-bg-opacity: 0.5; */
  background-color: rgba(24, 24, 24);
  transition: transform 0.3s ease-in-out;
  padding: 3rem 0;
  display: none;
}
.filterclosebtn svg {
  font-size: 2rem;
}
.modalfiltercntr {
  position: relative;
  --tw-bg-opacity: 1;
  background-color: #faf7f3;
  margin: auto auto auto 0px;
  box-shadow: 0 0 transparent, 0 0 transparent, 0 0 transparent 0 0 transparent,
    0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-radius: 0px;
  width: 100%;
  /* max-width: 28rem; */
  min-height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  outline: none !important;
}
.modalfiltercntrtop {
  display: flex;
  position: relative;
  width: 100%;
  padding: 1.5rem;
  justify-content: space-between;
  align-items: center;
  border-bottom-width: 1px;
  outline: none !important;
}
.modalfiltercntrcenter {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
  display: flex;
  width: 100%;
  flex: 1 1 0%;
  flex-direction: column;
  padding-bottom: 1rem;
  outline: none !important;
}
.accordion {
  border-bottom-width: 1px;
}
.heading-Namecntr {
  display: flex;
}
.arrow-dropdown svg {
  font-size: 24px;
  line-height: 1.333;
  width: 1em;
  height: 1em;
  outline: none !important;
}

/* modalfilter */
.filterBtn {
  display: none;
}
@media only screen and (max-width: 768px) {
  .modalfilterwrapper {
    display: block;
  }
  .moblienone {
    display: none;
  }
  .filterBtn {
    display: initial;
  }
  .filterBtn span {
    text-transform: uppercase;
  }
}
/* gradiant removed form banner */
.blog-img-container.gradient_remove_bg::before {
  display: none;
}
/* gradiant removed form banner */

/* webstories css */
.home-section1 {
  position: relative;
  height: 70vh;
  width: 100%;
  overflow: hidden;
}
.home-slide-container {
  position: relative;
  width: 100%;
  height: 100%;
}
.home-slide-container .hero-slide:nth-child(1) {
  z-index: 2;
}
.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: black;
}
.hero-slide img,
.hero-slide video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.home-slide-text {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 5;
  width: 100%;
  height: 100%;
  color: white;
  display: flex;
  flex-direction: column;
  align-items: start;
  justify-content: end;
  padding-bottom: 3vw;
  padding-left: 5vw;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.919) 5%, transparent);
}
.hero-title {
  position: relative;
  height: 5.5vw;
  width: 100%;
  margin-bottom: 2vw;
  overflow: hidden;
}
.home-slide-text h1 {
  position: absolute;
  left: 0%;
  top: 100%;
  font-size: 6vw;
  font-weight: 600;
  line-height: 0.9;
  text-align: center;
  text-transform: uppercase;
  font-family: headingBold;
}

.home-slide-text h1:nth-child(1) {
  top: 0%;
}
.hero-para {
  position: relative;
  width: 40%;
  height: 4vw;
  overflow: hidden;
  margin-bottom: 2vw;
}
.home-slide-text p {
  position: absolute;
  width: 100%;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  text-align: start;
  font-size: 1.5vw;
  text-transform: capitalize;
  line-height: 1.4;
}

.home-slide-text p:nth-child(1) {
  top: 0%;
}

.home-slide-bar-wrap {
  position: absolute;
  bottom: 3vw;
  right: 5vw;
  display: flex;
  align-items: center;
  justify-content: end;
  z-index: 7;
  gap: 15px;
}
.home-slide-bar {
  width: 100%;
  background-color: rgb(69, 69, 69);
  height: 2px;
  position: relative;
  border-radius: 5px;
}
.bar-loader {
  width: 0%;
  height: 100%;
  background-color: white;
}
/* webstories css */
