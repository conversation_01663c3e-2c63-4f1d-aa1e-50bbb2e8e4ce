import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get Category List
export const getCategory = async (body) => {
  const res = await fetch(Const.Link + `api/tenant/category-list?slug=${body.slug}&limit=${body.limit}&offset=${body.offset}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

export const getSubmenus = async (slug) => {
  const res = await fetch(Const.Link + `api/tenant/category-submenus?slug=${slug}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

export const getCategoryLatestAricles = async (body) => {
  const res = await fetch(Const.Link + `api/tenant/category-latest?slug=${body.slug}`, new Headers("GET")
  );
  return ProcessAPI(res);
};

// Get Category Sitemap
export const getCategorySitemap = async (slug) => {
  const res = await fetch(Const.Link + "api/tenant/category-sitemap", new Headers("GET"));
  return ProcessAPI(res);
};
