import React from "react";
import Image from "next/image";
import Link from "next/link";
import { getAuthorText } from "@/utils/Util";

const Card = ({
  title,
  image,
  altName,
  category,
  slug,
  timestamp,
  author,
  contributor,
  key = 0,
}) => {
  return (
    <div className="relatedContent" key={key}>
      <Link href={slug ?? "#"} className="relatedCard">
        <figure>
          <Image
            className="lazy loaded"
            src={image ?? ""}
            data-src={image ?? ""}
            alt={altName ?? ""}
            data-was-processed="true"
            fill
          />
        </figure>
        <div className="relatedPostContent">
          <strong>{category ?? ""}</strong>
          <h2>{title ?? ""}</h2>
          <span className="date-author">
            {getAuthorText("By", author, contributor)}
          </span>
        </div>
      </Link>
    </div>
  );
};

export default Card;
