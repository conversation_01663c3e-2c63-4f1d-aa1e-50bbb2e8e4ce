import { Const } from "@/utils/Constants";
import { getWebstoriesSitemap } from "@/pages/api/WebStoriesApi";
import { convertToISTISOString } from "@/utils/Util";

export default async function handler(req, res) {
  try {
    res.setHeader("Content-Type", "application/xml");
    const response = await getWebstoriesSitemap();
    const data = response && response.data.length > 0 ? response.data : [];
    if (data.length > 0) {
      const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
        ${data
          .map(
            (item) => `
              <url>
                <loc>${Const.ClientLink}${item?.loc || ""}</loc>
                <lastmod>${convertToISTISOString(item?.lastmod || "")}</lastmod>
                <changefreq>${item?.changefreq || "hourly"}</changefreq>
                <priority>${item?.priority || 0.9}</priority>
              </url>`
          )
          .join("")}
      </urlset>`;
      return res.status(200).send(sitemap);
    } else {
      return res.status(404).send();
    }
  } catch (error) {
    console.error("Error generating sitemap:", error.message);
    return res.status(500).send("Internal Server Error");
  }
}
