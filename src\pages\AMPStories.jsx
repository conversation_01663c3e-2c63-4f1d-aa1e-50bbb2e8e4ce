import { ampCSS } from "@/components/amp/ampCss";
import AMPHero from "@/components/amp/AMPHero";
import AMPNavbar from "@/components/amp/AMPNavbar";
import React from "react";

const AMPStories = ({
  _id,
  data,
  author,
  breadcrumbs,
  latest,
  related,
  tag,
  meta,
  slug,
  nextStories,
}) => {
  return (
    <div>
      <style jsx>{ampCSS}</style>
      <AMPNavbar />
      <div className="container">
        <AMPHero
          title={data?.title ?? ""}
          excerpt={data?.excerpt ?? ""}
          author={author ?? []}
          timestamp={data?.updatedAt ?? ""}
          breadcrumbs={breadcrumbs ?? []}
          // setOpenShare={setOpenShare}
          contributor={data?.contributor ?? []}
        />
      </div>
    </div>
  );
};

export default AMPStories;
