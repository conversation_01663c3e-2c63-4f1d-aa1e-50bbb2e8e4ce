import React from "react";
import { Tweet } from "react-tweet";
import Image from "next/image";
import Link from "next/link";
import { hasHtmlTags, getEmbedType, extractTwitterId } from "@/utils/Util";
import AstroWidget from "@/components/astrology/AstroWidget";

const renderListContent = (content) => {
  return content.map((node, index) => {
    switch (node.type) {
      case "paragraph":
        return (
          <p
            style={{
              ...(node.attrs?.textAlign && {
                textAlign: node.attrs.textAlign,
              }),
            }}
          >
            {renderContent(node.content)}
            <br />
          </p>
        );

      case "text":
        let textStyles = {};
        let isLink = false;
        let linkAttrs = {};

        if (node.marks) {
          node.marks.forEach((mark) => {
            if (mark.type === "textStyle") {
              textStyles.color = mark?.attrs?.color;
            }
            if (mark.type === "bold") {
              textStyles.fontWeight = "bold";
            }
            if (mark.type === "italic") {
              textStyles.fontStyle = "italic";
            }
            if (mark.type === "link") {
              isLink = true;
              linkAttrs = mark.attrs;
            }
          });
        }
        const textElement = (
          <span key={index} style={textStyles}>
            {node.text}
          </span>
        );
        return isLink ? (
          <Link
            key={index}
            href={linkAttrs.href}
            target={linkAttrs.target || "_blank"}
            rel={linkAttrs.rel || "noopener noreferrer"}
            style={textStyles}
          >
            {textElement}
          </Link>
        ) : (
          textElement
        );
      case "hardBreak":
        return <br />;
      case "horizontalRule":
        return <hr />;
      case "heading":
        const HeadingTag = `h${node.attrs.level}`;
        return (
          <div className="StoriesInfo_left_innercntr-full-width" key={index}>
            <div className="">
              <div className="rich-text_wrapper">
                <HeadingTag
                  style={{
                    ...(node.attrs?.textAlign && {
                      textAlign: node.attrs.textAlign,
                    }),
                    lineHeight: node.attrs.lineHeight,
                    margin: "25px 0px 10px 0px",
                    // fontSize:
                    //   node.attrs.level === 1
                    //     ? "42px!important"
                    //     : node.attrs.level === 2
                    //     ? "34px!important"
                    //     : "34px!important",
                  }}
                >
                  {renderContent(node.content)}
                </HeadingTag>
              </div>
            </div>
          </div>
        );
    }
  });
};

const renderContent = (content) => {
  if (!content) return null;

  return content.map((node, index) => {
    switch (node.type) {
      case "paragraph":
        return (
          <p
            className="Card_horoscope"
            style={{
              ...(node.attrs?.textAlign && {
                textAlign: node.attrs.textAlign,
              }),
            }}
            key={index}
          >
            {renderContent(node.content)}
            <br />
          </p>
        );

      case "text":
        let textStyles = {};
        let isLink = false;
        let linkAttrs = {};

        if (node.marks) {
          node.marks.forEach((mark) => {
            if (mark.type === "textStyle") {
              textStyles.color = mark?.attrs?.color;
            }
            if (mark.type === "bold") {
              textStyles.fontWeight = "bold";
            }
            if (mark.type === "italic") {
              textStyles.fontStyle = "italic";
            }
            if (mark.type === "link") {
              isLink = true;
              linkAttrs = mark.attrs;
            }
          });
        }
        const textElement = (
          <span key={index} style={textStyles}>
            {node.text}
          </span>
        );
        return isLink ? (
          <Link
            key={index}
            href={linkAttrs.href}
            target={linkAttrs.target || "_blank"}
            rel={linkAttrs.rel || "noopener noreferrer"}
            style={textStyles}
          >
            {textElement}
          </Link>
        ) : (
          textElement
        );
      case "hardBreak":
        return <br />;
      case "horizontalRule":
        return <hr />;
      case "astroBlock":
        return (
          <>
            <AstroWidget
              key={index}
              attrs={node.attrs}
              content={node.content}
            />
          </>
        );
      case "imageBlock":
        return (
          <div className="StoriesInfo_left_innercntr-full-width" key={index}>
            <div
              style={{
                display: "flex",
                justifyContent: node.attrs.align,
                width: "100%",
              }}
            >
              <div
                className="media-grid_wrapper"
                style={{ width: node.attrs.width }}
              >
                <div className="media-grid_wrapper_inner-full-width">
                  <Image
                    src={node.attrs.src}
                    alt={node.attrs.alt}
                    layout="responsive"
                    style={{ width: "100%", height: "100%" }}
                    width={700}
                    height={475}
                    objectFit="cover"
                  />
                  {(node.attrs.caption || node.attrs.courtesy) && (
                    <div className="Stories_caption_wrapper">
                      {hasHtmlTags(node.attrs.caption) ? (
                        <span
                          className="Stories_caption"
                          dangerouslySetInnerHTML={{
                            __html: node.attrs.caption,
                          }}
                        />
                      ) : (
                        <span className="Stories_caption">
                          {node.attrs.caption}
                        </span>
                      )}
                      {hasHtmlTags(node.attrs.courtesy) ? (
                        <span
                          className="Stories_courtesy"
                          dangerouslySetInnerHTML={{
                            __html: node.attrs.courtesy,
                          }}
                        />
                      ) : (
                        <span className="Stories_courtesy">
                          {node.attrs.courtesy}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      case "videoBlock":
        return (
          <div className="StoriesInfo_left_innercntr-full-width" key={index}>
            <div
              style={{
                display: "flex",
                justifyContent: node.attrs.align,
                width: "100%",
                margin: "20px 0",
              }}
            >
              <div
                className="media-grid_wrapper"
                style={{ width: node.attrs.width }}
              >
                <div className="media-grid_wrapper_inner-full-width">
                  <video
                    src={node.attrs.src}
                    alt={node.attrs.alt}
                    controls
                    muted
                    width={"100%"}
                    height={"100%"}
                    style={{ objectFit: "cover" }}
                  />
                  {(node.attrs.caption || node.attrs.courtesy) && (
                    <div className="Stories_caption_wrapper">
                      {hasHtmlTags(node.attrs.caption) ? (
                        <span
                          className="Stories_caption"
                          dangerouslySetInnerHTML={{
                            __html: node.attrs.caption,
                          }}
                        />
                      ) : (
                        <span className="Stories_caption">
                          {node.attrs.caption}
                        </span>
                      )}
                      {hasHtmlTags(node.attrs.courtesy) ? (
                        <span
                          className="Stories_courtesy"
                          dangerouslySetInnerHTML={{
                            __html: node.attrs.courtesy,
                          }}
                        />
                      ) : (
                        <span className="Stories_courtesy">
                          {node.attrs.courtesy}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      case "orderedList":
        return (
          <ol
            key={index}
            start={node?.attrs?.start || 1}
            className="Stories_list_style"
          >
            {node.content.map((item, idx) => (
              <li key={idx}>{renderListContent(item.content)}</li>
            ))}
          </ol>
        );
      case "bulletList":
        return (
          <ul
            key={index}
            start={node?.attrs?.start || 1}
            className="Stories_list_style"
          >
            {node.content.map((item, idx) => (
              <li key={idx}>{renderListContent(item.content)}</li>
            ))}
          </ul>
        );
      case "heading":
        const HeadingTag = `h${node.attrs.level}`;
        return (
          <div className="StoriesInfo_left_innercntr-full-width" key={index}>
            <div className="">
              <div className="rich-text_wrapper">
                <HeadingTag
                  style={{
                    ...(node.attrs?.textAlign && {
                      textAlign: node.attrs.textAlign,
                    }),
                    lineHeight: node.attrs.lineHeight,
                    margin: "10px 0px",
                    // fontSize:
                    //   node.attrs.level === 1
                    //     ? "42px!important"
                    //     : node.attrs.level === 2
                    //     ? "34px!important"
                    //     : "34px!important",
                  }}
                >
                  {renderContent(node.content)}
                </HeadingTag>
              </div>
            </div>
          </div>
        );
      case "embed":
        return (
          <>
            {getEmbedType(node.attrs.embed) === "twitter" ? (
              <div
                className="StoriesInfo_left_innercntr-full-width"
                key={index}
                style={{
                  width: node.attrs.width,
                  // paddingTop:"70%",
                  margin: "20px 0",
                }}
              >
                <div
                  className="flex-all-embed"
                  style={{ justifyContent: node.attrs.align }}
                >
                  <Tweet id={extractTwitterId(node.attrs.embed)} />
                </div>
              </div>
            ) : (
              <div
                className={`StoriesInfo_left_innercntr-full-width embed-${getEmbedType(
                  node.attrs.embed
                )}`}
                key={index}
                style={{
                  width: node.attrs.width,
                  // paddingTop:"70%",
                  margin: "20px 0",
                }}
                dangerouslySetInnerHTML={{ __html: node.attrs.embedFrame }}
              ></div>
            )}
            {(node.attrs.caption || node.attrs.courtesy) && (
              <div className="Stories_caption_wrapper">
                {hasHtmlTags(node.attrs.caption) ? (
                  <span
                    className="Stories_caption"
                    dangerouslySetInnerHTML={{
                      __html: node.attrs.caption,
                    }}
                  />
                ) : (
                  <span className="Stories_caption">{node.attrs.caption}</span>
                )}
                {hasHtmlTags(node.attrs.courtesy) ? (
                  <span
                    className="Stories_courtesy"
                    dangerouslySetInnerHTML={{
                      __html: node.attrs.courtesy,
                    }}
                  />
                ) : (
                  <span className="Stories_courtesy">
                    {node.attrs.courtesy}
                  </span>
                )}
              </div>
            )}
          </>
        );
      default:
        return null;
    }
  });
};

// TiptapRendered component
const AstroTiptapRenderer = ({ content }) => {
  return renderContent(content);
};

export default AstroTiptapRenderer;
