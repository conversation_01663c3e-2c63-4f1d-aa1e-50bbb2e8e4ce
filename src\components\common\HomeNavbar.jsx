import React, { useEffect, useState } from "react";
import <PERSON> from "next/link";
import Image from "next/image";
import gsap from "gsap";
import { useGSAP } from "@gsap/react";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import { BsPlusLg } from "react-icons/bs";
import { RxHamburgerMenu } from "react-icons/rx";
import MobileMenu from "./MobileMenu";
import SearchNavbar from "./SearchNavbar";
import { FiSearch } from "react-icons/fi";
import Button from "./Button";
import BackToTop from "@/components/common/BacktoTop";
gsap.registerPlugin(ScrollTrigger);
const HomeNavbar = ({ menu }) => {
  const [showMenu, setShowMenu] = useState(false);
  const [openSearch, setOpenSearch] = useState(false);

  const initializeAnimation = () => {
    if (window.innerWidth >= 1024) {
      const tl = gsap.timeline({
        scrollTrigger: {
          trigger: ".Navbar_wrapper",
          start: "top top",
          end: "bottom top",
          scrub: 1,
          markers: false,
        },
      });

      tl.to(
        ".Navbar_logo img",
        {
          height: "58px",
          duration: 1,
          ease: "linear",
        },
        "a"
      );
      tl.to(
        ".Navbar_logo h3",
        {
          duration: 1,
          ease: "linear",
          opacity: 0,
        },
        "a"
      );
      tl.to(
        ".Navbar_logo",
        {
          height: "4rem",
          duration: 1,
          ease: "linear",
        },
        "a"
      );

      const width = window.innerWidth;

      if (width >= 992 && width <= 1200) {
        tl.to(
          ".Navbar_wrapper",
          {
            gridTemplateColumns: "1fr 2fr",
            duration: 0.5,
          },
          "a"
        );
      }

      const navItems = document.querySelectorAll(".header_wrapper nav ul li");
      const totalItems = navItems.length;
      const translateValues = [
        "-90%",
        "-77%",
        "-58%",
        "-38%",
        "-17%",
        "-4.6667%",
      ];
      navItems.forEach((item, index) => {
        const topPercentage = (100 / (totalItems - 1)) * index;
        const zIndex = totalItems - index;
        item.style.top = `${topPercentage}%`;
        item.style.left = "0";
        item.style.zIndex = zIndex;
        const xValue = translateValues[index] || "0%";

        tl.to(
          item,
          {
            ease: "linear",
            duration: 0.5,
            top: "0%",
            x: xValue,
            delay: index * 0.05,
          },
          "a"
        );
      });

      tl.to(
        ".Navbar_wrapper",
        {
          height: "4rem",
          ease: "linear",
          duration: 1.1,
        },
        "a"
      );

      ScrollTrigger.refresh();
    }
  };

  useEffect(() => {
    // Initialize animation on mount
    initializeAnimation();

    // Add resize listener to update animation on window resize
    const handleResize = () => {
      // Clear previous ScrollTrigger
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
      initializeAnimation();
    };

    window.addEventListener("resize", handleResize);

    // Clean up on unmount
    return () => {
      window.removeEventListener("resize", handleResize);
      ScrollTrigger.getAll().forEach((trigger) => trigger.kill());
    };
  }, []);

  useGSAP(() => {
    var pluscross = document.querySelector(".Plus_btn");
    var pluscrossSvg = document.querySelector(".Plus_btn svg");
    var navLinks = document.querySelectorAll(".header_wrapper nav ul li a");

    var tl = gsap.timeline();
    tl.to(
      ".DropdownMenu-wrapper",
      {
        top: 0,
        duration: 0.5,
      },
      "a"
    );
    tl.to(
      pluscrossSvg,
      {
        rotate: "135deg",
        duration: 0.5,
        scale: 2,
        // ease: "linear",
      },
      "a"
    );
    tl.pause();
    let flag = 0;

    pluscross.addEventListener("click", (e) => {
      e.preventDefault();
      // Check the flag to determine whether to play or reverse the animation
      if (flag === 0) {
        tl.play(); // Play the timeline forward
        flag = 1; // Set flag to 1 (indicating the animation is played forward)
        document.body.style.overflow = "hidden"; // Stop scrolling
        document.documentElement.style.overflow = "hidden";
        navLinks.forEach((el) => {
          el.style.opacity = 0;
          el.style.pointerEvents = "none";
        });
        document.querySelector(".shopImgnav").style.opacity = "1";
        document.querySelector(".shopImgnav").style.pointerEvents = "auto";
      } else {
        tl.reverse(); // Reverse the animation
        flag = 0; // Set flag to 0 (indicating the animation is reversed)
        document.body.style.overflow = "auto"; // Enable scrolling again
        document.documentElement.style.overflow = "";
        navLinks.forEach((el) => {
          el.style.opacity = 1;
          el.style.pointerEvents = "auto";
          document.querySelector(".shopImgnav").style.opacity = "0";
          document.querySelector(".shopImgnav").style.pointerEvents = "none";
        });
      }
    });
  });
  const toggleMenu = () => {
    setShowMenu((prevState) => !prevState);
  };
  // Close the mobile menu
  const closeMenu = () => {
    setShowMenu(false);
  };

  return (
    <>
      <div className="DropdownMenu-wrapper">
        <div className="DropdownMenu-cntr">
          <div className="container">
            <div className="DropdownMenu-cntr-content">
              <div className="dropdown-wrapper container">
                <div className="dropdown-top-links dropdown-nav-1">
                  {menu.map((item, index) => {
                    return (
                      <div
                        className="single-dropdown-col"
                        key={`menu-dropdown-items-${index}`}
                      >
                        <p>
                          <Link href={item.link}>{item.name}</Link>
                        </p>
                        {item.submenus.length > 0 && (
                          <div className="dropdown-links">
                            {item.submenus.map((submenu, subIndex) => {
                              return (
                                <div key={`submenu-dropdown-items-${subIndex}`}>
                                  <Link
                                    className="animated-link-underline"
                                    href={submenu.link}
                                  >
                                    {submenu.name}
                                  </Link>
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    );
                  })}
                  {/* <div className="single-dropdown-col">
                    <p>
                      <Link
                        href="https://shop.yudirect.biz/ManifestLife/Subscribe.php"
                        target="blank"
                      >
                        Subscribe
                      </Link>
                    </p>
                  </div> */}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div className="header_wrapper">
        <div className="container">
          <header className="Navbar_wrapper">
            <Link href="/" className="Navbar_logo">
              <h3>[Curate Beautiful. Create Happy] </h3>
              <img src="../../../logo svg.svg" alt="" />
            </Link>
            <div className="header_right_part">
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <p className="subscribe_text">
                  <Link
                    href="https://shop.yudirect.biz/ManifestLife/Subscribe.php"
                    target="blank"
                  >
                    Subscribe
                  </Link>
                </p>
                <BsPlusLg className="nav_hamburger" onClick={toggleMenu} />
              </div>
              <div className="shopImgnav">
                <div className="testimonial_product_navImg">
                  <div className="tp_wrapper_navImg">
                    <div className="tp_media_navImg">
                      <div className="tp_image_navImg">
                        <Image
                          id="block-testimonial_XHMbWy-34619739308283"
                          className=""
                          sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px"
                          loading="lazy"
                          fill
                          objectFit="contain"
                          alt="Khushi Kapoor on Manifest Cover"
                          src="/Assets/Manifest-Artboard-2.jpg"
                        />
                      </div>
                      <div className="tp_button tp_button_Imgnav">
                        <Button
                          href={
                            "https://shop.yudirect.biz/ManifestLife/Subscribe.php"
                          }
                          target={"_blank"}
                        >
                          Subscribe
                        </Button>
                        {/* <Link herf=""></Link> */}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <nav>
                <ul>
                  {menu.map((item, index) => {
                    return (
                      <li key={`nav-menu-${index}`}>
                        <Link href={item.link}>{item.name}</Link>
                      </li>
                    );
                  })}

                  <li>
                    <div className="Plus_btn">
                      <BsPlusLg />
                    </div>
                  </li>
                </ul>
              </nav>
            </div>
          </header>
          <header className="Navbar_wrapperMbl">
            <div className="Navbar_logo2">
              <Link href="/">
                <h3>[Curate Beautiful. Create Happy] </h3>
                <img src="../../../logo svg.svg" alt="" />
              </Link>
            </div>
            <div className="header_right_part2">
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <p className="subscribe_text">
                  <Link
                    href="https://shop.yudirect.biz/ManifestLife/Subscribe.php"
                    target="blank"
                  >
                    Subscribe
                  </Link>
                </p>
                <BsPlusLg className="nav_hamburger2" onClick={toggleMenu} />
              </div>
            </div>
          </header>
        </div>
      </div>
      <MobileMenu
        menu={menu}
        closeMenu={closeMenu}
        showMenu={showMenu}
        setShowMenu={setShowMenu}
      />
      <SearchNavbar openSearch={openSearch} setOpenSearch={setOpenSearch} />
      {!showMenu && (
        <div
          className="card-actions_actions_button searchbtn"
          onClick={() => {
            setOpenSearch(!openSearch);
          }}
        >
          <FiSearch />
        </div>
      )}
      {!showMenu && <BackToTop />}
    </>
  );
};

export default HomeNavbar;
