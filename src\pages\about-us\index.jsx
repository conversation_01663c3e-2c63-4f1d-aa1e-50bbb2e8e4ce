import React, { useEffect, useRef } from "react";
import { useGSAP } from "@gsap/react";
import gsap from "gsap";
import SplitText from "gsap/dist/SplitText";
import SeoHeader from "@/components/seo/SeoHeader";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import TransparentSection from "@/components/common/TransparentSection";
gsap.registerPlugin(SplitText);

const chiefTeamData = [
  {
    id: 1,
    title: "<PERSON><PERSON> Jain",
    designation: "Chairperson",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "<PERSON><PERSON> Jain",
  },
  {
    id: 2,
    title: "<PERSON>",
    designation: "Chief Operating Officer",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "<PERSON>",
  },
];

const editorialTeamData = [
  {
    id: 0,
    title: "<PERSON><PERSON>",
    designation: "Digital Editor",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "<PERSON>va Mubarak",
  },
  {
    id: 1,
    title: "Shambhavi Dutta",
    designation: "Features Editor",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Shambhavi Dutta",
  },
  {
    id: 2,
    title: "Palak Valecha",
    designation: "Senior Stylist And Fashion Writer",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Palak Valecha",
  },
  {
    id: 3,
    title: "Tanya Verma",
    designation: "Social Media Head",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Tanya Verma",
  },
  {
    id: 4,
    title: "Vishvi Gupta",
    designation: "Social Media Manager",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Vishvi Gupta",
  },
  {
    id: 5,
    title: "Harshita Singh",
    designation: "Digital Writer",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Harshita Singh",
  },
  {
    id: 6,
    title: "Neha Karra",
    designation: "Jr Digital Writer",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Neha Karra",
  },
];

const artWebDesignTeamData = [
  {
    id: 1,
    title: "Jit Ray",
    designation: "Creative Director",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Jit Ray",
  },
  {
    id: 2,
    title: "Amit Malik",
    designation: "Art Director",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Amit Malik",
  },
  {
    id: 3,
    title: "Azad Mohan",
    designation: "Art Director",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Azad Mohan",
  },
  {
    id: 4,
    title: "Kishore Rawat",
    designation: "Designer",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Kishore Rawat",
  },
  {
    id: 5,
    title: "Sunil Malik",
    designation: "Art Director",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Sunil Malik",
  },
  {
    id: 6,
    title: "Avaneesh Kumar",
    designation: "SEO Manager",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Avaneesh Kumar",
  },
  {
    id: 7,
    title: "Rohit Tiwari",
    designation: "Web Design & Development",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Rohit Tiwari",
  },
  {
    id: 8,
    title: "Vikas Gupta",
    designation: "Web Design & Development",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Vikas Gupta",
  },
];

const businessTeamData = [
  {
    id: 1,
    title: "Raas Taneja",
    designation: "Chief Finance Officer",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Raas Taneja",
  },
  {
    id: 2,
    title: "Jabir Merchant",
    designation: "Chief Revenue Officer",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Jabir Merchant",
  },
  {
    id: 3,
    title: "Anupam Sehgal",
    designation: "Chief Marketing Officer",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Anupam Sehgal",
  },
  {
    id: 4,
    title: "Ruchira Kanwal",
    designation: "Business Head",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Ruchira Kanwal",
  },
  {
    id: 5,
    title: "Raghav Borgohain",
    designation: "Regional Manager (North)",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Raghav Borgohain",
  },
  {
    id: 6,
    title: "Shubhani Gupta",
    designation: "Chief Manager (North)",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Shubhani Gupta",
  },
  {
    id: 7,
    title: "Dimple Sutaria",
    designation: "Regional Manager (West)",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Dimple Sutaria",
  },
  {
    id: 8,
    title: "Harleen Kaur",
    designation: "Manager (South)",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Harleen Kaur",
  },
  {
    id: 9,
    title: "Mukesh Koli",
    designation: "Audience Development Director",
    image: "https://hips.hearstapps.com/hmg-prod/images/1-1667246994.jpg",
    altName: "Mukesh Koli",
  },
];

const AboutUs = ({ meta }) => {
  const teamCardsRef = useRef(null); // Reference to the Teamcard container
  //   const useRef = () => {};
  useGSAP(() => {
    var split = new SplitText(".cover2_text", {
      type: "lines",
      linesClass: "line",
    });
    var lines = document.querySelectorAll(".line");
    lines.forEach(function (line) {
      line.innerHTML = "<span>" + line.innerHTML + "</span>";
    });
    // Create a timeline for each card
    const tl = gsap.timeline({ paused: true });

    gsap.set(".cover2_text .line span", { skewY: 20, y: 100 });

    document.querySelectorAll(".Teamcard_cntr").forEach((el) => {
      const innerText = el.querySelectorAll(".cover2_text .line span");

      el.addEventListener("mouseenter", () => {
        gsap.to(innerText, 0.8, {
          y: 0,
          ease: "power4.out",
          delay: 0.1,
          skewY: 0,
          stagger: { amount: 0.3 },
        });
      });

      el.addEventListener("mouseleave", () => {
        gsap.to(innerText, 0.8, {
          y: 100,
          ease: "power4.out",
          delay: 0.1,
          skewY: 20,
          stagger: { amount: 0.3 },
        });
      });
    });
  });

  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div className="AboutandTeamCntr">
        <div className="container">
          <div className="AboutUscntr">
            <div className="pageInner">
              <article
                id="post-592"
                className="post-592 page type-page status-publish hentry entry"
              >
                <header className="entry-header alignwide">
                  <h1 className="entry-title">About Us</h1>{" "}
                </header>
                {/* .entry-header */}
                <div className="entry-content">
                  <p>
                    We, at&nbsp;<em>Manifest,</em>&nbsp;offer a sophisticated
                    and elegant take on luxury, couture and weddings. We are
                    rearing to address the crème-de-la crème, well-heeled, Gen-Z
                    and millennial connoisseurs. This is your go-to destination
                    for comprehensive and intelligent features with a unique
                    viewpoint that can help you build an informed perspective.
                    Expect hot takes on seasonal high points in couture, beauty,
                    pop culture, jewellery, watches, travel and all things
                    wedding.&nbsp;
                  </p>
                  <p>
                    We are published by RPSG Media which launched in 2023.
                    Through our various multimedia platforms, we intend to make
                    significant inroads into the fastest-growing luxury
                    lifestyle market in the world. With a varied print portfolio
                    and a fun editorial team leading the digital division, RPSG
                    Media under the RP-Sanjiv Goenka Group is here to create
                    culture-defining storytelling that&nbsp;influences audiences
                    across the digital landscape in South Asia.
                  </p>
                </div>
                {/* .entry-content */}
              </article>
              {/* #post-592 */}
            </div>
          </div>
          <div className="TeamCntr">
            <div className="TeamCntr_inner">
              <div className="TeamCntr_left">
                <div className="TeamCntr_left_inner">
                  <h3>The team</h3>
                  <p>
                    Our team at Manifest is a dynamic mix of writers, editors,
                    and creatives dedicated to insightful storytelling and sharp
                    cultural analysis. With a passion for authenticity and
                    innovation, we craft compelling narratives that inform,
                    inspire, and leave a lasting impact.
                  </p>
                </div>
              </div>
              <div className="TeamCntr_right">
                {chiefTeamData && chiefTeamData.length > 0 && (
                  <div className="TeamCntr_right_wrapper">
                    <div className="TeamCntr_right_inner">
                      {chiefTeamData.map((item, index) => {
                        return (
                          <>
                            <div
                              key={`chief-team-${index}`}
                              className="Teamcard_cntr"
                            >
                              {/* <div className="cover2">
                                <Image src={el.TeamImage} fill sizes="" alt="" />
                              </div>
                              <div className="cover2_text">{el.desc}</div> */}
                              <div className="Teamcard_text_chief">
                                <h5>{item.title}</h5>
                                <h6>{item.designation}</h6>
                              </div>
                            </div>
                          </>
                        );
                      })}
                    </div>
                  </div>
                )}
                {editorialTeamData && editorialTeamData.length > 0 && (
                  <div className="TeamCntr_right_wrapper">
                    <h3>Editorial Team</h3>
                    <div className="TeamCntr_right_inner">
                      {editorialTeamData.map((item, index) => {
                        return (
                          <>
                            <div
                              key={`editorial-team-${index}`}
                              className="Teamcard_cntr"
                            >
                              {/* <div className="cover2">
                                <Image src={el.TeamImage} fill sizes="" alt="" />
                              </div>
                              <div className="cover2_text">{el.desc}</div> */}
                              <div className="Teamcard_text">
                                <h5>{item.title}</h5>
                                <h6>{item.designation}</h6>
                              </div>
                            </div>
                          </>
                        );
                      })}
                    </div>
                  </div>
                )}
                {artWebDesignTeamData && artWebDesignTeamData.length > 0 && (
                  <div className="TeamCntr_right_wrapper">
                    <h3>Art & Web Design</h3>
                    <div className="TeamCntr_right_inner">
                      {artWebDesignTeamData.map((item, index) => {
                        return (
                          <>
                            <div
                              key={`art-and-web-design-team-${index}`}
                              className="Teamcard_cntr"
                            >
                              {/* <div className="cover2">
                                <Image src={el.TeamImage} fill sizes="" alt="" />
                              </div>
                              <div className="cover2_text">{el.desc}</div> */}
                              <div className="Teamcard_text">
                                <h5>{item.title}</h5>
                                <h6>{item.designation}</h6>
                              </div>
                            </div>
                          </>
                        );
                      })}
                    </div>
                  </div>
                )}
                {businessTeamData && businessTeamData.length > 0 && (
                  <div className="TeamCntr_right_wrapper">
                    <h3>Business Team</h3>
                    <div className="TeamCntr_right_inner">
                      {businessTeamData.map((item, index) => {
                        return (
                          <>
                            <div
                              key={`business-team-${index}`}
                              className="Teamcard_cntr"
                            >
                              {/* <div className="cover2">
                                <Image src={el.TeamImage} fill sizes="" alt="" />
                              </div>
                              <div className="cover2_text">{el.desc}</div> */}
                              <div className="Teamcard_text">
                                <h5>{item.title}</h5>
                                <h6>{item.designation}</h6>
                              </div>
                            </div>
                          </>
                        );
                      })}
                    </div>
                  </div>
                )}
                {/* <div className="Teamcard_cntr">
                    <div className="cover2">
                      <Image
                        src="https://www.deep.co.uk/wp-content/uploads/2024/04/Deep_Design_Agency_Team_Charlie-459x500.jpg"
                        fill
                      />
                    </div>
                    <div className="Teamcard_text">
                      <h5>GRANT BOWDEN</h5>
                      <h6>Creative Director / Co-owner</h6>
                    </div>
                  </div> */}
              </div>
            </div>
          </div>
        </div>
      </div>
      <TransparentSection />
    </>
  );
};

export default AboutUs;

export async function getStaticProps() {
  const meta = {
    title: "About Wedding Forward Magazine - Manifest India",
    description:
      "Manifest Magazine is your ultimate source for real and celebrity wedding stories, fashion, beauty, and wedding inspiration. Stay updated with expert advice, trends, and creative stories from around the world.",
    keywords: [],
    author: "Manifest India",
    robots: "index,follow",
  };
  return { props: { meta: meta } };
}
