import React, { useEffect } from "react";
import { useRouter } from "next/router";
import { usePathname } from "next/navigation";
import gsap from "gsap";
import SplitText from "gsap/dist/SplitText";
import Link from "next/link";

gsap.registerPlugin(SplitText);
const Hero = ({ title, submenus }) => {
  const router = useRouter();
  const pathname = usePathname();

  // useEffect(() => {
  //   const titleheadingAnim = document.querySelector(".h1-header h1");

  //   const animation = gsap.to(titleheadingAnim, {
  //     duration: 2,
  //     transform: "translateY(0)",
  //     ease: "power4",
  //     // stagger: 0.08, // Uncomment if stagger is needed
  //   });

  //   // Listen for route changes (optional, to ensure effects on each route change)
  //   const handleRouteChange = () => {
  //     animation.restart(); // Restart the animation on route change
  //   };

  //   router.events.on("routeChangeStart", handleRouteChange);

  //   return () => {
  //     router.events.off("routeChangeStart", handleRouteChange); // Cleanup listener
  //     animation.kill();
  //   };
  // }, [router.events]); // This will trigger the effect on route changes

  return (
    <>
      <section
        id="category-hero-section"
        className="bg-background text-foreground color-changer"
      >
        <div className="Title-header-wrapper">
          {submenus && submenus.length > 0 && (
            <>
              <div className="category-sub-cat">
                <ul>
                  {submenus?.map((submenu, index) => {
                    const isActive =
                      submenu?.name === "All"
                        ? pathname === submenu?.slug
                        : pathname.startsWith(submenu?.slug ?? "");
                    const submenuSlug = submenu?.slug ?? "javascript:void(0)";
                    const submenuName = submenu?.name ?? "";
                    return (
                      <li key={`submenus-item-${index}`}>
                        <Link
                          className={isActive ? "active" : ""}
                          href={submenuSlug}
                        >
                          {submenuName}
                        </Link>
                      </li>
                    );
                  })}
                </ul>
              </div>
            </>
          )}
          <div className="h1-header">
            <h1>{title ?? ""}</h1>
          </div>
        </div>
      </section>
    </>
  );
};

export default Hero;
