import React from "react";
import Card from "@/components/cards/Card";
import Button from "@/components/common/Button";

const GridSection = ({ id, title, data, hasMore, handleShowMore }) => {
  return (
    <>
      {data && data.length > 0 ? (
        <>
          <div className="container">
            <div
              className={`bg-background text-foreground color-changer ${id}`}
            >
              <div className="GridCardContainer" style={{ overflow: "hidden" }}>
                {data.map((item, index) => (
                  <Card
                    key={`${id}-item-${index}`}
                    title={item?.title ?? ""}
                    image={
                      item?.croppedImg
                        ? item?.croppedImg
                        : item?.image
                        ? item?.image
                        : ""
                    }
                    altName={item?.altName ?? ""}
                    category={item?.category ?? ""}
                    slug={item?.slug ?? ""}
                    timestamp={item?.timestamp ?? ""}
                    author={item?.author ?? []}
                    contributor={item?.contributor ?? []}
                  />
                ))}
              </div>
              {hasMore && (
                <>
                  <div className="flex-all">
                    <Button onClick={handleShowMore}>SEE MORE</Button>
                  </div>
                </>
              )}
            </div>
          </div>
        </>
      ) : (
        <>No data found</>
      )}
    </>
  );
};

export default GridSection;
