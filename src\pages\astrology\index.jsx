import React, { useState } from "react";
import { usePathname } from "next/navigation";
import SeoHeader from "@/components/seo/SeoHeader";
import Hero from "@/components/category/Hero";
import DailyHoroscopeSection from "@/components/astrology/DailyHoroscopeSection";
import GridSection from "@/components/category/GridSection";
import Button from "@/components/common/Button";
import TransparentSection from "@/components/common/TransparentSection";
import { dailyHoroscopeData } from "@/helper/Data";
import { getCategory } from "@/pages/api/CategoryApi";
import { getFlaggedPage } from "@/pages/api/HomeApi";
import BreadcrumbSchema from "@/components/seo/BreadcrumbSchema";
import { Ads } from "@/components/ads/Ads";

const Astrology = ({ meta, title, initialData, initialCount }) => {
  const pathname = usePathname();
  const router = pathname.split("/");
  const LIMIT = 12;
  const [data, setData] = useState(initialData);
  const [totalCount, setTotalCount] = useState(initialCount);
  const [hasMore, setHasMore] = useState(initialData.length < initialCount);
  const [loading, setLoading] = useState(false);
  const [offset, setOffset] = useState(LIMIT);

  const handleLoadMore = async () => {
    if (!hasMore || loading) return;

    setLoading(true);
    const payload = {
      slug: `/astrology/in-the-stars`,
      limit: LIMIT,
      offset: offset,
    };

    try {
      const response = await getCategory(payload);
      const newData = response?.data?.data || [];
      const newTotalCount = response?.data?.count || 0;
      setData((prevData) => [...prevData, ...newData]);
      setTotalCount(newTotalCount);
      setOffset((prevOffset) => prevOffset + LIMIT);
      setHasMore(data.length + newData.length < newTotalCount);
    } catch (error) {
      console.error("Error loading more data:", error);
    } finally {
      setLoading(false);
    }
  };
  return (
    <>
      <SeoHeader meta={meta} />
      <BreadcrumbSchema />
      <div className="Astrology_wrapper">
        <div className="container">
          <Hero title={title ?? "Astrology"} />
          <div>
            <Ads
              id="div-gpt-ad-astrology-top"
              adUnits={[
                {
                  adUnit: "/23290324739/Manifest-Desktop-TOP-970",
                  sizes: [[970, 90]],
                  sizeMapping: [
                    {
                      viewport: [0, 0],
                      sizes: [[970, 90]],
                    },
                  ],
                  minWidth: 1024,
                  maxWidth: Infinity,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Top-320",
                  sizes: [
                    [320, 50],
                    [320, 100],
                  ],
                  sizeMapping: [
                    {
                      viewport: [1023, 0],
                      sizes: [
                        [320, 50],
                        [320, 100],
                      ],
                    },
                  ],
                  minWidth: 768,
                  maxWidth: 1023,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Top-320",
                  sizes: [
                    [320, 50],
                    [320, 100],
                  ],
                  sizeMapping: [
                    {
                      viewport: [767, 0],
                      sizes: [
                        [320, 50],
                        [320, 100],
                      ],
                    },
                  ],
                  minWidth: 0,
                  maxWidth: 767,
                },
              ]}
              targeting={{
                section: [router?.[1] || null],
                "sub-section": [null],
              }}
            />
          </div>
          <DailyHoroscopeSection data={dailyHoroscopeData} />
          <section className="InStarGrid_container">
            <div>
              <Ads
                id="div-gpt-ad-astrology-middle"
                adUnits={[
                  {
                    adUnit: "/23290324739/Manifest-Desktop-TOP-970",
                    sizes: [[970, 250]],
                    sizeMapping: [
                      {
                        viewport: [0, 0],
                        sizes: [[970, 250]],
                      },
                    ],
                    minWidth: 1024,
                    maxWidth: Infinity,
                  },
                  {
                    adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                    sizeMapping: [
                      {
                        viewport: [1023, 0],
                        sizes: [
                          [300, 250],
                          [300, 100],
                        ],
                      },
                    ],
                    minWidth: 768,
                    maxWidth: 1023,
                  },
                  {
                    adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                    sizeMapping: [
                      {
                        viewport: [767, 0],
                        sizes: [
                          [300, 250],
                          [300, 100],
                        ],
                      },
                    ],
                    minWidth: 0,
                    maxWidth: 767,
                  },
                ]}
                targeting={{
                  section: [router?.[1]],
                  "sub-section": [null],
                }}
              />
            </div>
            <GridSection paddingbtm={"paddingbtm-10"} data={data} />
            <Ads
              id="div-gpt-ad-astrology-bottom"
              adUnits={[
                {
                  adUnit: "/23290324739/Manifest-Desktop-Bottom-300",
                  sizes: [[300, 250]],
                  sizeMapping: [
                    {
                      viewport: [0, 0],
                      sizes: [[300, 250]],
                    },
                  ],
                  minWidth: 1024,
                  maxWidth: Infinity,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                  sizes: [
                    [300, 100],
                    [300, 250],
                  ],
                  sizeMapping: [
                    {
                      viewport: [1023, 0],
                      sizes: [
                        [300, 100],
                        [300, 250],
                      ],
                    },
                  ],
                  minWidth: 768,
                  maxWidth: 1023,
                },
                {
                  adUnit: "/23290324739/Manifest-Mobile-Bottom-300",
                  sizes: [
                    [300, 100],
                    [300, 250],
                  ],
                  sizeMapping: [
                    {
                      viewport: [767, 0],
                      sizes: [
                        [300, 100],
                        [300, 250],
                      ],
                    },
                  ],
                  minWidth: 0,
                  maxWidth: 767,
                },
              ]}
              targeting={{
                section: [router?.[1] || null],
                "sub-section": [null],
              }}
            />
          </section>

          {hasMore && (
            <div className="flex-all">
              <Button onClick={handleLoadMore} disabled={loading}>
                {loading ? "Loading..." : "SEE MORE"}
              </Button>
            </div>
          )}
        </div>
      </div>
      <TransparentSection />
    </>
  );
};

export default Astrology;

export async function getServerSideProps(context) {
  try {
    const LIMIT = 12;
    const slug = `/astrology/in-the-stars`;
    const payload = {
      slug,
      limit: LIMIT,
      offset: 0,
    };
    const [response, categoryRes] = await Promise.all([
      getFlaggedPage("/astrology"),
      getCategory(payload),
    ]);
    if (response.status === "success") {
      const { headers, meta } = response.data;
      return {
        props: {
          title: headers?.title ?? "Astrology",
          initialData:
            categoryRes?.data && categoryRes?.data?.data?.length > 0
              ? categoryRes?.data?.data
              : [],
          initialCount: categoryRes?.data?.count || 0,
          meta: meta ?? {},
        },
      };
    }
  } catch (error) {
    console.error("Error fetching data:", error.message);
    return {
      props: {
        data: {},
        meta: {
          title:
            "Manifest India - Wedding News, Celebrity Weddings, Tips &amp; Trends",
          description:
            "Manifest India is your source for wedding news, celebrity wedding updates, fashion trends, wedding skincare, rituals and more.",
          keywords: [],
          author: "RPSG Media",
          robots: "index,follow",
        },
      },
    };
  }
}
