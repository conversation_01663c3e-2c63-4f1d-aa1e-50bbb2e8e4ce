.Astrology_wrapper {
  position: relative;
  z-index: 9;
  background-color: #faf7f3;
  padding: 0 0 5rem 0;
  border-bottom: 1px solid #000;
}
.CategoryGrid_container {
  grid-column: 1 / span 12;
  margin: 5rem auto 0;
  position: relative;
  background-color: #faf7f3;
  /* padding: 0 1.25rem; */
}
.InStarGrid_container {
  grid-column: 1 / span 12;
  margin: 3rem auto 0;
  position: relative;
  background-color: #faf7f3;
  /* padding: 0 1.25rem; */
}
.Heading_container {
  padding: 1.5rem 0;
  grid-column: 1 / span 12;
  text-align: center;
}
.Heading_subtitle {
  display: block;
  color: gray;
  font-weight: 700;
  font-size: 0.694rem;
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: 0.0375rem;
  font-family: var(--primary-font-Neue);
}
.Heading_title {
  margin: 0.25rem 0 0;
  font-family: var(--primary-font-scotch-display-compress);
  font-weight: 700;
  font-size: 2.074rem;
  line-height: 1;
}
/* .Heading_container:after {
  content: "";
  display: block;
  position: relative;
  width: 4rem;
  height: 0.125rem;
  margin: 1.25rem auto 0;
  background-color: black;
} */
.CategoryGrid_list {
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: repeat(12, [col-start] 1fr);
  gap: 0.375rem;
  list-style: none;
  margin: 0 auto;
  padding: 0;
  /* max-width: 67.25rem; */
}
.CategoryGrid_item {
  /* opacity: 0; */
  grid-column: span 6;
  background-color: #efe8db;
  overflow: hidden;
  transition: all 0.1s;
}
/* .CategoryGrid_item:hover {
  transform: perspective(900px) translateY(-5%) rotateX(25deg) translateZ(0);
  box-shadow: 2px 35px 32px -8px rgba(0, 0, 0, 0.75);
  -webkit-box-shadow: 2px 35px 32px -8px rgba(0, 0, 0, 0.75);
  -moz-box-shadow: 2px 35px 32px -8px rgba(0, 0, 0, 0.75);
} */
.CategoryGrid_link {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  height: 100%;
  padding: 1rem 0;
  /* background-image: url(https://v.ftcdn.net/02/59/04/81/700_F_259048183_nYNdgZTLBShglFxky5K4X3yHJCvNB3Ao_ST.mp4); */
  font-family: var(--primary-font-scotch-display);
}
.CategoryGrid_hero {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 66.66667%;
  height: auto;
  aspect-ratio: 1 / 1;
  margin: 0 0 0.5rem;
  overflow: hidden;
}
.CategoryGrid_image {
  position: relative;
  width: calc(100% - 0.5rem);
  height: auto;
  aspect-ratio: 1 / 1;
  border-radius: 50%;
}
.CategoryGrid_text {
  text-align: center;
  color: #000;
}
/* horoscopes details page */
.Article_container {
  display: grid;
  /* margin: 0 auto; */
  padding: 0;
  background-color: #faf7f3;
  position: relative;
  /* margin: 2rem 0; */
}
.HoroscopeHeader_container {
  grid-column: 1 / span 12;
  /* margin: 2rem 0 0; */
}
.Grid_container {
  display: grid;
  grid-template-rows: 1fr;
  grid-template-columns: repeat(12, [col-start] 1fr);
  -moz-column-gap: 0.5rem;
  column-gap: 0.5rem;
  /* margin: 0 auto; */
  /* max-width: 67.25rem; */
  max-width: 100rem;
}
.HoroscopeHeader_text {
  grid-column: 1 / span 12;
  margin-bottom: 1.5rem;
}
.HoroscopeHeader_list {
  display: flex;
  align-items: center;
  margin: 0 0 0.375rem;
  padding: 0;
  list-style: none;
}
.HoroscopeHeader_item {
  font-family: var(--primary-font-Neue);
  font-weight: 700;
  font-size: 0.694rem;
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: 0.0375rem;
  color: gray;
}
.HoroscopeHeader_link {
  display: inline-block;
  line-height: 1;
}
.HoroscopeHeader_title {
  margin: 0;
  font-size: 2.074rem;
  font-family: var(--primary-font-scotch-display-compress);
  line-height: 1;
}
.Card_container {
  grid-column: 1 / span 12;
  background-color: #efe8db;
  min-height: auto;
}
.Card_text {
  padding: 1.5rem 1rem 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.Card_heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
}
.Card_title {
  grid-area: title;
  margin: 0;
  font-size: 0.833rem;
  line-height: 1;
  text-transform: uppercase;
  font-family: var(--primary-font-Neue);
}
.Card_controls {
  grid-area: controls;
  display: flex;
  align-items: flex-start;
  /* gap: 0.5rem; */
  flex-direction: column;
}
.Card_button {
  color: #6b6b68;
  height: 1.5rem;
  margin: 0;
  padding: 0.0625rem 0.5rem 0;
  border: 1px solid #999998 !important;
  border-radius: 0.75rem;
  background-color: transparent;
  font-size: 0.694rem;
  font-weight: 700;
  font-family: var(--primary-font-Neue);
  line-height: 1;
  text-transform: uppercase;
  cursor: pointer;
  border: none;
}
/* .calendar-picker {
  font-size: 12px;
  padding: 1px;
  border-radius: 5px;
  border: 1px solid #dcdcd7;
  background-color: transparent;
  cursor: pointer;
  font-family: var(--primary-font-Neue);
} */
.Card_avatar {
  grid-area: avatar;
  width: 100%;
  height: auto;
  margin-left: auto;
  border-radius: 5px;
  overflow: hidden;
  margin-bottom: 20px;
}
.Card_avatar .Card_image {
  width: 100%;
  height: 100%;
}
.Card_image {
  width: auto;
  height: 100%;
}
.Card_content {
  position: relative;
  margin-top: 1rem;
  height: auto;
  display: flex;
  align-items: end;
  font-family: var(--primary-font-Neue);
}
.Card_content .calendar {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}
.Card_horoscope {
  margin-bottom: 20px;
  font-size: 1rem;
  line-height: 1.3;
  font-family: var(--primary-font-Neue);
}
.Card_horoscope:last-child {
  margin-bottom: 0px;
}
.Card_link {
  display: block;
  margin-top: 1rem;
  color: gray;
  font-size: 1rem;
  font-weight: 700;
  font-family: var(--primary-font-Neue);
}
.Card_hero {
  display: none;
  /* background-color: #767522; */
  text-align: end;
}
.Card_image {
  width: auto;
  height: 100%;
}
.List_container_Icons {
  position: relative;
  grid-column: 1 / span 12;
  margin: 2rem 0;
}
.List_list {
  position: relative;
  /* display: grid; */
  /* grid-template-columns: repeat(12, 1fr); */
  display: flex;
  row-gap: 1.5rem;
  -moz-column-gap: 0.5rem;
  column-gap: 1.5rem;
  list-style: none;
  margin: 0;
  padding: 0 0 4rem;
  max-height: 8rem;
  overflow-x: auto;
}
/* .List_list[data-open="true"] {
  max-height: 9999px;
} */
.List_item {
  grid-column: span 3;
  text-align: center;
  font-family: var(--primary-font-Neue);
}
.List_link {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #767522;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1;
}
.List_hero {
  width: 5rem;
  height: 5rem;
  border-radius: 100rem;
  margin-bottom: 0.5rem;
  padding: 0.5rem;
  background-color: #f3f3ed;
  transition: all 0.15s ease-out;
}
.List_hero:hover {
  background-color: #3d3d3b;
}
.List_link[data-active="true"] .List_hero {
  background-color: #3d3d3b;
}
.List_image {
  display: block;
  width: 100%;
  height: auto;
  padding: 0.5rem;
}
.List_button {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.25rem;
  bottom: 1rem;
  left: 0;
  right: 0;
  width: 7rem;
  height: 1.5rem;
  margin: 0 auto;
  padding: 0.0625rem 0.5rem 0;
  color: #6b6b68;
  background-color: #fdfdfb;
  border: 1px solid #dcdcd7;
  border-radius: 0.75rem;
  font-size: 0.694rem;
  font-weight: 700;
  font-family: var(--primary-font-Neue);
  line-height: 1;
  text-transform: uppercase;
  cursor: pointer;
}
.Content_container {
  grid-column: 1 / span 12;
}
.Content_body {
  position: relative;
  margin-top: -0.5rem;
}
/* .Content_body:before {
  content: "";
  display: block;
  width: 100%;
  height: 1px;
  margin-top: 0.5rem;
  margin-bottom: 1.25rem;
  background-color: #dcdcd7;
  text-align: center;
} */
/* .Content_body:after {
  content: "✦✦✦";
  position: absolute;
  display: inline-block;
  color: #767522;
  background-color: #fdfdfb;
  top: -0.375rem;
  left: 0;
  right: 0;
  width: 3rem;
  margin: 0 auto;
  font-size: 0.833rem;
  text-align: center;
} */
.Content_text {
  color: #6b6b68;
  font-size: 0.833rem !important;
  line-height: 1.25;
}
.Content_container p {
  margin-bottom: 0;
  font-size: 1rem;
  line-height: 1.33333;
  font-family: var(--primary-font-Neue);
}
.Content_container h2,
.Content_container h3 {
  margin: 1.75rem 0 0.375rem;
  font-weight: 700;
  line-height: 1.2;
  scroll-margin-top: 3rem;
}
.Content_container h2 {
  font-size: 1.44rem;
}
.HoroscopeDetails_container {
  position: relative;
}
.List_container {
  position: relative;
}
.Content_container ol,
.Content_container ul {
  margin: 0.25rem 0;
  padding: 0 0 0 2rem;
}
.Content_container ul {
  list-style-type: disc;
}
.Content_container li {
  font-size: 1rem;
}
.List_container li {
  position: relative;
  padding: 0 0 0 4rem;
}
.List_container li:before {
  content: "";
  display: block;
  position: absolute;
  left: 0;
  width: 2.5rem;
  height: 2.5rem;
  margin: 0 0.25rem;
  background-size: 2.5rem 2.5rem;
  background-repeat: no-repeat;
  background-position: 50%;
}
.List_container li:first-child:before {
  background-image: url(https://images.ctfassets.net/cto6k7l91cv5/1Et5RvuypPqYUYJ54tfnOZ/a6873370bb87f384188a314cb6c72ad0/leo-astrological-icon.png?w=64&h=64&fit=fill&q=60&fm=webp);
}
.Content_container h2,
.Content_container h3 {
  margin: 1.75rem 0 0.375rem;
  font-family: var(--primary-font-Neue);
  font-weight: 700;
  line-height: 1.2;
  scroll-margin-top: 3rem;
}
.Content_container h3 {
  font-size: 1.2rem;
}
.Content_container p {
  margin-bottom: 0;
  font-size: 1rem;
  line-height: 1.33333;
}
.Content_container h2 + p,
.Content_container h3 + p,
.Content_container > p:first-child {
  margin-top: 0;
}
.Content_container li p {
  margin-top: 0.25rem;
  font-family: var(--primary-font-Neue);
}
.Sidebar_container {
  display: none;
  height: 100%;
}
.Sidebar_container .StoriesInfo_rightcntr.sidebar_advert_start {
  align-items: flex-start;
}
.Sidebar_related {
  position: sticky;
  top: 2.5rem;
  width: 100%;
  margin-bottom: 2rem;
}
.Sidebar_heading {
  margin: 0;
  font-size: 1rem;
  font-weight: 700;
  line-height: 1;
  border-top: 1px solid #dcdcd7;
  border-bottom: 1px solid #dcdcd7;
  font-family: var(--primary-font-scotch-display);
}
.Sidebar_list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}
.Sidebar_item {
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #dcdcd7;
}
.Item_container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
}
.Item_text {
  flex: 1 1 auto;
}
.Item_guide {
  color: #767522;
  font-size: 0.833rem;
  font-weight: 700;
  line-height: 1;
  text-transform: uppercase;
  letter-spacing: 0.025rem;
  font-family: var(--primary-font-scotch-display);
}
.Item_title {
  font-size: 1rem;
  font-weight: 700;
  line-height: 1.25;
  margin: 0;
  font-family: var(--primary-font-scotch-display);
}
.Item_hero {
  flex: 0 0 auto;
  width: 4.5rem;
  height: 4.5rem;
  background-color: #767522;
}
.Item_image {
  display: block;
  width: 4.5rem;
  height: 4.5rem;
}
.Sidebar_text {
  color: #6b6b68;
  font-size: 1rem;
  line-height: 1.25;
  margin: 1.25rem 0 0;
  font-family: var(--primary-font-Neue);
  font-weight: 400;
}
.monthly_horoscope-fullwidth {
  grid-template-columns: 100%;
}
.rich-text_wrapper.leftSide.left_para p {
  font-family: var(--primary-font-scotch-display);
}
/* horoscopes details page */
@media only screen and (min-width: 768px) {
  .List_hero {
    width: 5rem;
    height: 5rem;
  }
  .CategoryGrid_container {
    /* max-width: 71.25rem; */
    max-width: 100%;
    margin-top: 5rem;
    /* padding: 0 2rem; */
  }
  .InStarGrid_container {
    /* max-width: 71.25rem; */
    max-width: 100%;
    margin-top: 3rem;
    /* padding: 0 2rem; */
  }
  .Heading_container {
    /* margin-bottom: 3.75rem; */
    padding: 3rem 0;
  }
  .Heading_subtitle {
    font-size: 0.833rem;
  }
  .Heading_title {
    margin-top: 0.75rem;
    font-size: 2.986rem;
  }
  /* .Heading_container:after {
    width: 5rem;
    margin-top: 1.5rem;
  } */
  .CategoryGrid_item {
    grid-column: span 3;
  }
  .CategoryGrid_item video {
    width: 100%;
    height: 100%;
    /* position: absolute; */
    /* top: 0; */
  }
  .CategoryGrid_link {
    padding: 3rem 0;
  }
  .CategoryGrid_hero {
    margin: 0 0 0.75rem;
    width: 6rem;
    height: 6rem;
  }
  .CategoryGrid_image {
    width: 5.25rem;
    height: 5.25rem;
  }
  /* horoscopesdetails */
  .Article_container {
    display: grid;
    grid-template-rows: 1fr;
    grid-template-columns: repeat(12, [col-start] 1fr);
    -moz-column-gap: 1rem;
    column-gap: 1rem;
    max-width: 100rem;
    /* max-width: 100%; */
    /* padding: 0 2rem; */
  }
  /* .HoroscopeHeader_container {
    margin: 3rem 0 3.125rem;
  } */
  .Grid_container {
    -moz-column-gap: 1rem;
    column-gap: 1rem;
  }
  .HoroscopeHeader_text {
    margin-bottom: 2.5rem;
  }
  .HoroscopeHeader_list {
    margin-bottom: 0.5rem;
  }
  .HoroscopeHeader_item {
    font-size: 0.833rem;
  }
  .HoroscopeHeader_title {
    font-size: 2.986rem;
  }
  .Card_container {
    display: flex;
    min-height: 25rem;
  }
  .Card_text {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1.5rem 2rem 2rem;
  }
  .Card_heading {
    grid-template-rows: 2rem 3rem;
    display: flex;
    justify-content: space-between;
  }
  .Card_title {
    font-size: 1rem;
  }
  .Card_controls {
    flex-direction: row;
    align-items: center;
  }
  .Card_button {
    height: 2rem;
    padding: 0.125rem 0.75rem 0;
    border-radius: 1rem;
    font-size: 0.833rem;
  }
  .calendar-picker {
    font-size: 16px;
    padding: 10px;
  }
  .Card_avatar {
    grid-area: avatar;
    width: 100%;
    height: auto;
    margin-left: auto;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 20px;
  }
  .Card_horoscope {
    font-size: 1.2rem;
  }
  .Card_link {
    font-size: 1.2rem;
  }
  .List_container {
    margin-top: 2rem;
  }
  .List_list {
    row-gap: 2rem;
    -moz-column-gap: 2rem;
    column-gap: 0rem;
    /* max-height: none; */
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  /* .List_item {
    grid-column: span 2;
  } */
  /* .Content_container {
    display: grid;

    -moz-column-gap: 1rem;
    column-gap: 1rem;
  } */
  .Content_body {
    grid-column: 2 / span 10;
    margin-top: -0.625rem;
  }
  /* .Content_body:before {
    margin-top: 0.625rem;
    margin-bottom: 2rem;
  } */
  .Content_text {
    display: none;
  }
  .Content_container p {
    font-size: 1.2rem;
  }
  .Content_container h2,
  .Content_container h3 {
    margin: 2.5rem 0 0.5rem;
  }
  .Content_container h2 {
    font-size: 1.728rem;
  }
  .Content_container ol,
  .Content_container ul {
    margin: 0.5rem 0;
    padding: 0 0 0 2.5rem;
  }
  .List_container ul {
    margin: 0;
    padding: 0;
    list-style: none;
  }
  .Content_container li {
    font-size: 1.2rem;
  }
  .List_container li {
    padding-left: 5.5rem;
  }
  .List_container li:before {
    width: 3rem;
    height: 3rem;
    margin: 0 0.5rem;
    background-size: 3rem 3rem;
  }
  .List_button {
    display: none;
  }
  .Content_container h2,
  .Content_container h3 {
    margin: 2.5rem 0 2.5rem;
  }
  .Content_container h3 {
    font-size: 1.44rem;
  }
  .Content_container p {
    font-size: 1.2rem;
  }
  .Content_container li p {
    margin-top: 0.25rem;
  }
  .Sidebar_heading {
    padding: 0.875rem 0;
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
  .Item_guide {
    font-size: 0.833rem;
    margin-bottom: 0.5rem;
  }
  .Item_title {
    font-size: 1.2rem;
  }
  /* .Content_body:after {
    top: 0.125rem;
    width: 4rem;
    font-size: 1rem;
  } */
  /* horoscopesdetails */
}
@media only screen and (min-width: 1024px) {
  .CategoryGrid_list {
    gap: 30px;
  }
  /* horoscopesdetails */

  .HoroscopeHeader_text {
    grid-column: 2 / span 10;
  }
  .Card_text {
    flex: 0 1 66.66667%;
  }
  .Card_avatar {
    display: none;
  }
  .Card_content {
    margin-top: 0;
  }
  .Card_hero {
    display: block;
    flex: 0 1 33.33333%;
  }
  .List_item {
    grid-column: span 1;
  }
  .Content_container {
    grid-column: 1 / span 8;
  }
  .Sidebar_container {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    grid-column: 9 / span 4;
  }
  .Sidebar_related {
    width: 18.75rem;
    max-width: 18.75rem;
  }
  .monthly_horoscope-fullwidth {
    grid-template-columns: 31% 69%;
  }
  /* horoscopesdetails */
}
@media only screen and (min-width: 1140px) {
  .CategoryGrid_container {
    margin-top: 7.5rem;
    padding: 0;
  }
  .InStarGrid_container {
    margin-top: 4rem;
    padding: 0;
  }
  /* horoscopesdetails */
  .Article_container {
    padding: 0;
  }
  .Card_container {
    min-height: 38rem;
  }
  /* horoscopesdetails */
}
@media only screen and (min-width: 1366px) {
  /* .Heading_container:after {
    margin-top: 2rem;
  } */
}
