import React, { useEffect, useState } from "react";
import Router from "next/router";
// import SmoothScrolling from "@/components/common/SmoothScrolling";
import { usePathname } from "next/navigation";
import { useRouter } from "next/router";

import HomeLayout from "@/components/layouts/HomeLayout";
import AdsProvider from "@/context/AdsProvider";
import Layout from "@/components/layouts/Layout";
import { menus } from "@/helper/MenuData";
import "@/styles/globals.css";
import "@/styles/home.css";
import "@/styles/navbar2.css";
import "@/styles/navbar.css";
import "@/styles/stories.css";
import "swiper/css";
import "@/styles/details.css";
import "@/styles/astrology.css";
import "@/styles/calendar.css";

export default function App({ Component, pageProps }) {
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const isAmp = Component?.config?.amp;

  useEffect(() => {
    const handleStart = (url, { shallow }) => {
      if (!shallow) {
        setIsLoading(true);
      }
    };

    const handleStop = () => setIsLoading(false);

    router.events.on("routeChangeStart", handleStart);
    router.events.on("routeChangeComplete", handleStop);
    router.events.on("routeChangeError", handleStop);

    return () => {
      router.events.off("routeChangeStart", handleStart);
      router.events.off("routeChangeComplete", handleStop);
      router.events.off("routeChangeError", handleStop);
    };
  }, []);
  return (
    <>
      {isLoading && (
        <>
          <div className="loader-cont">
            <div class="MuiBox-root css-0">
              <span
                class="MuiLinearProgress-root MuiLinearProgress-colorPrimary MuiLinearProgress-indeterminate css-lrbo11-MuiLinearProgress-root"
                role="progressbar"
              >
                <span class="MuiLinearProgress-bar1"></span>
                <span class="MuiLinearProgress-bar2"></span>
              </span>
            </div>
          </div>
        </>
      )}
      <AdsProvider>
        {isAmp ? (
          <Component {...pageProps} />
        ) : pathname === "/" ? (
          <>
            <HomeLayout menu={menus}>
              {/* <SmoothScrolling> */}
              <Component {...pageProps} />
              {/* </SmoothScrolling> */}
            </HomeLayout>
          </>
        ) : (
          <>
            <Layout menu={menus}>
              {/* <SmoothScrolling> */}
              <Component {...pageProps} />
              {/* </SmoothScrolling> */}
            </Layout>
          </>
        )}
      </AdsProvider>
    </>
  );
}
