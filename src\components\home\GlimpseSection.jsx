import React, { useEffect } from "react";
import gsap from "gsap";
import Link from "next/link";
import Image from "next/image";
import { ScrollTrigger } from "gsap/dist/ScrollTrigger";
import styles from "@/components/home/<USER>";
import { dateFormateWithTimeShort, getAuthorText } from "@/utils/Util";
const GlimpseSection = ({ data, triggerId }) => {
  useEffect(() => {
    let tl = null;
    let timeoutId;

    const setupScrollTrigger = () => {
      const container = document.querySelector(`#${triggerId}`);
      if (!container) return;

      const middle = container.querySelector(`.${styles.middlesideeinner}`);
      const right = container.querySelector(`.${styles.rightsideeinner}`);
      if (!middle || !right) return;

      const middlesideinnerHeight = Math.abs(
        middle.scrollHeight - window.innerHeight
      );
      const rightsideinnerHeight = Math.abs(
        right.scrollHeight - window.innerHeight
      );
      const pinHeightvalue = window.innerWidth >= 1024 ? 0 : -50;

      // Kill existing triggers and timeline
      ScrollTrigger.getAll().forEach((trigger) => {
        if (trigger.vars.trigger === `#${triggerId}`) {
          trigger.kill();
        }
      });

      if (tl) tl.kill();

      // Create GSAP timeline with ScrollTrigger
      tl = gsap.timeline({
        scrollTrigger: {
          trigger: `#${triggerId}`,
          start: `0% ${pinHeightvalue}%`,
          end: `+=${rightsideinnerHeight}`,
          pin: true,
          scrub: 0.5,
          markers: false,
        },
      });

      tl.to(
        middle,
        {
          top: `-${middlesideinnerHeight}px`,
          ease: "none",
          duration: 2,
        },
        "a"
      );

      tl.to(
        right,
        {
          top: `-${rightsideinnerHeight}px`,
          ease: "none",
          duration: 2,
        },
        "a"
      );
    };

    // Delay setup 2 seconds after mount
    timeoutId = setTimeout(() => {
      setupScrollTrigger();
      ScrollTrigger.refresh();
      ScrollTrigger.sort();
    }, 2000);

    // Re-setup on resize, also with 2s delay
    const handleResize = () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setupScrollTrigger();
        ScrollTrigger.refresh();
        ScrollTrigger.sort();
      }, 2000);
    };

    window.addEventListener("resize", handleResize);

    // Cleanup
    return () => {
      window.removeEventListener("resize", handleResize);
      clearTimeout(timeoutId);
      ScrollTrigger.getAll().forEach((trigger) => {
        if (trigger.vars.trigger === `#${triggerId}`) {
          trigger.kill();
        }
      });
      if (tl) tl.kill();
    };
  }, [triggerId]);

  const leftSideData = data && data.length > 0 ? data[0] : {};
  const midSideData = data && data.length > 0 ? data.slice(1, 5) : [];
  const rightSideData = data && data.length > 0 ? data.slice(5) : [];
  return (
    <div>
      {data && data.length > 0 && (
        <div className={styles.Celebrities_wrappers} id={triggerId}>
          <div className={styles.celeb_gridd}>
            <div className={styles.celeb_leftt}>
              <div className={styles.leftsliderr}>
                <Link
                  href={leftSideData?.slug ?? "#"}
                  className={styles.celeb_StickyColumn}
                >
                  <Image
                    className={`${styles.lazy} ${styles.loaded}`}
                    src={
                      leftSideData?.croppedImg
                        ? leftSideData?.croppedImg
                        : leftSideData?.coverImg
                        ? leftSideData?.coverImg
                        : ""
                    }
                    alt={leftSideData?.altName ?? ""}
                    data-was-processed="true"
                    fill
                  />
                  <div className={styles.more_trend_wrapp}>
                    <strong>{leftSideData?.category ?? ""}</strong>
                    <h2 className={styles.slug}>{leftSideData?.title ?? ""}</h2>
                    <p className={styles.blog_banner_text_descc}>
                      {leftSideData?.excerpt ?? ""}
                    </p>
                    <span className={styles.date_authorr}>
                      {`${dateFormateWithTimeShort(leftSideData?.timestamp)}`}
                      {getAuthorText(
                        " // ",
                        leftSideData?.author,
                        leftSideData?.contributor
                      )}
                    </span>
                  </div>
                </Link>
              </div>
            </div>
            <div className={styles.celeb_multi_gridd}>
              <div className={styles.middlesidee}>
                <div className={styles.middlesideeinner}>
                  {midSideData &&
                    midSideData.length > 0 &&
                    midSideData.map((item, index) => {
                      return (
                        <Link
                          className={styles.rightcardd}
                          href={item?.slug ?? "#"}
                          key={`rightcard-item-${index}`}
                        >
                          <figure>
                            <Image
                              className={`${styles.lazy} ${styles.loaded}`}
                              src={
                                item?.croppedImg
                                  ? item?.croppedImg
                                  : item?.coverImg
                                  ? item?.coverImg
                                  : ""
                              }
                              data-src={
                                item?.croppedImg
                                  ? item?.croppedImg
                                  : item?.coverImg
                                  ? item?.coverImg
                                  : ""
                              }
                              alt={item?.altName || ""}
                              data-was-processed="true"
                              fill
                            />
                          </figure>
                          <div className={styles.relatedPostContentt}>
                            <strong>{item?.category ?? ""}</strong>

                            <h2>{item?.title ?? ""}</h2>
                          </div>
                        </Link>
                      );
                    })}
                </div>
              </div>
              <div className={styles.rightsidee}>
                <div className={styles.rightsideeinner}>
                  {rightSideData &&
                    rightSideData.length > 0 &&
                    rightSideData.map((item, index) => {
                      return (
                        <Link
                          className={styles.rightcardd}
                          href={item?.slug ?? "#"}
                          key={`rightcard-item-${index}`}
                        >
                          <figure>
                            <Image
                              className={`${styles.lazy} ${styles.loaded}`}
                              src={
                                item?.croppedImg
                                  ? item?.croppedImg
                                  : item?.coverImg
                                  ? item?.coverImg
                                  : ""
                              }
                              data-src={
                                item?.croppedImg
                                  ? item?.croppedImg
                                  : item?.coverImg
                                  ? item?.coverImg
                                  : ""
                              }
                              alt={item?.altName || ""}
                              data-was-processed="true"
                              fill
                            />
                          </figure>
                          <div className={styles.relatedPostContentt}>
                            <strong>{item?.category ?? ""}</strong>

                            <h2>{item?.title ?? ""}</h2>
                          </div>
                        </Link>
                      );
                    })}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default GlimpseSection;
