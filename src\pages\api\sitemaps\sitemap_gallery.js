import { Const } from "@/utils/Constants";
import { getImageGallerySitemap } from "@/pages/api/ArticleApi";
import { escapeXml, convertToISTISOString } from "@/utils/Util";
export default async function handler(req, res) {
  try {
    res.setHeader("Content-Type", "application/xml");
    const response = await getImageGallerySitemap();
    const data = response?.data?.length > 0 ? response.data : [];

    if (data.length > 0) {
      const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
      <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xmlns:xhtml="https://www.w3.org/1999/xhtml"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
            http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd
            https://www.w3.org/1999/xhtml 
            https://www.w3.org/2002/08/xhtml/xhtml1-strict.xsd">
        >
        ${data
          .map((item) => {
            const imagesXml =
              item.images
                ?.map(
                  (img) => `
            <image:image>
                <image:loc>${escapeXml(img?.src || "")}</image:loc>
                ${img?.title ? `<image:title>${escapeXml(img?.title || "")}</image:title>` : ""}
                ${
                  img?.caption
                    ? `<image:caption>${escapeXml(img?.caption) || ""}</image:caption>`
                    : ""
                }
            </image:image>`
                )
                .join("") || "";
            return `<url>
                        <loc>${Const.ClientLink}${item?.loc || ""}</loc>
                        <lastmod>${convertToISTISOString(item?.lastmod || "")}</lastmod>
                        <changefreq>${item?.changefreq || "hourly"}</changefreq>
                        <priority>${item?.priority || 0.9}</priority>
                        ${imagesXml}
                    </url>`;
          })
          .join("")}
        </urlset>`;

      return res.status(200).send(sitemap);
    } else {
      return res.status(404).send("No data found");
    }
  } catch (error) {
    console.error("Error generating sitemap:", error.message);
    return res.status(500).send("Internal Server Error");
  }
}
