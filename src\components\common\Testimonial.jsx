import React, { useEffect, useRef, useState } from "react";
import { SlArrowLeft } from "react-icons/sl";
import { FaArrowRightLong, FaArrowLeftLong } from "react-icons/fa6";
import { IoIosStar } from "react-icons/io";
import { Swiper, SwiperSlide } from "swiper/react";
import Image from "next/image";
import Button from "./Button";
import Link from "next/link";
import { htmlParser } from "@/utils/Util";
const data = [
  {
    content:
      "<label>What's in a name?<br /><span>Turns out, everything!</span></label>",
  },
  {
    content:
      "For <b>Manifest</b> to become one of Cambridge Dictionary's most viewed words of 2024 — 1,30,000 hits and counting — it means some of us must have Googled it at least once. I know, I hit that search button over and over again <br /><span>because each time I looked at it, I saw a new meaning.</span>",
  },
  {
    content:
      "Putting together a homegrown title at a time when the demise of print has been long announced may seem surprising...<br /><span>...but it has long been a dream of mine to give India a magazine it deserves. A magazine that is the country.</span>",
  },
  {
    content: `<span className='mt-0' style='margin-bottom: 15px'>And nothing, absolutely nothing, represents India more than our weddings.</span>It is a time when families are brought together. Traditions come alive as they are adapted to each couple's beliefs. And lives are joined in a way that the romantic in me still enjoys.`,
  },
  {
    content: `<span className='mt-0' style='margin-bottom: 15px'>The fact that it comes with band, baaja and baraat — what's not to love?</span>And that is the reason our first issue celebrates:<br /><span>THE NEW BRIDE.</span>`,
  },
  {
    content: `If you look at<br /><span style='margin-bottom: 15px'>'Curate Beautiful, Create Happy'</span>individually, they are powerful but when you put them together, they become a promise. While each section is dedicated to a word, I hope you will see this as your first of many handbooks for happiness.`,
  },
  {
    content:
      "Where we give you a fast-paced look into everything you should wish list when you start to think about marriage. What to buy and how to dress, along with modern mithai and the homegrown fragrance makers to bookmark. Make special note of the feature on alta.",
    image: "/Assets/curate-new.png",
    altName: "Curate",
  },
  {
    content:
      "It has everything you need to make your wedding special. From the big comeback of red and pink in bridal wear to how the cool kids are wearing corsets to the celebration, there is only one way to get ready for a wedding —with enjoyment.",
    image: "/Assets/beautiful-new.png",
    altName: "Beautiful",
  },
  {
    content:
      "This section is a building block, a step to a better life. A place where we have all the answers. Or at least the beginning of a great conversation.",
    image: "/Assets/create-new.png",
    altName: "Create",
  },
  {
    content:
      "This needs very little description. It has travel with a special focus on incredible India, and a collection of wedding albums, sourced randomly, but collected with abundance because happiness has no limits.",
    image: "/Assets/happy-new.png",
    altName: "Happy",
  },
  {
    content: `This first issue has a lot more, including a carefully edited address book of everything you need to make your wedding perfectly 'gramworthy! But it doesn't stop there. Each issue will be different because each one of you is unique.<br /><span>Because we manifested this difference, now we will celebrate you.</span>`,
  },
];
const Testimonial = () => {
  const swiperRef = useRef(null);

  const [isAtStart, setIsAtStart] = useState(true);
  const [isAtEnd, setIsAtEnd] = useState(false);

  const handleScrollLeft = () => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slidePrev();
    }
  };

  const handleScrollRight = () => {
    if (swiperRef.current) {
      swiperRef.current.swiper.slideNext();
    }
  };

  useEffect(() => {
    const swiperInstance = swiperRef.current?.swiper;
    if (swiperInstance) {
      const updateArrowVisibility = () => {
        setIsAtStart(swiperInstance.isBeginning);
        setIsAtEnd(swiperInstance.isEnd);
      };

      updateArrowVisibility(); // Initial update

      swiperInstance.on("slideChange", updateArrowVisibility);
      return () => {
        swiperInstance.off("slideChange", updateArrowVisibility);
      };
    }
    //     const swiper = new Swiper(".swiper", {
    //       // Optional parameters
    //       direction: "horizontal",
    //       loop: true,
    //       autoHeight: true,

    //       // If we need pagination
    //       pagination: {
    //         el: ".swiper-pagination",
    //       },

    //       // Navigation arrows
    //       navigation: {
    //         nextEl: ".swiper-button-next",
    //         prevEl: ".swiper-button-prev",
    //       },

    //       // And if we need scrollbar
    //       /*scrollbar: {
    //     el: '.swiper-scrollbar',
    //   },*/
    //     });
  }, []);

  return (
    <div className="new_testimonial_design">
      <div className="section__color-wrapper">
        <div className="container">
          <div className="">
            <div className="">
              <div className="testimonial-list_wrapper_main">
                <div className="testimonial-list_wrapper">
                  <Swiper
                    className="swiper-wrapper"
                    ref={swiperRef}
                    // modules={[Pagination, FreeMode]}
                    // spaceBetween={10}
                    // className="mySwiper"
                    // breakpoints={{
                    //   1024: {
                    //     slidesPerView: 4,
                    //     spaceBetween: 30,
                    //   },
                    //   992: {
                    //     slidesPerView: 3,
                    //     spaceBetween: 30,
                    //   },
                    //   768: {
                    //     slidesPerView: 2,
                    //     spaceBetween: 20,
                    //   },
                    //   600: {
                    //     slidesPerView: 2.25,
                    //     spaceBetween: 20,
                    //   },
                    //   0: {
                    //     slidesPerView: 1.25,
                    //     spaceBetween: 20,
                    //   },
                    // }}
                  >
                    {data.map((item, index) => {
                      return (
                        <>
                          <SwiperSlide
                            className="swiper-slide"
                            key={`swiper-card-item-${index}`}
                          >
                            <div className="content-wrapper">
                              <div className="content">
                                {/* <div className="testimonial__star_main">
                                  <div className="testimonial__star_item">
                                    <IoIosStar />
                                  </div>
                                  <div className="testimonial__star_item">
                                    <IoIosStar />
                                  </div>
                                  <div className="testimonial__star_item">
                                    <IoIosStar />
                                  </div>
                                  <div className="testimonial__star_item">
                                    <IoIosStar />
                                  </div>
                                  <div className="testimonial__star_item">
                                    <IoIosStar />
                                  </div>
                                </div> */}
                                {item?.image ? (
                                  <>
                                    <div className="testimonial_title_image">
                                      <Image
                                        src={item?.image || ""}
                                        alt={item?.altName || ""}
                                        fill
                                      />
                                    </div>
                                    <blockquote className="testimonial__content blockquote">
                                      {htmlParser(item.content)}
                                    </blockquote>
                                  </>
                                ) : (
                                  <blockquote className="testimonial__content blockquote">
                                    {htmlParser(item.content)}
                                  </blockquote>
                                )}
                                <p className="testimonial__author">
                                  - Avarna Jain,
                                  <br />
                                   Chairperson RPSG Lifestyle Media
                                </p>
                              </div>
                            </div>
                          </SwiperSlide>
                        </>
                      );
                    })}
                  </Swiper>
                  <div className="swiper-nav-wrapper testimonial-list__nav">
                    <button
                      aria-label="Previous story"
                      className="card-actions_actions_button arrowbtn"
                      onClick={handleScrollLeft}
                      disabled={isAtStart}
                    >
                      <FaArrowLeftLong style={{ opacity: isAtStart ? 0 : 1 }} />
                    </button>
                    <button
                      aria-label="Previous story"
                      className="card-actions_actions_button arrowbtn"
                      onClick={handleScrollRight}
                      disabled={isAtEnd}
                    >
                      <FaArrowRightLong
                        style={{ opacity: isAtStart ? 0 : 1 }}
                      />
                    </button>
                  </div>
                </div>

                <div className="testimonial_product_main">
                  <div className="testimonial_product">
                    <div className="tp_wrapper">
                      <div className="tp_media">
                        <div className="tp_image">
                          <Link
                            href="https://shop.yudirect.biz/ManifestLife/Subscribe.php"
                            target="_blank"
                          >
                            <Image
                              id="block-testimonial_XHMbWy-34619739308283"
                              className=""
                              sizes="(max-width: 740px) calc(100vw - 96px), (max-width: 999px) calc(100vw - 160px), 600px"
                              loading="lazy"
                              fill
                              objectFit="contain"
                              alt="Khushi Kapoor on Manifest Cover"
                              src="/Assets/Manifest-Artboard-2.jpg"
                            />
                          </Link>
                        </div>
                        <div className="tp_button">
                          <Link
                            href="https://shop.yudirect.biz/ManifestLife/Subscribe.php"
                            target="_blank"
                          >
                            {" "}
                            <Button>Subscribe</Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Testimonial;
