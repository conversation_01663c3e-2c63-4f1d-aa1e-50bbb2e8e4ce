import Headers from "./Headers";
import { ProcessAPI, Const } from "@/utils/Constants";

// Get THR TV 
export const getThrTv = async (slug) => {
  const res = await fetch(Const.Link + "api/page/thrtv", new Headers("GET"));
  return ProcessAPI(res);
};

// Get THR TV Most Recent
export const getThrTvMostRecent = async (slug) => {
  const res = await fetch(Const.Link + "api/page/thrtv-most-recent", new Headers("GET"));
  return ProcessAPI(res);
};
// Get THR TV Section
export const getThrTvSections = async (slug) => {
  const res = await fetch(Const.Link + "api/page/thrtv-section", new Headers("GET"));
  return ProcessAPI(res);
};

// Get Video Data
export const getVideo = async (slug) => {
  const res = await fetch(Const.Link + `api/video?slug=${slug}`, new Headers("GET"));
  return ProcessAPI(res);
};
