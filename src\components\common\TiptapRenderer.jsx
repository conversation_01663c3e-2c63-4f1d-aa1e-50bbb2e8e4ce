import React, { useEffect, useState } from "react";
import { Tweet } from "react-tweet";
import Image from "next/image";
import Link from "next/link";
import { Ads } from "@/components/ads/Ads";
import {
  hasHtmlTags,
  getEmbedType,
  extractTwitterId,
  getAuthorText,
} from "@/utils/Util";
import { getRelatedStories } from "@/pages/api/ArticleApi";

const renderListContent = (content) => {
  const renderedContent = [];
  content.forEach((node, index) => {
    let element = null;
    switch (node.type) {
      case "paragraph":
        element = (
          <p
            style={{
              ...(node.attrs?.textAlign && {
                textAlign: node.attrs.textAlign,
              }),
            }}
          >
            {renderContent(node.content)}
            <br />
          </p>
        );
        break;
      case "text":
        let textStyles = {};
        let isLink = false;
        let linkAttrs = {};

        if (node.marks) {
          node.marks.forEach((mark) => {
            if (mark.type === "textStyle") {
              textStyles.color = mark?.attrs?.color;
            }
            if (mark.type === "bold") {
              textStyles.fontWeight = "bold";
            }
            if (mark.type === "italic") {
              textStyles.fontStyle = "italic";
            }
            if (mark.type === "link") {
              isLink = true;
              linkAttrs = mark.attrs;
            }
          });
        }
        const textElement = (
          <span key={index} style={textStyles}>
            {node.text}
          </span>
        );
        element = isLink ? (
          <Link
            key={index}
            href={linkAttrs.href}
            target={linkAttrs.target || "_blank"}
            rel={linkAttrs.rel || "noopener noreferrer"}
            style={textStyles}
          >
            {textElement}
          </Link>
        ) : (
          textElement
        );
        break;
      case "hardBreak":
        element = <br key={`break-${index}`} />;
        break;
      case "horizontalRule":
        element = <hr key={`hr-${index}`} />;
        break;
      case "heading":
        const HeadingTag = `h${node.attrs.level}`;
        element = (
          <div className="StoriesInfo_left_innercntr-full-width" key={index}>
            <div className="">
              <div className="rich-text_wrapper">
                <HeadingTag
                  style={{
                    ...(node.attrs?.textAlign && {
                      textAlign: node.attrs.textAlign,
                    }),
                    lineHeight: node.attrs.lineHeight,
                    margin: "25px 0px 10px 0px",
                    // fontSize:
                    //   node.attrs.level === 1
                    //     ? "42px!important"
                    //     : node.attrs.level === 2
                    //     ? "34px!important"
                    //     : "34px!important",
                  }}
                >
                  {renderContent(node.content)}
                </HeadingTag>
              </div>
            </div>
          </div>
        );
        break;
    }
    if (element) {
      renderedContent.push(element);
    }
  });
  return renderedContent;
};
const renderContent = (content, router, relatedPosts) => {
  if (!content || content.length === 0) return null;
  const renderedContent = [];
  const meaningfulNodes = content.filter((node) => node.type !== "text");
  const totalNodes = meaningfulNodes.length;

  const adPositions = [
    Math.floor(totalNodes / 3),
    Math.floor((2 * totalNodes) / 3),
  ];

  let nonTextIndex = 0;

  content.forEach((node, index) => {
    let element = null;
    switch (node.type) {
      case "paragraph":
        element = (
          <div className="StoriesInfo_left_innercntr-full-width" key={index}>
            <div className="">
              <div className="rich-text_wrapper">
                <p
                  style={{
                    ...(node.attrs?.textAlign && {
                      textAlign: node.attrs.textAlign,
                    }),
                  }}
                >
                  {renderContent(node.content)}
                  <br />
                </p>
              </div>
            </div>
          </div>
        );
        break;
      case "text":
        let textStyles = {};
        let isLink = false;
        let linkAttrs = {};

        if (node.marks) {
          node.marks.forEach((mark) => {
            if (mark.type === "textStyle") {
              textStyles.color = mark?.attrs?.color;
            }
            if (mark.type === "bold") {
              textStyles.fontWeight = "bold";
            }
            if (mark.type === "italic") {
              textStyles.fontStyle = "italic";
            }
            if (mark.type === "link") {
              isLink = true;
              linkAttrs = mark.attrs;
            }
          });
        }
        const textElement = (
          <span key={index} style={textStyles}>
            {node.text}
          </span>
        );
        element = isLink ? (
          <Link
            key={index}
            href={linkAttrs.href}
            target={linkAttrs.target || "_blank"}
            rel={linkAttrs.rel || "noopener noreferrer"}
            style={textStyles}
          >
            {textElement}
          </Link>
        ) : (
          textElement
        );
        break;
      case "hardBreak":
        element = <br key={`break-${index}`} />;
        break;
      case "horizontalRule":
        element = <hr key={`hr-${index}`} />;
        break;
      case "imageBlock":
        element = (
          <div className="StoriesInfo_left_innercntr-full-width" key={index}>
            <div
              style={{
                display: "flex",
                justifyContent: node.attrs.align,
                width: "100%",
              }}
            >
              <div
                className="media-grid_wrapper"
                style={{ width: node.attrs.width }}
              >
                <div className="media-grid_wrapper_inner-full-width">
                  <Image
                    src={node.attrs.src}
                    alt={node.attrs.alt}
                    layout="responsive"
                    style={{ width: "100%", height: "100%" }}
                    width={700}
                    height={475}
                    objectFit="cover"
                  />
                  {(node.attrs.caption || node.attrs.courtesy) && (
                    <div className="Stories_caption_wrapper">
                      {hasHtmlTags(node.attrs.caption) ? (
                        <span
                          className="Stories_caption"
                          dangerouslySetInnerHTML={{
                            __html: node.attrs.caption,
                          }}
                        />
                      ) : (
                        <span className="Stories_caption">
                          {node.attrs.caption}
                        </span>
                      )}
                      {hasHtmlTags(node.attrs.courtesy) ? (
                        <span
                          className="Stories_courtesy"
                          dangerouslySetInnerHTML={{
                            __html: node.attrs.courtesy,
                          }}
                        />
                      ) : (
                        <span className="Stories_courtesy">
                          {node.attrs.courtesy}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
        break;
      case "videoBlock":
        element = (
          <div className="StoriesInfo_left_innercntr-full-width" key={index}>
            <div
              style={{
                display: "flex",
                justifyContent: node.attrs.align,
                width: "100%",
                margin: "20px 0",
              }}
            >
              <div
                className="media-grid_wrapper"
                style={{ width: node.attrs.width }}
              >
                <div className="media-grid_wrapper_inner-full-width">
                  <video
                    src={node.attrs.src}
                    alt={node.attrs.alt}
                    controls
                    muted
                    width={"100%"}
                    height={"100%"}
                    style={{ objectFit: "cover" }}
                  />
                  {(node.attrs.caption || node.attrs.courtesy) && (
                    <div className="Stories_caption_wrapper">
                      {hasHtmlTags(node.attrs.caption) ? (
                        <span
                          className="Stories_caption"
                          dangerouslySetInnerHTML={{
                            __html: node.attrs.caption,
                          }}
                        />
                      ) : (
                        <span className="Stories_caption">
                          {node.attrs.caption}
                        </span>
                      )}
                      {hasHtmlTags(node.attrs.courtesy) ? (
                        <span
                          className="Stories_courtesy"
                          dangerouslySetInnerHTML={{
                            __html: node.attrs.courtesy,
                          }}
                        />
                      ) : (
                        <span className="Stories_courtesy">
                          {node.attrs.courtesy}
                        </span>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
      case "orderedList":
        element = (
          <ol
            key={index}
            start={node?.attrs?.start || 1}
            className="Stories_list_style"
          >
            {node.content.map((item, idx) => (
              <li key={idx}>{renderListContent(item.content)}</li>
            ))}
          </ol>
        );
        break;
      case "bulletList":
        element = (
          <ul
            key={index}
            start={node?.attrs?.start || 1}
            className="Stories_list_style"
          >
            {node.content.map((item, idx) => (
              <li key={idx}>{renderListContent(item.content)}</li>
            ))}
          </ul>
        );
        break;
      case "heading":
        const HeadingTag = `h${node.attrs.level}`;
        element = (
          <div className="StoriesInfo_left_innercntr-full-width" key={index}>
            <div className="">
              <div className="rich-text_wrapper">
                <HeadingTag
                  style={{
                    ...(node.attrs?.textAlign && {
                      textAlign: node.attrs.textAlign,
                    }),
                    lineHeight: node.attrs.lineHeight,
                    margin: "25px 0px 10px 0px",
                    // fontSize:
                    //   node.attrs.level === 1
                    //     ? "42px!important"
                    //     : node.attrs.level === 2
                    //     ? "34px!important"
                    //     : "34px!important",
                  }}
                >
                  {renderContent(node.content)}
                </HeadingTag>
              </div>
            </div>
          </div>
        );
        break;
      case "embed":
        element = (
          <>
            {getEmbedType(node.attrs.embed) === "twitter" ? (
              <div
                className="StoriesInfo_left_innercntr-full-width"
                key={index}
                style={{
                  width: node.attrs.width,
                  // paddingTop:"70%",
                  margin: "20px 0",
                }}
              >
                <div
                  className="flex-all-embed"
                  style={{ justifyContent: node.attrs.align }}
                >
                  <Tweet id={extractTwitterId(node.attrs.embed)} />
                </div>
              </div>
            ) : (
              <div
                className={`StoriesInfo_left_innercntr-full-width embed-${getEmbedType(
                  node.attrs.embed
                )}`}
                key={index}
                style={{
                  width: node.attrs.width,
                  // paddingTop:"70%",
                  margin: "20px 0",
                }}
                dangerouslySetInnerHTML={{ __html: node.attrs.embedFrame }}
              ></div>
            )}
            {(node.attrs.caption || node.attrs.courtesy) && (
              <div className="Stories_caption_wrapper">
                {hasHtmlTags(node.attrs.caption) ? (
                  <span
                    className="Stories_caption"
                    dangerouslySetInnerHTML={{
                      __html: node.attrs.caption,
                    }}
                  />
                ) : (
                  <span className="Stories_caption">{node.attrs.caption}</span>
                )}
                {hasHtmlTags(node.attrs.courtesy) ? (
                  <span
                    className="Stories_courtesy"
                    dangerouslySetInnerHTML={{
                      __html: node.attrs.courtesy,
                    }}
                  />
                ) : (
                  <span className="Stories_courtesy">
                    {node.attrs.courtesy}
                  </span>
                )}
              </div>
            )}
          </>
        );
        break;
      case "relatedPosts":
        const key = node?.attrs?.blockId || node?.attrs?.postIds?.join(",");
        const posts = relatedPosts?.[key] || [];
        element = (
          <div key={index} className="StoriesInfo_left_innercntr-full-width">
            <div
              style={{
                display: "flex",
                justifyContent: node.attrs.align,
                width: "100%",
              }}
            >
              <div className={"related-post-wrapper"}>
                <h2 className={"related-post-heading"}>
                  {node?.attrs?.title || ""}
                </h2>
                <div
                  className={node?.attrs?.layout ?? "grid"}
                  style={{ width: node?.attrs?.width ?? "100%" }}
                >
                  {posts?.map((post, idx) => (
                    <Link
                      key={idx}
                      className={"related-post-card"}
                      target="_blank"
                      href={`${post?.slug}`}
                      passHref
                    >
                      <div className={"related-post-image"}>
                        <Image
                          className="imgcover"
                          src={post?.croppedImg || post?.coverImg || ""}
                          alt={post?.altName || ""}
                          fill
                          objectFit="cover"
                        />
                      </div>
                      <div
                        style={{
                          width: "100%",
                          display: "flex",
                          flexDirection: "column",
                          gap: "5px",
                        }}
                      >
                        <strong>{post?.category}</strong>
                        {/* <h3 className={`${"category"} mt-0 mb-1`}>
                        {post?.category}
                      </h3> */}
                        <h3 className={`card-title mb-0`}>{post?.title}</h3>
                        <span className="author">
                          {getAuthorText("", post?.author, post?.contributor)}
                        </span>
                      </div>
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        );
        break;
      default:
        break;
    }
    if (element) {
      renderedContent.push(element);
      if (node.type !== "text" && node.type !== "hardBreak") {
        if (adPositions.includes(nonTextIndex)) {
          renderedContent.push(
            <div
              className="StoriesInfo_left_innercntr-full-width"
              key={`ad-${nonTextIndex}`}
            >
              <Ads
                id={`div-gpt-ad-stories-middle-${nonTextIndex}`}
                style={{ margin: "30px auto" }}
                adUnits={[
                  {
                    adUnit: "/23290324739/Manifest-Desktop-Middle-300",
                    sizes: [[300, 250]],
                    sizeMapping: [
                      {
                        viewport: [0, 0],
                        sizes: [[300, 250]],
                      },
                    ],
                    minWidth: 1024,
                    maxWidth: Infinity,
                  },
                  {
                    adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                    sizeMapping: [
                      {
                        viewport: [1023, 0],
                        sizes: [
                          [300, 250],
                          [300, 100],
                        ],
                      },
                    ],
                    minWidth: 768,
                    maxWidth: 1023,
                  },
                  {
                    adUnit: "/23290324739/Manifest-Mobile-Middle-300",
                    sizes: [
                      [300, 250],
                      [300, 100],
                    ],
                    sizeMapping: [
                      {
                        viewport: [767, 0],
                        sizes: [
                          [300, 250],
                          [300, 100],
                        ],
                      },
                    ],
                    minWidth: 0,
                    maxWidth: 767,
                  },
                ]}
                targeting={{
                  section: [router?.[1] || null],
                  "sub-section": [router?.[2] || null],
                }}
              />
            </div>
          );
        }
        nonTextIndex++;
      }
    }
  });

  return renderedContent;
};

// TiptapRendered component
const TiptapRendered = ({ content, router }) => {
  const [relatedPosts, setRelatedPosts] = useState([]);

  useEffect(() => {
    const fetchAllRelatedPosts = async () => {
      const allRelatedPosts = {};

      const relatedPostNodes = content?.filter(
        (node) => node.type === "relatedPosts"
      );

      await Promise.all(
        relatedPostNodes.map(async (node) => {
          const key = node?.attrs?.blockId || node?.attrs?.postIds?.join(",");
          if (!key) return;

          try {
            const postIds = node.attrs.postIds;
            const payload = { _id: postIds };
            const response = await getRelatedStories(payload);

            if (response.status === "success") {
              allRelatedPosts[key] = response.data || [];
            }
          } catch (err) {
            console.error(`Error loading posts for block ${key}:`, err.message);
          }
        })
      );

      setRelatedPosts(allRelatedPosts); // object: { [key]: postData[] }
    };

    fetchAllRelatedPosts();
  }, [content]);
  return <>{renderContent(content, router, relatedPosts)}</>;
};

export default TiptapRendered;
