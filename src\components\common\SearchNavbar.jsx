import React, { useEffect, useRef, useState } from "react";
import { useRouter } from "next/router";
import { RxCross2 } from "react-icons/rx";
import { RiArrowRightSLine } from "react-icons/ri";
import { FiSearch } from "react-icons/fi";
import Link from "next/link";
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from "react-outside-click-handler";

const trendingSearches = [
  { name: "Best Of 2024", search: "best of 2024" },

  { name: "relationships", search: "relationships" },
  { name: "Fashion Designer", search: "fashion designer" },
  { name: "Jewellery", search: "jewellery" },
  { name: "skincare", search: "skincare" },
  { name: "India Couture Week", search: "india couture week" },
  { name: "Lakmé Fashion Week", search: "lakmé fashion week" },
  { name: "make-up", search: "makeup" },
  { name: "Beauty Products", search: "beauty products" },
  { name: "gifting Guide", search: "gifting guide" },
  { name: "Manifestation", search: "manifestation" },
  { name: "Diwali", search: "diwali" },
  {
    name: "FDCI Manifest Wedding Weekend",
    search: "fdci manifest wedding weekend",
  },
  { name: "Lakmé Fashion Week 2024", search: "lakmé fashion week 2024" },
  { name: "FDCI x Lakmé Fashion Week", search: "fdci x lakmé fashion week" },
];
const SearchNavbar = ({ openSearch, setOpenSearch }) => {
  const router = useRouter();
  const [text, setText] = useState("");
  const LIMIT = 12;
  const inputSearchRef = useRef(null);

  const handleSearch = (e) => {
    e.preventDefault();
    const trimmedText = text?.trim();
    if (!trimmedText) return; // Prevent empty searches

    router.push(`/result?query=${text}&resultsLimit=${LIMIT}`);
    setOpenSearch(false);
    setText("");
  };

useEffect(() => {
  if (inputSearchRef.current) {
    if (openSearch) {
      inputSearchRef.current.focus();
    } else {
      inputSearchRef.current.blur();
    }
  }
}, [openSearch]);



  return (
    <div
      className="searchNavbar"
      style={{
        pointerEvents: openSearch ? "all" : "none",
        backgroundColor: openSearch
          ? "rgba(0, 0, 0, 0.5) "
          : "rgba(0, 0, 0, 0)",
      }}
    >
      <div
        className="menu-inner"
        style={{
          transform: openSearch ? "translateX(0%)" : "translateX(100%)",
        }}
      >
        <OutsideClickHandler onOutsideClick={() => setOpenSearch(false)}>
          <div className="manu-inner-block">
            <div className="menu-top">
              <span />
              <div className="menu-search-close">
                {/* <div className="menu-search t3 flex-all">
                  Menu <RiArrowRightSLine />
                </div> */}
                <div
                  className="menu-close flex-all"
                  onClick={() => setOpenSearch(false)}
                >
                  <RxCross2 />
                </div>
              </div>
            </div>
            <div className="menu-main search-cont">
              <div className="search-group">
                <form onSubmit={handleSearch}>
                  <input
                    type="text"
                    placeholder={"Search"}
                    value={text}
                    ref={inputSearchRef}
                    onChange={(e) => {
                      setText(e.target.value);
                    }}
                    className="search-bar"
                  />
                </form>
                <FiSearch className="search-icon" onClick={handleSearch} />
              </div>
              <div className="ts-cont">
                <div className="ts-title">
                  Trending Searches <RiArrowRightSLine />
                </div>
                <div className="ts-items">
                  {trendingSearches.map((item, index) => (
                    <Link
                      className="ts-item"
                      href={`/result?query=${item.search}&resultsLimit=${LIMIT}`}
                      onClick={() => setOpenSearch(false)}
                      key={index}
                    >
                      {item.name}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </OutsideClickHandler>
      </div>
    </div>
  );
};

export default SearchNavbar;
