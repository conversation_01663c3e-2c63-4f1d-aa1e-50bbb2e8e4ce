import React, { useState } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import Outside<PERSON>lick<PERSON>and<PERSON> from "react-outside-click-handler";
import { FaWhatsapp, FaFacebookF, FaLink } from "react-icons/fa";
import { BsTwitterX } from "react-icons/bs";
import { Const } from "@/utils/Constants";
import { LuCopy } from "react-icons/lu";
import { IoCloseOutline } from "react-icons/io5";
const ShareModal = ({ show, setShow }) => {
  const [copied, setCopied] = useState(false);
  const pathname = usePathname();
  const URL = `${Const.ClientLink}${pathname}`;
  const shareLinks = {
    whatsapp: `https://wa.me/?text=${encodeURI(URL)}`,
    facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURI(URL)}`,
    twitter: `https://twitter.com/intent/tweet?url=${encodeURI(URL)}`,
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(URL);
    // setShow(false);
    setCopied(true);
  };

  return (
    <div className={`share__modal ${show ? "show" : ""}`}>
      <OutsideClickHandler
        onOutsideClick={() => {
          setShow(false);
          setCopied(false);
        }}
      >
        <div className="share__content">
          <div className="share_body">
            <h2>Share this with your community</h2>
            <IoCloseOutline
              id="share_modal_close_btn"
              onClick={() => {
                setShow(false);
                setCopied(false);
              }}
            />
            <div className="share__icons">
              <Link
                href={shareLinks.whatsapp}
                target="_blank"
                rel="noopener noreferrer"
                className="share__icon"
              >
                <FaWhatsapp />
              </Link>
              <Link
                href={shareLinks.facebook}
                target="_blank"
                rel="noopener noreferrer"
                className="share__icon"
              >
                <FaFacebookF />
              </Link>
              <Link
                href={shareLinks.twitter}
                target="_blank"
                rel="noopener noreferrer"
                className="share__icon"
              >
                <BsTwitterX />
              </Link>
            </div>
            <div className="">
              {copied ? (
                <div className="copy_text_anim">
                  {/* <div className=""> */}
                  <svg
                    width="50px"
                    height="50px"
                    viewBox="0 0 133 133"
                    version="1.1"
                    xmlns="http://www.w3.org/2000/svg"
                    xmlnsXlink="http://www.w3.org/1999/xlink"
                  >
                    <g
                      id="check-group"
                      stroke="none"
                      strokeWidth={1}
                      fill="none"
                      fillRule="evenodd"
                    >
                      <circle
                        id="filled-circle"
                        fill="rgba(2, 104, 16, 0.9)"
                        cx="66.5"
                        cy="66.5"
                        r="54.5"
                      />
                      <circle
                        id="white-circle"
                        fill="#FFFFFF"
                        cx="66.5"
                        cy="66.5"
                        r="55.5"
                      />
                      <circle
                        id="outline"
                        stroke="rgba(2, 104, 16, 0.9)"
                        strokeWidth={4}
                        cx="66.5"
                        cy="66.5"
                        r="54.5"
                      />
                      <polyline
                        id="check"
                        stroke="#FFFFFF"
                        strokeWidth="5.5"
                        points="41 70 56 85 92 49"
                      />
                    </g>
                  </svg>
                  <span>Copied!</span>
                  {/* </div> */}
                </div>
              ) : (
                <div className="link_copycntr">
                  <input className="" type="text" readOnly defaultValue={URL} />
                  <LuCopy onClick={copyToClipboard} />
                </div>
              )}
            </div>
          </div>
        </div>
      </OutsideClickHandler>
    </div>
  );
};

export default ShareModal;
