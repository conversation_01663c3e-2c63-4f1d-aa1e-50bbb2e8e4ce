import React, { useState } from "react";
import { getResults, getFilters } from "../../pages/api/ResultApi";
import { FaAngleDown } from "react-icons/fa6";
const Sort = ({
  setShowFilter,
  showFilter,
  setNewFilterData,
  newFilterData,
  filterSelected,
  setFilterSelected,
  query,
  setNewData,
  setNewTotalCounts,
  classes = "",
}) => {
  // console.log(newFilterData, "filter data")
  const [show, setShow] = useState(false);
  const handleFilter = async (filter, name) => {
    let index = filterSelected[name].indexOf(filter._id);
    if (index > -1) {
      let tempArray = filterSelected[name];
      console.log(tempArray, "before filter");
      const filteredArray = tempArray.filter((item) => item._id === filter._id);
      console.log(filteredArray, "after filter");
      let newFilters = { ...filterSelected, [name]: filteredArray };
      console.log(newFilters, "filters to send");
      const data = await getResults(query, 0, 10, newFilters);
      const filterData = await getFilters(query, newFilters);
      setNewFilterData(filterData);
      console.log(data.count, "=> data count");
      setNewData(data.data);
      setNewTotalCounts(data.totalCounts);
      setFilterSelected(newFilters);
    } else {
      let newFilters = {
        ...filterSelected,
        [name]: [...(filterSelected[name] || []), filter._id], // Ensure it's an array or initialize as empty
      };
      console.log(newFilters, "filters to send");
      const data = await getResults(query, 0, 10, newFilters);
      const filterData = await getFilters(query, newFilters);
      console.log(filterData, "new filter data ui");
      console.log(filterData, "server data");
      setNewFilterData(filterData);
      // console.log(data.count, "sort data")
      // console.log(data,"data from backend after filteration")
      setNewData(data.data);
      setNewTotalCounts(data.totalCounts);
      setFilterSelected(newFilters);
    }
  };
  let handleFilterNumber = async (e, name) => {
    console.log(name, "nam");
    let tempOption = e.target.value;
    if (tempOption == filterSelected[name]) {
      return;
    }

    let newFilters = { ...filterSelected, [name]: parseInt(tempOption) };
    console.log(newFilters, "filters to send");

    const data = await getResults(query, 0, 10, newFilters);
    const filterData = await getFilters(query, newFilters);
    setNewFilterData(filterData);
    console.log(data.count, "=> data count");
    setNewData(data.data);
    setNewTotalCounts(data.totalCounts);
    setFilterSelected(newFilters);
  };

  return (
    <div className={showFilter ? `parent-div-filter ${classes}` : classes}>
      <div className="btn-filter-div">
        <button
          className="save-filter-btn"
          onClick={() => {
            setShowFilter(false);
          }}
          style={{ marginBottom: "30px" }}
        >
          Save Filter
        </button>
      </div>
      <div className="sort-div2">
        <h3 className="sd">Sort by</h3>
        <div className="tag-select-div">
          <select
            className="tag-select"
            value={filterSelected.sortBy}
            onChange={(e) => {
              handleFilterNumber(e, "sortBy");
            }}
          >
            <option className="tag-option" value={0}>
              Relevance
            </option>
            <option className="tag-option" value={-1}>
              Published Date (Newest first)
            </option>
            <option className="tag-option" value={1}>
              Published Date (Oldest first)
            </option>
          </select>
        </div>
      </div>

      <div className="sort-div">
        <h3 className="sd">Filter by Date</h3>
        <div className="tag-select-div">
          <select
            className="tag-select"
            value={filterSelected.dateRange}
            onChange={(e) => {
              handleFilterNumber(e, "dateRange");
            }}
          >
            <option className="tag-option" value={0}>
              All
            </option>
            <option className="tag-option" value={1}>
              Past 24 hours
            </option>
            <option className="tag-option" value={2}>
              Past 7 days
            </option>
            <option className="tag-option" value={3}>
              Past 30 days
            </option>
            <option className="tag-option" value={4}>
              Past 1 year
            </option>
          </select>
        </div>
      </div>
      <div className="author-sort">
        <div className="sort-top">
          <h3 className="filter-head-p sd">
            Filter by Categories
            <span style={{ fontSize: "25px", marginLeft: "5px" }}>{` (${
              newFilterData && (newFilterData?.subcategories?.length ?? 0)
            })`}</span>
          </h3>
          <p className="sort-clear">CLEAR</p>
        </div>
        {newFilterData &&
          newFilterData.subcategories.map((filter, k) => (
            <div key={k}>
              <div className="row-sort">
                <div className="checkbox-filtername">
                  <input
                    type="checkbox"
                    checked={
                      filterSelected.subcategoryIds.includes(filter._id)
                        ? true
                        : false
                    }
                    onChange={() => handleFilter(filter, "subcategoryIds")}
                  />
                  <p className="filter-name-tag">{filter.name}</p>
                </div>
                <p className="filter-qty-tag">{filter.articleCount}</p>
              </div>
            </div>
          ))}
      </div>
      <div className="author-sort">
        <div className="sort-top">
          <h3 className="filter-head-p sd">
            Filter by Tags
            <span style={{ fontSize: "25px", marginLeft: "5px" }}>{` (${
              newFilterData && (newFilterData?.tags?.length ?? 0)
            })`}</span>
            <span
              onClick={() => {
                setShow(!show);
              }}
            >
              <FaAngleDown
                style={{
                  transition: "all 0.3s ease",
                  rotate: show ? "0deg" : "180deg",
                  marginLeft: "5px",
                  cursor: "pointer",
                }}
              />
            </span>
          </h3>

          <p className="sort-clear">CLEAR</p>
        </div>
        <div className={`sort_cntr ${show ? null : "sort_hide"}`}>
          {newFilterData &&
            newFilterData.tags.map((filter, k) => (
              <div key={k}>
                <div className="row-sort">
                  <div className="checkbox-filtername">
                    <input
                      type="checkbox"
                      checked={
                        filterSelected.tagIds.includes(filter._id)
                          ? true
                          : false
                      }
                      onChange={() => handleFilter(filter, "tagIds")}
                    />
                    <p className="filter-name-tag">{filter.name || ""}</p>
                  </div>
                  <p className="filter-qty-tag">{filter.articleCount}</p>
                </div>
              </div>
            ))}
        </div>
      </div>

      <div className="author-sort">
        <div className="sort-top">
          <h3 className="filter-head-p sd">
            Filter by Authors
            <span style={{ fontSize: "25px", marginLeft: "5px" }}>{` (${
              newFilterData && (newFilterData?.writers?.length ?? 0)
            })`}</span>
          </h3>
          <p className="sort-clear">CLEAR</p>
        </div>
        {/* <div className={`sort_cntr ${show ? null : "sort_hide"}`}> */}
        {newFilterData &&
          newFilterData.writers.map((filter, k) => (
            <div key={k}>
              <div className="row-sort">
                <div className="checkbox-filtername">
                  <input
                    type="checkbox"
                    checked={
                      filterSelected.writerIds.includes(filter._id)
                        ? true
                        : false
                    }
                    onChange={() => handleFilter(filter, "writerIds")}
                  />
                  <p className="filter-name-tag">
                    {filter.firstname} {filter.lastname || ""}
                  </p>
                </div>
                <p className="filter-qty-tag">{filter.articleCount}</p>
              </div>
            </div>
          ))}
        {/* </div> */}
      </div>
    </div>
  );
};

export default Sort;
