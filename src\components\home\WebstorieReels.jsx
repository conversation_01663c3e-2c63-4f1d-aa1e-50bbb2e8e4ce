import { useGSAP } from "@gsap/react";
import gsap from "gsap";
import SplitText from "gsap/dist/SplitText";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState } from "react";
import { BsArrowUpRight } from "react-icons/bs";
import { BsArrowUpLeft } from "react-icons/bs";
const stories = [
  {
    id: 1,
    title:
      "Lorem ipsum, dolor sit amet consectetur adipisicing elit.Laudantium fuga incidunt omnis aliquam! Eum, doloribus?",
    profileImg:
      "https://a.storyblok.com/f/133769/250x250/f5452045b8/behance-logo.svg/m/50x50/filters:quality(90)",
    profileName: "Behance",
    storyImg:
      "https://a.storyblok.com/f/133769/3000x2000/2bd9e21f3c/team.jpg/m/2400x1600/filters:quality(90)",
  },
  {
    id: 2,
    title: "Lorem ipsum, dolor sit am adipisicing elit.Lauium fugadunt.",
    profileImg:
      "https://a.storyblok.com/f/133769/x/3982bd5abb/avatar-awwwards.svg/m/50x0/filters:quality(90)",
    profileName: "Awwwards",
    storyImg:
      "https://a.storyblok.com/f/133769/2500x1669/f17a650324/pixelflakes-news-hero.jpg/m/2400x1602/filters:quality(90)",
  },
  {
    id: 3,
    title:
      "Lorem ipsum, dolor sit amet consectetur lit.Laudantium fuga ialiquam! Eum, doloribus?",
    profileImg:
      "https://a.storyblok.com/f/133769/1024x1024/a42fc53515/avatar-ddd.jpg/m/50x50/filters:quality(90)",
    profileName: "Dutch Digital Design",
    storyImg:
      "https://a.storyblok.com/f/133769/3000x2000/2bd9e21f3c/team.jpg/m/2400x1600/filters:quality(90)",
  },
  {
    id: 4,
    title:
      "Lorem ipsum, dolor sit amet consicing elit.Laudantium fuga incidunt.",
    profileImg:
      "https://a.storyblok.com/f/133769/2500x1563/84b9175883/exo-ape-silver-lovie.jpg/m/2400x1500/filters:quality(90)",
    profileName: "Winners Gallery",
    storyImg:
      "https://a.storyblok.com/f/133769/2500x1339/a0431054ca/columbia-pictures-lady.jpg/m/2400x1285/filters:quality(90)",
  },
  {
    id: 5,
    title:
      "dolor sit amet consectetur adipisicing elit.Laudantium fuga incidunt.",
    profileImg:
      "https://a.storyblok.com/f/133769/1000x1000/6289e7641f/lovie-logo.png/m/50x50/filters:quality(90)",
    profileName: "The Lovie Awards",
    storyImg:
      "https://a.storyblok.com/f/133769/2500x1563/84b9175883/exo-ape-silver-lovie.jpg/m/2400x1500/filters:quality(90)",
  },
  {
    id: 6,
    title: "Lorem ipslor nsectetur adipisicing elit.Laudantium fuga incidunt.",
    profileImg:
      "https://a.storyblok.com/f/133769/1000x1000/6289e7641f/lovie-logo.png/m/50x50/filters:quality(90)",
    profileName: "The Lovie Awards",
    storyImg:
      "https://a.storyblok.com/f/133769/2500x1339/a0431054ca/columbia-pictures-lady.jpg/m/2400x1285/filters:quality(90)",
  },
];
{
  // titleRef = document.querySelectorAll(".story_content_title-cntr");
  // mediaRef = document.querySelectorAll(".story_imgs");
  // mediaRef,
  // { scale: scale, rotate: e, x: l },
  // {
  // scale: 1,
  // rotate: -100,
  // x: 0,
  // duration: 1,
  // ease: "easeInOut",
  // };
  // gsap.fromTo(
  // titleRef,
  // {
  //   rotate: e,
  //   yPercent: n,
  //   opacity: 0,
  //   transformOrigin: "left",
  // },
  // {
  //   rotate: 0,
  //   yPercent: 0,
  //   opacity: 1,
  //   stagger: o,
  //   delay: 0.2,
  //   duration: 1,
  //   ease: "easeOut",
  // }
  // );
}
gsap.registerPlugin(SplitText);
const WebstorieReels = () => {
  // const gotoSlide = () => {
  //   if (isTweening) return;
  //   isTweening = true;
  //   const slides = slidesArray[0];
  //   //   console.log(slides);
  //   const currentSlide = slides[currentIndex];
  //   if (slides[currentIndex + 1]) {
  //     currentIndex++;
  //     nextSlide = slides[currentIndex];
  //   } else {
  //     // go to the fisrt slide
  //     currentIndex = 0;
  //     nextSlide = slides[currentIndex];
  //   }
  //   gsap.set(nextSlide, {
  //     zIndex: 2,
  //     clipPath: "polygon(125% 0%, 100% 0%, 100% 100%, 100% 100%)",
  //     scale: 1.8,
  //   });
  //   //   gsap.set(nextIndicie, {
  //   //     width: "0%",
  //   //   });
  //   gsap.set(currentSlide, { zIndex: 1 });
  //   //   gsap.set(currentIndicie);
  //   mediaRef,
  //     { scale: scale, rotate: e, x: l },
  //   {
  //     scale: 1,
  //     rotate: -100,
  //     x: 0,
  //     duration: 1,
  //     ease: "easeInOut",
  //   };

  //   gsap.fromTo(
  //     titleRef,
  //     {
  //       rotate: e,
  //       yPercent: n,
  //       opacity: 0,
  //       transformOrigin: "left",
  //     },
  //     {
  //       rotate: 0,
  //       yPercent: 0,
  //       opacity: 1,
  //       stagger: o,
  //       delay: 0.2,
  //       duration: 1,
  //       ease: "easeOut",
  //     }
  //   );
  //   gsap.to(nextSlide, {
  //     duration: 1,
  //     ease: "easeInOut",
  //     clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
  //     scale: 1,
  //     onComplete: () => {
  //       isTweening = false;
  //       gsap.set(currentSlide, { clearProps: "z-index" });
  //       timer.restart(true);
  //     },
  //   });
  // };

  const [direction, setDirection] = useState(-1);
  const [covered_slide, set_covered_slide] = useState(0);

  var scale = 10; // Example value, replace with actual value
  var directions = true; // Example value, replace with actual value
  var x = 300; // Example value, replace with actual value

  var t = scale;
  var e = directions ? -7 : 7;
  var o = directions ? -0.1 : 0.1;
  var n = directions ? -100 : 100;
  var l = directions ? -x / 3 : x / 3;

  let mediaRef;
  let titleRef;

  let cursor = (e) => {
    console.log(e.clientX, " -> e");
    const reel_cursor = document.querySelector(".reel-cursor");
    gsap.to(reel_cursor, {
      top: e.clientY,
      left: e.clientX,
    });
    if (e.clientX < window.innerWidth / 2) {
      direction !== 0 && setDirection(0);
    } else {
      direction !== 1 && setDirection(1);
    }
  };
  let animation = (e) => {
    !isTweening &&
      (e.clientX < window.innerWidth / 2 ? gotoprev() : gotonext());
  };

  useEffect(() => {
    window.addEventListener("mousemove", cursor);
    window.addEventListener("click", animation);

    //   trying new
    const sliders = gsap.utils.toArray(".story_content");
    const IndicesAll = gsap.utils.toArray(".indices");
    let slidesArray = sliders.map((slider) =>
      gsap.utils.toArray(".story_image_contentCntr", slider)
    );
    console.log(slidesArray, "opop");

    const IndiciesArray = IndicesAll.map((indic) =>
      gsap.utils.toArray(".index", indic)
    );
    const next = document.getElementsByClassName(".nextCntr");
    const prev = document.getElementsByClassName(".prevCntr");
    let currentIndex = 0;
    let currentIndiceIndex = 0;
    let timer;
    var currentSlide = 0;
    var dir = 0;
    var nextSlide;
    var nextIndicie;

    //
    // Assuming you have access to the necessary variables directly

    //
    // Element references
    titleRef = document.querySelectorAll(".story_content_title-cntr");
    mediaRef = document.querySelectorAll(".story_imgs");
    var authorRef = document.querySelector(".author");
    var yourElement = document.querySelectorAll(".story_content_title-cntr h1");
    var split = new SplitText(yourElement, {
      type: "chars,lines,words",
      classline: "line",
    });
  }, []);

  let curr_slide = 0;
  let isTweening = false;
  let gotofirst = () => {
    let slidesArray = document.getElementsByClassName(
      "story_image_contentCntr"
    );
    let last_slide = slidesArray[slidesArray.length - 1];
    gsap.set(last_slide, { zIndex: 2 });

    gsap.set(slidesArray[0], {
      zIndex: 3,
      clipPath: "polygon(125% 0%, 100% 0%, 100% 100%, 100% 100%)",
      scale: 1.8,
    });
    gsap.to(slidesArray[0], {
      duration: 1,
      clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
      scale: 1,
      ease: "power2.inOut",
      onComplete: () => {
        gsap.set(last_slide, { zIndex: 1 });
        curr_slide = 0;
        set_covered_slide(0);

        isTweening = false;
      },
    });
    return;
  };

  let gotonext = () => {
    console.log(covered_slide, "=> covered_slide");
    isTweening = true;

    let slidesArray = document.getElementsByClassName(
      "story_image_contentCntr"
    );

    if (curr_slide > slidesArray.length - 2) {
      gotofirst();
      return;
    }

    let next_slide = curr_slide + 1;
    let nextSlide = slidesArray[next_slide];
    gsap.set(slidesArray[curr_slide], { zIndex: 2 });
    gsap.set(nextSlide, {
      zIndex: 3,
      clipPath: "polygon(125% 0%, 100% 0%, 100% 100%, 100% 100%)",
      scale: 1.8,
    });
    gsap.to(nextSlide, {
      duration: 1,
      clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
      scale: 1,
      ease: "power2.inOut",
      onComplete: () => {
        // alert("uo")
        isTweening = false;

        gsap.set(slidesArray[curr_slide], { zIndex: 1 });
        set_covered_slide(curr_slide + 1);
        curr_slide += 1;

        // timer.restart(true);
      },
    });
    console.log(slidesArray.length - 2, "length");
  };

  let gotolast = () => {
    isTweening = true;
    let slidesArray = document.getElementsByClassName(
      "story_image_contentCntr"
    );

    let last_slide = slidesArray.length - 1;
    let lastSlide = slidesArray[last_slide];

    gsap.set(slidesArray[0], { zIndex: 2 });
    gsap.set(lastSlide, {
      zIndex: 3,
      clipPath: "polygon(0% 0%, -25% 0%, 0% 100%, 0% 100%)",
      scale: 1.8,
    });
    gsap.to(lastSlide, {
      duration: 1,
      clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
      scale: 1,
      ease: "power2.inOut",

      onComplete: () => {
        isTweening = false;

        gsap.set(slidesArray[0], { zIndex: 1 });
        curr_slide = last_slide;
        set_covered_slide(last_slide);
      },
    });
  };

  let gotoprev = (e) => {
    isTweening = true;
    let slidesArray = document.getElementsByClassName(
      "story_image_contentCntr"
    );

    if (curr_slide == 0) {
      gotolast();
      return;
    }
    console.log("curr_slide=>", curr_slide);
    let prev_slide = curr_slide - 1;
    let prevSlide = slidesArray[prev_slide];
    gsap.set(slidesArray[curr_slide], { zIndex: 2 });
    gsap.set(prevSlide, {
      zIndex: 3,
      clipPath: "polygon(0% 0%, -25% 0%, 0% 100%, 0% 100%)",
      scale: 1.8,
    });

    gsap.to(prevSlide, {
      duration: 1,
      clipPath: "polygon(0% 0%, 100% 0%, 100% 100%, 0% 100%)",
      scale: 1,
      ease: "power2.inOut",
      onComplete: () => {
        isTweening = false;

        gsap.set(slidesArray[curr_slide], { zIndex: 1 });
        set_covered_slide(curr_slide - 1);

        curr_slide -= 1;
      },
    });
  };

  return (
    <>
      <div className="webstorieReels">
        <div
          className="reel-cursor"
          style={{ transform: "translate(-50%,-50%)" }}
        >
          <p>{direction ? "Next" : "Prev"}</p>
        </div>
        <div className="prevCntr"></div>
        <div className="nextCntr"></div>
        <div className="story_content">
          <div className="indices">
            {stories.map((inde, index) => {
              return (
                <div
                  key={`indices_item_${index}`}
                  className="index"
                  style={{
                    backgroundColor: "white",
                    opacity: covered_slide >= index ? "1" : ".4",
                  }}
                >
                  <div className="index-highlight"></div>
                </div>
              );
            })}
          </div>
          {stories.map((Storys, index) => {
            return (
              <div
                className="story_image_contentCntr"
                key={index}
                style={
                  index !== 0
                    ? {
                        clipPath:
                          "polygon(100% 0%, 100% 0%, 100% 100%, 100% 100%)",
                      }
                    : {
                        clipPath: "polygon(100% 0%, 0% 0%, 0% 100%, 100% 100%)",
                      }
                }
              >
                {/* Image and Text Container */}
                <div className="story_reelstext_cntr">
                  {/* Text content for each story */}
                  <div className="story_content_row bottom">
                    <div className="story_content_title">
                      <div className="story_content_title-cntr">
                        <h1 style={{ fontSize: "30px" }}>{index}</h1>
                        <h1>{Storys.title}</h1>
                      </div>
                    </div>
                    <div className="story_content_row_credit">
                      <Link href="" alt target="_blank">
                        <small>Photo Credit: NASA</small>
                      </Link>
                    </div>
                  </div>
                </div>

                <div key={index} className="story_imgs">
                  <Image
                    src={Storys.storyImg}
                    alt=""
                    fill
                    sizes="(max-width: 1024px) 1280px, (max-width: 1280px) 1920px, 2400px)"
                  />
                </div>
              </div>
            );
          })}
        </div>
        <div className="webstorieReels_bottom">
          <div className="prev_btn webslidebtn">
            <div className="cylinderical_bg">
              <div className="cylinderical_bg_curved">
                <div className="reel_profile_next">
                  <div className="profile-icon_next">
                    <div className="circle_icon">
                      <BsArrowUpLeft />
                    </div>
                    <Image
                      // fill
                      src="https://a.storyblok.com/f/133769/250x250/f5452045b8/behance-logo.svg/m/50x50/filters:quality(90)"
                      alt=""
                      width="250"
                      height="250"
                      loading="lazy"
                      sizes="(max-width: 1024px) 30px, (max-width: 1280px) 36px, 50px"
                    />
                  </div>
                </div>
                {/* <h1>Prev Slide</h1> */}
                <div class="title-mask">
                  <div class="title-line">
                    Exo Ape joins the <br />
                    Exo Ape joins the
                  </div>
                </div>
              </div>
              <div className="cylinderical_bg_inner">
                <h1>Prev</h1>
              </div>
            </div>
          </div>
          <div className="next_btn webslidebtn">
            <div className="cylinderical_bg">
              <div className="cylinderical_bg_inner _next">
                <h1>Next</h1>
              </div>
              <div className="cylinderical_bg_curved">
                <div class="title-mask">
                  <div class="title-line">
                    Exo Ape joins the <br />
                    Exo Ape joins the
                  </div>
                </div>
                <div className="reel_profile_next">
                  <div className="profile-icon_next">
                    <div className="circle_icon">
                      <BsArrowUpRight />
                    </div>
                    <Image
                      // fill
                      src="https://a.storyblok.com/f/133769/250x250/f5452045b8/behance-logo.svg/m/50x50/filters:quality(90)"
                      alt=""
                      width="250"
                      height="250"
                      loading="lazy"
                      sizes="(max-width: 1024px) 30px, (max-width: 1280px) 36px, 50px"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default WebstorieReels;
