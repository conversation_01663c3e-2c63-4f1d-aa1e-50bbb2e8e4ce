import React, { useState, useEffect } from "react";
import <PERSON> from "next/link";
import Image from "next/image";
import ScrollTrigger from "gsap/dist/ScrollTrigger";
import gsap from "gsap";
gsap.registerPlugin(ScrollTrigger);
const StaticAds = ({ adUnits, classAdd = null }) => {
  const [currentAd, setCurrentAd] = useState(null);

  const updateAdUnit = () => {
    setTimeout(() => {
      ScrollTrigger.refresh();
      ScrollTrigger.sort();
    }, 300);

    const windowWidth = window.innerWidth;

    // Find the ad unit that matches the current window width
    const matchedAd = adUnits.find(
      (unit) => windowWidth >= unit.minWidth && windowWidth <= unit.maxWidth
    );

    setCurrentAd(matchedAd || null);
  };

  useEffect(() => {
    updateAdUnit(); // Set initial ad unit
    window.addEventListener("resize", updateAdUnit); // Update on resize

    return () => {
      window.removeEventListener("resize", updateAdUnit); // Cleanup on unmount
    };
  }, [adUnits]);

  if (!currentAd) {
    return null; // Render nothing if no matching ad is found
  }

  return (
    <div
    // style={{
    //   width: "100%",
    //   // height: "25vh",
    //   overflow: "hidden",
    //   position: "relative",
    //   padding: "30px 0",
    // }}
    >
      <Link
        href="https://www.rpsg.in/"
        target="_blank"
        className={`ad-parent-dynamic ad-text ${classAdd}`}
      >
        <Image
          src={currentAd.adImagePath}
          alt="dynamic-ad"
          width={currentAd.adSize[0]}
          height={currentAd.adSize[1]}
          style={{ objectFit: "cover" }}
        />
      </Link>
      {/* <Link
        href={""}
        style={{
          width: "100%",
          height: "100%",
        }}
      >
        <img
          style={{
            width: "100%",
            height: "100%",
            objectFit: "none",
            objectPosition: "center",
          }}
          src={currentAd.adImagePath}

          // src="https://images.unsplash.com/photo-1508138221679-760a23a2285b?w=500&auto=format&fit=crop&q=60&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OHx8cmFuZG9tfGVufDB8fDB8fHww"
          // alt=""
        />
      </Link> */}
    </div>
  );
};

export default StaticAds;
