import React from "react";
import Link from "next/link";

const Breadcrumb = ({ data }) => {
  if (!data || data.length === 0) return null;

  return (
    <div className="breadcrumb">
      <p style={{ fontWeight: 300 }}>
        {data.map((item, index) => (
          <React.Fragment key={`breadcrumbs-item-${index}`}>
            <Link href={item?.slug ?? "#"}>{item?.name ?? ""}</Link>
            {index < data.length - 1 && <span>&nbsp; &gt; &nbsp;</span>}
          </React.Fragment>
        ))}
      </p>
    </div>
  );
};

export default Breadcrumb;
